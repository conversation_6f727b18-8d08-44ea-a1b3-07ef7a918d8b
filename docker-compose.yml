version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_ERP_API=${NEXT_PUBLIC_ERP_API}
        - NEXT_PUBLIC_CMS_API=${NEXT_PUBLIC_CMS_API}
        - NEXT_PUBLIC_ASSET_URL=${NEXT_PUBLIC_ASSET_URL}
        - NEXT_PUBLIC_GA_ID=${NEXT_PUBLIC_GA_ID}
        - NEXT_PUBLIC_DIRECTUS_API=${NEXT_PUBLIC_DIRECTUS_API}
        - NEXT_PUBLIC_CMS_URL=${NEXT_PUBLIC_CMS_URL}
    container_name: vns-web-v2
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
