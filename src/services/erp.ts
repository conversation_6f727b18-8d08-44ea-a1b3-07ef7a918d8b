'use server';
import { Department } from '@/types/department';
import { Job } from '@/types/job';

const ERP_API = process.env.NEXT_PUBLIC_ERP_API;
async function fetchData<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T | undefined> {
  const url = `${ERP_API}${endpoint}`;

  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      console.log(`Failed to fetch data from ${url}`);
      return {} as T;
    }

    return response.json() as Promise<T>;
  } catch (error) {
    console.log(error);
    console.log(`Failed to fetch data from ${url}`);
  }
}

// Fetch all jobs
export async function getAllOpeningJobs(): Promise<{ data: Job[] } | undefined> {
  const endpoint =
    '/api/resource/Job Opening?fields=["name","job_title","status","posted_on","closes_on","closed_on","company","department","employment_type","location","staffing_plan","planned_vacancies","job_requisition","vacancies","publish","route","publish_applications_received","job_application_route","description","currency","lower_range","upper_range","salary_per","publish_salary_range","designation"]&limit_start=0&limit_page_length=999&order_by=posted_on desc&filters=[["publish", "=", "1"], ["status", "=", "Open"]]';
  return fetchData<{ data: Job[] }>(endpoint);
}

//Fetch all departments
export async function getAllDepartments(): Promise<{ data: Department[] } | undefined> {
  const endpoint = '/api/resource/Department?fields=["name","department_name"]';
  return fetchData<{ data: Department[] }>(endpoint);
}

// Fetch a single post by name
export async function getJobByName(name: string): Promise<{ data: Job[] } | undefined> {
  return fetchData<{ data: Job[] }>(
    `/api/resource/Job Opening?fields=["*"]&filters=[["route", "=", "jobs/${name}"]]`
  );
}

// Submit job application
export async function submitJobApplication(data: any): Promise<any> {
  const formData = new FormData();
  formData.append(
    'data',
    JSON.stringify({
      ...data,
      doctype: 'Job Applicant',
      web_form_name: 'job-application-submit',
    })
  );
  formData.append('web_form', 'job-application-submit');
  formData.append('for_payment', 'false');
  formData.append('cmd', 'frappe.website.doctype.web_form.web_form.accept');

  const response = await fetch(`${ERP_API}`, {
    method: 'POST',
    headers: {
      accept: 'application/json, text/javascript, */*; q=0.01',
    },
    body: formData,
  });

  if (!response.ok) {
    console.log('Failed to submit job application');
  }

  return response.json();
}
// Upload file
export async function uploadFile(file: File): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('is_private', '0');
  formData.append('folder', 'Home');

  const response = await fetch(`/api/method/upload_file`, {
    method: 'POST',
    body: formData,
  });
  if (!response.ok) {
    console.log('Failed to upload file');
  }
  return response.json();
}
