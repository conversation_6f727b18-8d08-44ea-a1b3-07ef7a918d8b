// 'use server';
import { getDirectusClient } from '@/utils/directus';
import { getAssetUrl } from '@/utils/url/asset';
import { isEmptyObject } from '@/utils/func';
import { type Category, type Content, defaultPost, type Post } from '@/types/resources';
import { DATA_MODEL } from '@/utils/constants/directus';
import { aggregate, createItem, readItem, readItems, readSingleton } from '@directus/sdk';
import moment from 'moment';
import { TAB } from '@/utils/constants/resources';

export const getCategory = async (): Promise<Category[]> => {
  try {
    const directus = await getDirectusClient();
    const records = await directus.request(
      readItems(DATA_MODEL.CATEGORY, {
        fields: ['*'],
        filter: getStatusFilter(),
      })
    );
    return mapRecordToCategory(records);
  } catch (error) {
    console.error('There are some error on getCategory: ', error);
    return [];
  }
};

export const getResourcesArticles = async (
  limit: number,
  currentPage: number,
  ignoreIds: number[],
  filter: any[] = []
): Promise<Post[]> => {
  try {
    const directus = await getDirectusClient();
    const defaultFilter = {
      _and: [
        getIgnoreIdsFilter(ignoreIds),
        getStatusFilter(),
        { type: { value: { _eq: 'articles' } } },
      ],
    };
    const mergeFilter = !filter.length
      ? defaultFilter
      : { _and: [defaultFilter, ...filter] };
    const records = await directus.request(
      readItems(DATA_MODEL.RESOURCES, {
        fields: ['*', { type: ['label'] }],
        sort: ['-date_created'],
        page: currentPage,
        limit,
        filter: mergeFilter,
      })
    );
    return mapRecordToPost(records);
  } catch (error) {
    console.error('There are some error on getResourcesArticles: ', error);
    return [];
  }
};

export const getResourcesVideos = async (
  limit: number,
  currentPage: number,
  ignoreIds: number[],
  filter: any = {}
): Promise<Post[]> => {
  try {
    const directus = await getDirectusClient();
    const defaultFilter = {
      _and: [
        getIgnoreIdsFilter(ignoreIds),
        getStatusFilter(),
        { type: { value: { _eq: 'videos' } } },
      ],
    };

    const mergeFilter = isEmptyObject(filter)
      ? defaultFilter
      : { _and: [defaultFilter, filter] };
    const records = await directus.request(
      readItems(DATA_MODEL.RESOURCES, {
        fields: ['*', { type: ['label'] }],
        sort: ['-date_created'],
        page: currentPage,
        limit,
        filter: mergeFilter,
      })
    );
    return mapRecordToPost(records);
  } catch (error) {
    console.error('There are some error on getResourcesVideos: ', error);
    return [];
  }
};

export const getArticleByID = async (id: string, fields: any = []): Promise<Post> => {
  try {
    const directus = await getDirectusClient();
    const records = await directus.request(
      readItems(DATA_MODEL.RESOURCES, {
        fields: !isEmptyObject(fields)
          ? fields
          : [
              '*',
              {
                content: [
                  'main_section',
                  // 'right_sections',
                  'id',
                  { right_sections: ['content', 'id'] },
                ],
              },
            ],
        sort: ['-date_created'],
        page: 1,
        limit: 1,
        filter: { _and: [{ slug: { _eq: id } }, getStatusFilter()] },
      })
    );
    return mapRecordToPost(records)[0] || {};
  } catch (error) {
    console.error('There are some error on getArticleByID: ', error);
    return defaultPost;
  }
};

export const getHighlightSectionByID = async (
  id: number,
  fields: any = []
): Promise<any> => {
  try {
    const directus = await getDirectusClient();
    const records = await directus.request(
      readItems('highlight_section', {
        fields: !isEmptyObject(fields) ? fields : ['*'],
        sort: ['-date_created'],
        page: 1,
        limit: 15,
        filter: { _and: [{ right_section_id: { _eq: id } }, getStatusFilter()] },
      })
    );
    return records;
  } catch (error) {
    console.error('There are some error on getArticleByID: ', error);
    return [];
  }
};

export const getRelatedArticles = async (
  tags: string[],
  ignoreIds?: number[],
  limit?: number,
  page?: number
): Promise<Post[]> => {
  try {
    const directus = await getDirectusClient();
    const records = await directus.request(
      readItems(DATA_MODEL.RESOURCES, {
        fields: ['*', { type: ['label'] }],
        sort: ['-date_created'],
        page: page || 1,
        limit: limit || 4,
        filter: {
          _and: [
            getIgnoreIdsFilter(ignoreIds || []),
            {
              _or: (tags || []).map((tag: string) => ({
                tag_values: { _contains: tag },
              })),
            },
            { type: { value: { _eq: TAB.ARTICLE } } },
            getStatusFilter(),
          ],
        },
      })
    );
    return mapRecordToPost(records) || {};
  } catch (error) {
    console.error('There are some error on getPosts: ', error);
    return [];
  }
};

export const getLiveStreamEvent = async (filter: any = {}): Promise<Post> => {
  try {
    const liveStreamFilter = {
      _and: [
        {
          _or: [
            {
              _and: [
                { show_as_live_streaming_carousel: true },
                { from_date: { _lte: moment().format('YYYY-MM-DDTHH:mm:ss') } },
                { to_date: { _gte: moment().format('YYYY-MM-DDTHH:mm:ss') } },
              ],
            },
            { label_id: { name: { _eq: 'Live' } } },
          ],
        },
        getStatusFilter(),
      ],
    };
    const directus = await getDirectusClient();
    const mergeFilter = isEmptyObject(filter)
      ? liveStreamFilter
      : { _and: [liveStreamFilter, filter] };
    const records = await directus.request(
      readItems(DATA_MODEL.POST, {
        fields: ['*', { label_id: ['name'] }, { category_id: ['name', 'color'] }],
        sort: ['-date_created'],
        page: 1,
        limit: 1,
        filter: mergeFilter,
      })
    );
    return mapRecordToPost(records)?.[0];
  } catch (error) {
    console.error('There are some error on getPosts: ', error);
    return defaultPost;
  }
};

export const getTotalArticle = async (
  ignoreIds: number[],
  filter: any[] = [],
  keyword: string = ''
): Promise<number> => {
  try {
    const directus = await getDirectusClient();
    const defaultFilter = {
      _and: [getIgnoreIdsFilter(ignoreIds), getStatusFilter()],
    };
    const mergeFilter = !filter.length
      ? defaultFilter
      : { _and: [defaultFilter, ...filter] };
    const totalCount = await directus.request(
      aggregate(DATA_MODEL.RESOURCES, {
        aggregate: { count: '*' },
        query: {
          filter: mergeFilter,
          search: keyword,
        },
      })
    );
    return (totalCount[0]?.count || 0) as number;
  } catch (error) {
    console.error('There are some error on getPosts: ', error);
    return 0;
  }
};

const mapRecordToCategory = (data: Record<string, any>[] = []): Category[] => {
  return data.map((record: Record<string, any>) => {
    return {
      name: record.name,
      color: record.color,
    };
  });
};

const mapContentSection = (content: any) => {
  if (!content) return [];

  return content.map((section: Content) => ({
    main_section: section?.main_section || '',
    right_section: section?.right_section || '',
    right_sections: section?.right_sections || [],
  }));
};

const mapRecordToPost = (data: Record<string, any>[]): Post[] => {
  return data.map((record: Record<string, any>) => {
    return {
      thumbnail: record.thumbnail,
      title: record.title,
      content: mapContentSection(record.content),
      id: record.id,
      slug: record.slug,
      type: record.type,
      status: record.status,
      description: record.description,
      meta_title: '',
      meta_description: '',
      date_created: record.date_created,
      duration: record.duration,
      tag: record.tag,
      video: record.video || '',
    };
  });
};

const getStatusFilter = () => {
  return { status: { _eq: 'Published' } };
};

const getIgnoreIdsFilter = (ignoreIds: number[] = []) => {
  const ignoreIdsWithValues = ignoreIds.filter((id) => id);
  return ignoreIdsWithValues.length ? { id: { _nin: ignoreIdsWithValues } } : {};
};

export const searchPosts = async (
  keyword: string,
  ignoreIds: number[],
  page: number = 1,
  limit: number
): Promise<Post[]> => {
  try {
    const directus = await getDirectusClient();
    const records = await directus.request(
      readItems(DATA_MODEL.POST, {
        search: keyword,
        filter: { _and: [getIgnoreIdsFilter(ignoreIds), getStatusFilter()] },
        fields: ['*', { label_id: ['name'] }, { category_id: ['name', 'color'] }],
        limit,
        page,
      })
    );
    return mapRecordToPost(records);
  } catch (error) {
    console.error('There are some error on searchPosts: ', error);
    return [];
  }
};

// POST method
export const createContactSubmission = async (data: any) => {
  try {
    const directus = await getDirectusClient();
    const submissions = await directus.request(createItem('contact_records', data));
    return {
      success: true,
      data: submissions,
    };
  } catch (error) {
    console.error('Error creating submission:', error);
    return {
      success: false,
      error: 'Failed to submit form',
    };
  }
};

export const createNewsletterSubscription = async ({
  email,
  agreeToPrivacyPolicy,
}: {
  email: string;
  agreeToPrivacyPolicy: boolean;
}) => {
  try {
    const directus = await getDirectusClient();
    const subscription = await directus.request(
      createItem('newsletter_subscription_records', { email, agreeToPrivacyPolicy })
    );
    return {
      success: true,
      data: subscription,
    };
  } catch (error) {
    console.error('Error creating newsletter subscription:', error);
    return {
      success: false,
      error: 'Failed to subscribe to newsletter',
    };
  }
};
export const getCarouselGroups = async (name?: string) => {
  try {
    const directus = await getDirectusClient();
    const response = await directus.request(
      readItems('carousel_groups', {
        fields: [
          'id',
          'name',
          {
            items: [
              'id',
              'sort',
              {
                item: [
                  'id',
                  'click_url',
                  { asset: ['*'] },
                  {
                    translations: [
                      'id',
                      'title',
                      'subtitle',
                      'description',
                      'languages_code',
                      'button_text',
                    ],
                  },
                ],
              },
            ],
          },
        ],
        ...(name
          ? {
              filter: {
                name: {
                  _eq: name,
                },
              },
            }
          : {}),
      })
    );
    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error('Error fetching carousel groups:', error);
    return {
      success: false,
      error: 'Failed to fetch carousel groups',
    };
  }
};

/**
 * Fetches SEO metadata for a specific page from the Directus CMS
 *
 * This function retrieves title, description, keywords, and image data
 * for a given page ID to be used in generating proper metadata tags
 * for search engines and social media sharing.
 *
 * @param id - The unique identifier for the page in the 'seos' collection
 * @returns An object containing success status and either the metadata or an error message
 */
export const getMetadata = async (id: string) => {
  try {
    const directus = await getDirectusClient();
    const response = await directus.request(
      readItem('seos', id, {
        fields: ['title', 'description', 'keywords', { image: ['filename_disk'] }],
      })
    );
    const { title, description, keywords, image } = response;
    return {
      title,
      description,
      keywords,
      openGraph: {
        title,
        description,
        images: image ? [getAssetUrl(image?.filename_disk)] : undefined,
      },
    };
  } catch (error) {
    console.error('Error fetching metadata:', error);
    return {};
  }
};

export const getMedia = async (): Promise<Record<string, any>> => {
  try {
    const directus = await getDirectusClient();
    const response = await directus.request(
      readItems('medias', { fields: ['*', { translations: ['*'] }] })
    );
    return response;
  } catch (error) {
    console.error('Error fetching media:', error);
    return [];
  }
};

/**
 * Fetches dynamic sections from the Directus CMS based on the provided name and optional item fields.
 *
 * @param name - The name of the dynamic section to fetch. If not provided, all sections matching the status filter will be fetched.
 * @param itemFields - Optional fields to include for each item in the section. Defaults to all fields if not specified.
 * @returns A promise that resolves to an object containing:
 *   - `success`: A boolean indicating whether the operation was successful.
 *   - `data`: An array of items from the fetched section if successful.
 *   - `error`: An error message if the operation failed.
 *
 * @throws Will log an error to the console if the fetch operation fails.
 */
export const getDynamicSections = async (
  name: string,
  itemFields?: any,
  pFilter?: any[]
) => {
  try {
    const directus = await getDirectusClient();
    const filter = name
      ? { _and: [{ name: { _eq: name } }, getStatusFilter(), ...(pFilter ?? [])] }
      : getStatusFilter();
    const response = await directus.request(
      readItems('sections', {
        fields: [
          '*',
          {
            items: [
              '*',
              {
                item: itemFields || ['*'],
              },
            ],
          },
        ],
        filter,
      })
    );
    const items = response?.[0]?.items || [];
    return {
      success: true,
      data: items,
    };
  } catch (error) {
    console.error('Error fetching dynamic section with name:', name, error);
    return {
      success: false,
      error: `Failed to fetch dynamic section with name ${name}`,
    };
  }
};

export const getPolicy = async (): Promise<{ content: string }> => {
  try {
    const directus = await getDirectusClient();
    const response = await directus.request(readSingleton('policy'));
    return response as { content: string };
  } catch (error) {
    console.error('Error fetching policy:', error);
    return { content: '' };
  }
};

export const getPartners = async () => {
  try {
    const directus = await getDirectusClient();
    const response = await directus.request(
      readItems('partners', {
        fields: ['*', { asset: ['filename_disk'] }],
        filter: getStatusFilter(),
      })
    );
    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error('Error fetching partners:', error);
    return {
      success: false,
      error: 'Failed to fetch partners',
    };
  }
};

export const getMetadataWithTag = async (id: string) => {
  try {
    const directus = await getDirectusClient();
    const response = await directus.request(
      readItem('tag_seos', id, {
        fields: ['title', 'description', 'keywords', { image: ['filename_disk'] }],
      })
    );

    const { title, description, keywords, image } = response;
    return {
      title,
      description,
      keywords,
      openGraph: {
        title,
        description,
        images: image ? [getAssetUrl(image?.filename_disk)] : undefined,
      },
    };
  } catch (error) {
    console.error('Error fetching metadata tag seos:', error);
    const defaultMetaData = await getMetadata('resources-search');
    return defaultMetaData;
  }
};
