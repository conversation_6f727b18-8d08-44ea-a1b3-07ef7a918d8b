import { Contact } from '@/types/contact';

async function fetchData<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${endpoint}`;

  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch data from ${url}`);
    }

    return response.json() as Promise<T>;
  } catch (error) {
    console.error(error);
    throw new Error(`Failed to fetch data from ${url}`);
  }
}

export const processNewsletter = async (email: string) => {
  const endpoint = '/api/news-letter';
  const data = {
    email,
  };
  return fetchData<{ email: string }>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};
