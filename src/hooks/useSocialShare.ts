export type SocialPlatform = 'facebook' | 'twitter' | 'linkedin' | 'email' | 'copy';

export interface ShareOptions {
  url: string;
  title?: string;
  description?: string;
  hashtags?: string[];
}

// Kiểm tra nếu đang chạy trên client-side
const isClient = typeof window !== 'undefined';

/**
 * Share link lên Facebook
 */
export const shareToFacebook = (options: ShareOptions): void => {
  if (!isClient) return;

  const { url } = options;
  const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
  openPopup(shareUrl, 'Facebook Share');
};

export const shareToTwitter = (options: ShareOptions): void => {
  if (!isClient) return;

  const { url, title, hashtags } = options;
  let shareUrl = `https://x.com/intent/tweet?url=${encodeURIComponent(url)}`;

  if (title) {
    shareUrl += `&text=${encodeURIComponent(title)}`;
  }

  if (hashtags && hashtags.length > 0) {
    shareUrl += `&hashtags=${encodeURIComponent(hashtags.join(','))}`;
  }

  openPopup(shareUrl, 'Twitter Share');
};

export const shareToLinkedIn = (options: ShareOptions): void => {
  if (!isClient) return;

  const { url, title, description } = options;
  let shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;

  if (title) {
    shareUrl += `&title=${encodeURIComponent(title)}`;
  }

  if (description) {
    shareUrl += `&summary=${encodeURIComponent(description)}`;
  }

  openPopup(shareUrl, 'LinkedIn Share');
};

export const shareToEmail = (options: ShareOptions): void => {
  if (!isClient) return;

  const { url, title, description } = options;
  let subject = title || 'Shared Link';
  let body = `Check out this link: ${url}`;

  if (description) {
    body += `\n\n${description}`;
  }

  const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  window.location.href = mailtoUrl;
};

export const copyToClipboard = async (options: ShareOptions): Promise<boolean> => {
  if (!isClient) return false;

  try {
    await navigator.clipboard.writeText(options.url);
    return true;
  } catch (err) {
    // Fallback for old browsers or if clipboard API fails
    try {
      const textArea = document.createElement('textarea');
      textArea.value = options.url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (fallbackErr) {
      console.error('Copy to clipboard failed:', fallbackErr);
      return false;
    }
  }
};

const openPopup = (url: string, title: string): void => {
  if (!isClient) return;

  const popup = window.open(
    url,
    title,
    'width=800,height=600,scrollbars=yes,resizable=yes'
  );

  if (popup) {
    popup.focus();
  }
};

export const socialShare = async (
  platform: SocialPlatform,
  options: ShareOptions
): Promise<boolean> => {
  if (!isClient) return false;

  // Validate URL
  if (!options.url) {
    throw new Error('URL is required');
  }

  try {
    switch (platform) {
      case 'facebook':
        shareToFacebook(options);
        return true;
      case 'twitter':
        shareToTwitter(options);
        return true;
      case 'linkedin':
        shareToLinkedIn(options);
        return true;
      case 'email':
        shareToEmail(options);
        return true;
      case 'copy':
        return await copyToClipboard(options);
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  } catch (error) {
    console.error('Social share error:', error);
    return false;
  }
};

/**
 * Share with Web Share API (if supported)
 */
export const nativeShare = async (options: ShareOptions): Promise<boolean> => {
  if (!isClient) return false;

  if (navigator.share) {
    try {
      await navigator.share({
        title: options.title,
        text: options.description,
        url: options.url,
      });
      return true;
    } catch (err) {
      console.log('Native share cancelled or failed:', err);
      return false;
    }
  } else {
    console.log('Web Share API not supported');
    return false;
  }
};

export const useSocialShare = () => {
  const share = async (
    platform: SocialPlatform,
    options: ShareOptions
  ): Promise<boolean> => {
    return await socialShare(platform, options);
  };

  const shareNative = async (options: ShareOptions): Promise<boolean> => {
    return await nativeShare(options);
  };

  const copyLink = async (url: string): Promise<boolean> => {
    return await copyToClipboard({ url });
  };

  return {
    share,
    shareNative,
    copyLink,
  };
};
