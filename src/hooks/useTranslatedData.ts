import { useLocale } from 'next-intl';

/**
 * A hook that gets a translated value from data based on the current locale
 * @param data - The data object containing translations
 * @param defaultLocale - Optional fallback locale if translation is not found
 * @returns The translated value for the current locale
 */
export function useTranslatedData<T extends Record<string, any>>(
  data: T | undefined | null,
  defaultLocale = 'en'
): T | undefined {
  const locale = useLocale();

  if (!data) return undefined;

  // If the data has translations field, try to find the translation for the current locale
  if (data && Array.isArray(data)) {
    // First try exact match
    const translation = data.find((t: any) => t.languages_code === locale);

    // If translation is found, return it
    if (translation) {
      return translation as T;
    }

    // If no exact match, try to match by language prefix (for cases like en-US or vi-VN)
    const languagePrefix = locale.split('-')[0];
    const prefixTranslation = data.find((t: any) =>
      t.languages_code.startsWith(languagePrefix)
    );

    if (prefixTranslation) {
      return prefixTranslation as T;
    }

    // If no translation is found for current locale, try to find the default locale
    const defaultTranslation = data.find(
      (t: any) =>
        t.languages_code === defaultLocale || t.languages_code.startsWith(defaultLocale)
    );

    if (defaultTranslation) {
      return defaultTranslation as T;
    }
  }

  // If no translations field or no matching translation, return the original data
  return data;
}

/**
 * A function that gets a translated value from data based on the provided locale
 * @param data - The data object containing translations
 * @param locale - The locale to use for translation
 * @param defaultLocale - Optional fallback locale if translation is not found
 * @returns The translated value for the specified locale
 */
export function getTranslatedData<T extends Record<string, any>>(
  data: T | undefined | null,
  locale: string,
  defaultLocale = 'en'
): T | undefined {
  if (!data) return undefined;

  // If the data has translations field, try to find the translation for the provided locale
  if (data && Array.isArray(data)) {
    // First try exact match
    const translation = data.find((t: any) => t.languages_code === locale);

    // If translation is found, return it
    if (translation) {
      return translation as T;
    }

    // If no exact match, try to match by language prefix (for cases like en-US or vi-VN)
    const languagePrefix = locale.split('-')[0];
    const prefixTranslation = data.find((t: any) =>
      t.languages_code.startsWith(languagePrefix)
    );

    if (prefixTranslation) {
      return prefixTranslation as T;
    }

    // If no translation is found for provided locale, try to find the default locale
    const defaultTranslation = data.find(
      (t: any) =>
        t.languages_code === defaultLocale || t.languages_code.startsWith(defaultLocale)
    );

    if (defaultTranslation) {
      return defaultTranslation as T;
    }
  }

  // If no translations field or no matching translation, return the original data
  return data;
}
