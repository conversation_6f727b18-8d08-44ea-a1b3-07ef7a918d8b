export default function RankingDuetoneIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 33 32' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M15.3626 4.03066C15.8693 3.11999 16.1226 2.66666 16.5013 2.66666C16.88 2.66666 17.1333 3.11999 17.64 4.03066L17.7706 4.26532C17.9146 4.52399 17.9866 4.65199 18.0986 4.73732C18.212 4.82266 18.352 4.85466 18.632 4.91732L18.8853 4.97599C19.8693 5.19866 20.3613 5.30932 20.4786 5.68532C20.596 6.06132 20.26 6.45466 19.5893 7.23866L19.416 7.44132C19.2253 7.66399 19.1293 7.77466 19.0866 7.91332C19.044 8.05199 19.0586 8.19999 19.0866 8.49732L19.1133 8.76799C19.2146 9.81466 19.2653 10.3387 18.96 10.5707C18.6533 10.804 18.192 10.5907 17.2706 10.1667L17.0333 10.0573C16.7706 9.93732 16.64 9.87599 16.5013 9.87599C16.3626 9.87599 16.232 9.93732 15.9693 10.0573L15.732 10.1667C14.8106 10.5907 14.3493 10.804 14.0426 10.5707C13.736 10.3387 13.788 9.81466 13.8893 8.76799L13.916 8.49732C13.944 8.19999 13.9586 8.05199 13.916 7.91332C13.8733 7.77466 13.7773 7.66399 13.5866 7.44132L13.4133 7.23866C12.7426 6.45466 12.4066 6.06266 12.524 5.68532C12.6413 5.30932 13.1333 5.19866 14.1173 4.97599L14.3706 4.91732C14.6506 4.85466 14.7906 4.82399 14.904 4.73732C15.016 4.65199 15.088 4.52399 15.232 4.26532L15.3626 4.03066ZM17.8346 13.3333H15.168C13.2826 13.3333 12.34 13.3333 11.7546 13.92C11.168 14.504 11.168 15.4467 11.168 17.3333V29.3333H21.8346V17.3333C21.8346 15.448 21.8346 14.5053 21.248 13.92C20.664 13.3333 19.7213 13.3333 17.8346 13.3333Z'
        fill='currentColor'
      />
      <path
        opacity={0.5}
        d='M10.5813 25.92C9.9973 25.3333 9.05464 25.3333 7.16797 25.3333C5.2813 25.3333 4.33997 25.3333 3.75464 25.92C3.16797 26.504 3.16797 27.4467 3.16797 29.3333H11.168C11.168 27.448 11.168 26.5053 10.5813 25.92ZM21.8346 25.3333V29.3333H29.8346V25.3333C29.8346 23.448 29.8346 22.5053 29.248 21.92C28.664 21.3333 27.7213 21.3333 25.8346 21.3333C23.948 21.3333 23.0066 21.3333 22.4213 21.92C21.8346 22.504 21.8346 23.4467 21.8346 25.3333Z'
        fill='currentColor'
      />
    </svg>
  );
}
