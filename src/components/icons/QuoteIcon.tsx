export default function QuoteIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={140}
      height={108}
      viewBox='0 0 140 108'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M31.8199 64.9299C30.2635 64.9299 28.77 64.6846 27.2834 64.4609C27.7649 66.1347 28.2605 67.8373 29.0561 69.3667C29.8517 71.5888 31.094 73.515 32.3294 75.4557C33.3623 77.5551 35.1839 78.9764 36.5239 80.7727C37.9267 82.5186 39.839 83.6802 41.3535 85.1303C42.8401 86.6453 44.7873 87.4028 46.3367 88.4705C47.9559 89.4301 49.3657 90.4906 50.8732 90.9956L54.635 92.5972L57.9431 94.0184L54.5582 108L50.3916 106.961C49.0586 106.615 47.4324 106.211 45.5829 105.727C43.6915 105.367 41.6745 104.378 39.4272 103.477C37.2078 102.452 34.6395 101.76 32.2526 100.115C29.8517 98.5419 27.081 97.2289 24.6383 95.1222C22.2723 92.9507 19.4178 91.0677 17.3101 88.3046C15.0069 85.7218 12.7317 83.0092 10.966 79.9214C8.92104 76.978 7.53217 73.7459 6.06653 70.5499C4.74048 67.3539 3.67266 64.0858 2.80026 60.9114C1.14618 54.5483 0.406381 48.5026 0.120233 43.3299C-0.117061 38.1499 0.0225237 33.8429 0.315651 30.7263C0.42034 29.2545 0.615759 27.8261 0.755343 26.8377L0.929823 25.6257L1.11128 25.6689C2.35262 19.675 5.21021 14.1668 9.35349 9.78148C13.4968 5.39619 18.7565 2.31302 24.5241 0.888649C30.2917 -0.535721 36.3316 -0.243095 41.945 1.73267C47.5585 3.70844 52.5161 7.28662 56.2444 12.0533C59.9728 16.8199 62.3194 22.5803 63.013 28.668C63.7065 34.7557 62.7186 40.922 60.1634 46.4536C57.6083 51.9851 53.5904 56.6559 48.5746 59.9256C43.5587 63.1953 37.7498 64.9303 31.8199 64.9299ZM108.591 64.9299C107.035 64.9299 105.541 64.6846 104.055 64.4609C104.536 66.1347 105.032 67.8373 105.828 69.3667C106.623 71.5888 107.866 73.515 109.101 75.4557C110.134 77.5551 111.955 78.9764 113.295 80.7727C114.698 82.5186 116.61 83.6802 118.125 85.1303C119.612 86.6453 121.559 87.4028 123.108 88.4705C124.727 89.4301 126.137 90.4906 127.645 90.9956L131.406 92.5972L134.715 94.0184L131.33 108L127.163 106.961C125.83 106.615 124.204 106.211 122.354 105.727C120.463 105.367 118.446 104.378 116.199 103.477C113.986 102.445 111.411 101.76 109.024 100.107C106.623 98.5347 103.852 97.2216 101.41 95.115C99.0438 92.9435 96.1893 91.0605 94.0815 88.3046C91.7784 85.7218 89.5032 83.0092 87.7374 79.9214C85.6925 76.978 84.3037 73.7459 82.838 70.5499C81.512 67.3539 80.4441 64.0858 79.5717 60.9114C77.9177 54.5483 77.1779 48.5026 76.8917 43.3299C76.6544 38.1499 76.794 33.8429 77.0871 30.7263C77.1918 29.2545 77.3872 27.8261 77.5268 26.8377L77.7013 25.6257L77.8828 25.6689C79.1241 19.675 81.9817 14.1668 86.125 9.78148C90.2683 5.39619 95.5279 2.31302 101.296 0.888649C107.063 -0.535721 113.103 -0.243095 118.717 1.73267C124.33 3.70844 129.288 7.28662 133.016 12.0533C136.744 16.8199 139.091 22.5803 139.784 28.668C140.478 34.7557 139.49 40.922 136.935 46.4536C134.38 51.9851 130.362 56.6559 125.346 59.9256C120.33 63.1953 114.521 64.9303 108.591 64.9299Z'
        fill='#327FEF'
      />
    </svg>
  );
}
