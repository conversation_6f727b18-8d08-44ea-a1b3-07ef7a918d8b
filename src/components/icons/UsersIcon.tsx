export default function UsersIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={48}
      height={48}
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M37.541 27.2637C39.4926 27.4621 41.4262 27.889 43.2988 28.5459C44.9191 29.1144 46.001 30.6225 46.001 32.3125V34.1162C46.0008 36.4792 43.8698 38.294 41.4883 37.96L40.9414 37.8838C40.8313 37.8683 40.7206 37.8544 40.6104 37.8398C40.7586 37.2384 40.8408 36.609 40.8408 35.958V33.7188C40.8408 31.1182 39.5773 28.7355 37.541 27.2637ZM35.001 12C38.7634 12.0002 41.8135 14.9966 41.8135 18.6924C41.8133 22.388 38.7633 25.3835 35.001 25.3838C34.0644 25.3838 33.1716 25.1982 32.3594 24.8623C34.4413 22.8113 35.7343 19.962 35.7344 16.8086C35.7344 15.0918 35.3496 13.4653 34.665 12.0078C34.7762 12.0025 34.8885 12 35.001 12Z'
        fill='white'
      />
      <path
        d='M11.2217 27.2637C9.27011 27.4621 7.33648 27.889 5.46387 28.5459C3.8436 29.1144 2.76172 30.6225 2.76172 32.3125V34.1162C2.7619 36.4792 4.89294 38.294 7.27441 37.96L7.82129 37.8838C7.93141 37.8683 8.04213 37.8544 8.15234 37.8398C8.00414 37.2384 7.92188 36.609 7.92188 35.958V33.7188C7.92189 31.1182 9.18541 28.7355 11.2217 27.2637ZM13.7617 12C9.9993 12.0002 6.94922 14.9966 6.94922 18.6924C6.94943 22.388 9.99942 25.3835 13.7617 25.3838C14.698 25.3838 15.5903 25.1979 16.4023 24.8623C14.3207 22.8114 13.0284 19.9617 13.0283 16.8086C13.0283 15.0918 13.4131 13.4653 14.0977 12.0078C13.9865 12.0025 13.8742 12 13.7617 12Z'
        fill='white'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.3069 29.0429C20.851 26.7058 28.0022 26.7058 34.5463 29.0429C36.5224 29.7487 37.8415 31.6205 37.8415 33.7188V35.958C37.8415 38.892 35.2429 41.1458 32.3384 40.7308L31.6713 40.6355C26.8659 39.9491 21.9873 39.9491 17.1819 40.6355L16.5148 40.7308C13.6103 41.1458 11.0117 38.892 11.0117 35.958V33.7188C11.0117 31.6205 12.3308 29.7487 14.3069 29.0429Z'
        fill='#53687D'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M32.7342 16.8085C32.7342 12.2198 29.0144 8.5 24.4257 8.5C19.837 8.5 16.1172 12.2198 16.1172 16.8085C16.1172 21.3972 19.837 25.117 24.4257 25.117C29.0144 25.117 32.7342 21.3972 32.7342 16.8085Z'
        fill='#53687D'
      />
    </svg>
  );
}
