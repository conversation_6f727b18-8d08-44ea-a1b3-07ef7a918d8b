export default function CaseIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={48}
      height={48}
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M40 12.8574C42.2091 12.8574 44 14.6483 44 16.8575V20.1661C44 21.9326 42.8404 23.4907 41.1484 23.9982L26.2988 28.4534C24.7993 28.9033 23.2007 28.9033 21.7012 28.4534L6.85156 23.9982C5.15962 23.4907 4 21.9326 4 20.1661V16.8575C4 14.6483 5.79086 12.8574 8 12.8574H40ZM24 18.2149C23.1716 18.2149 22.5 18.8865 22.5 19.715V23.1428C22.5 23.9712 23.1716 24.6428 24 24.6428C24.8284 24.6428 25.5 23.9712 25.5 23.1428V19.715C25.5 18.8865 24.8284 18.2149 24 18.2149Z'
        fill='white'
      />
      <path
        d='M43.834 33.9995C43.8338 39.246 39.5806 43.4995 34.334 43.4995H13.668C8.4214 43.4995 4.16818 39.246 4.16797 33.9995V26.0171C4.72218 26.3803 5.33164 26.675 5.99023 26.8726L20.8398 31.3276C22.9017 31.9462 25.1003 31.9462 27.1621 31.3276L42.0117 26.8726C42.6703 26.675 43.2798 26.3803 43.834 26.0171V33.9995Z'
        fill='#53687D'
      />
      <path
        d='M29.166 10C29.166 8.70591 28.1821 7.64214 26.9219 7.51367L26.666 7.5H21.332C19.9513 7.5 18.832 8.61929 18.832 10V12.8574H15.832V10C15.832 6.96243 18.2945 4.5 21.332 4.5H26.666L27.2285 4.5293C30.0015 4.81136 32.166 7.15259 32.166 10V12.8574H29.166V10Z'
        fill='white'
      />
    </svg>
  );
}
