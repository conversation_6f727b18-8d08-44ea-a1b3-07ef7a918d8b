export default function MergeBranchIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 26 26' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M12.98 18.3333C11.9179 18.3333 10.8992 17.9119 10.1482 17.1618C9.39711 16.4116 8.97517 15.3942 8.97517 14.3333C8.97517 13.2725 9.39711 12.2551 10.1482 11.5049C10.8992 10.7548 11.9179 10.3333 12.98 10.3333M12.98 18.3333C14.0422 18.3333 15.0608 17.9119 15.8119 17.1618C16.5629 16.4116 16.9849 15.3942 16.9849 14.3333C16.9849 13.2725 16.5629 12.2551 15.8119 11.5049C15.0608 10.7548 14.0422 10.3333 12.98 10.3333M12.98 18.3333V22.3333M12.98 10.3333V1M12.98 22.3333C13.3341 22.3333 13.6736 22.4738 13.924 22.7239C14.1743 22.9739 14.315 23.313 14.315 23.6667C14.315 24.0203 14.1743 24.3594 13.924 24.6095C13.6736 24.8595 13.3341 25 12.98 25C12.626 25 12.2864 24.8595 12.0361 24.6095C11.7857 24.3594 11.6451 24.0203 11.6451 23.6667C11.6451 23.313 11.7857 22.9739 12.0361 22.7239C12.2864 22.4738 12.626 22.3333 12.98 22.3333ZM12.98 1L8.97517 5M12.98 1L16.9849 5M16.8434 13.3026L25 10.3372M25 10.3372L19.8673 7.94661M25 10.3372L22.6066 15.4639M9.10984 13.2852L1 10.3372M1 10.3372L6.13289 7.94661M1 10.3372L3.39357 15.4639'
        stroke='currentColor'
        strokeWidth={2}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
}
