export default function ClockCountDownFillIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 33 32' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <path
        d='M26.1696 12C26.1696 11.7033 26.2576 11.4133 26.4224 11.1666C26.5872 10.92 26.8215 10.7277 27.0956 10.6142C27.3697 10.5006 27.6713 10.4709 27.9623 10.5288C28.2532 10.5867 28.5205 10.7295 28.7303 10.9393C28.9401 11.1491 29.0829 11.4164 29.1408 11.7073C29.1987 11.9983 29.169 12.2999 29.0554 12.574C28.9419 12.8481 28.7496 13.0824 28.503 13.2472C28.2563 13.412 27.9663 13.5 27.6696 13.5C27.2718 13.5 26.8903 13.3419 26.609 13.0606C26.3277 12.7793 26.1696 12.3978 26.1696 12ZM24.6696 8.99998C24.9663 8.99998 25.2563 8.91201 25.503 8.74718C25.7496 8.58236 25.9419 8.34809 26.0554 8.074C26.169 7.79992 26.1987 7.49832 26.1408 7.20734C26.0829 6.91637 25.9401 6.6491 25.7303 6.43932C25.5205 6.22954 25.2532 6.08668 24.9623 6.0288C24.6713 5.97092 24.3697 6.00063 24.0956 6.11416C23.8215 6.22769 23.5872 6.41995 23.4224 6.66662C23.2576 6.9133 23.1696 7.20331 23.1696 7.49998C23.1696 7.8978 23.3277 8.27934 23.609 8.56064C23.8903 8.84194 24.2718 8.99998 24.6696 8.99998ZM28.2521 16C27.9879 15.9782 27.7259 16.0622 27.5236 16.2335C27.3213 16.4049 27.1953 16.6495 27.1734 16.9137C26.9944 19.0043 26.2221 21.0003 24.9472 22.6668C23.6724 24.3333 21.9481 25.601 19.9772 26.3208C18.0063 27.0406 15.8708 27.1825 13.822 26.7298C11.7732 26.2771 9.89631 25.2487 8.41218 23.7655C6.92805 22.2823 5.89841 20.406 5.44443 18.3575C4.99044 16.309 5.131 14.1734 5.84954 12.2021C6.56809 10.2307 7.83474 8.50564 9.50046 7.22974C11.1662 5.95385 13.1617 5.18025 15.2521 4.99998C15.3834 4.98915 15.5113 4.95255 15.6285 4.89229C15.7457 4.83202 15.8499 4.74927 15.9351 4.64875C16.0203 4.54823 16.0848 4.43191 16.1251 4.30644C16.1653 4.18097 16.1805 4.0488 16.1696 3.91748C16.1588 3.78616 16.1222 3.65826 16.0619 3.54108C16.0017 3.4239 15.9189 3.31973 15.8184 3.23454C15.7179 3.14934 15.6016 3.08477 15.4761 3.04453C15.3506 3.00428 15.2184 2.98915 15.0871 2.99998C12.6172 3.21244 10.2592 4.12591 8.2908 5.63293C6.32235 7.13994 4.82526 9.17781 3.97566 11.5068C3.12606 13.8357 2.95929 16.3589 3.49498 18.7794C4.03067 21.1999 5.24653 23.4171 6.99951 25.1701C8.75249 26.9231 10.9697 28.1389 13.3902 28.6746C15.8107 29.2103 18.3339 29.0435 20.6628 28.1939C22.9918 27.3443 25.0297 25.8473 26.5367 23.8788C28.0437 21.9104 28.9572 19.5524 29.1696 17.0825C29.1809 16.9511 29.1661 16.8187 29.126 16.6931C29.086 16.5674 29.0214 16.4509 28.9361 16.3503C28.8509 16.2497 28.7465 16.1669 28.6291 16.1068C28.5117 16.0467 28.3836 16.0104 28.2521 16ZM16.1696 6.99998C17.9497 6.99998 19.6897 7.52782 21.1698 8.51675C22.6498 9.50569 23.8033 10.9113 24.4845 12.5558C25.1657 14.2004 25.344 16.01 24.9967 17.7558C24.6494 19.5016 23.7923 21.1053 22.5336 22.3639C21.2749 23.6226 19.6713 24.4798 17.9254 24.827C16.1796 25.1743 14.37 24.9961 12.7255 24.3149C11.0809 23.6337 9.67533 22.4802 8.68639 21.0001C7.69746 19.5201 7.16962 17.78 7.16962 16C7.17227 13.6138 8.12133 11.3262 9.80858 9.63894C11.4958 7.95169 13.7835 7.00263 16.1696 6.99998ZM15.1696 16C15.1696 16.2652 15.275 16.5196 15.4625 16.7071C15.6501 16.8946 15.9044 17 16.1696 17H22.1696C22.4348 17 22.6892 16.8946 22.8767 16.7071C23.0643 16.5196 23.1696 16.2652 23.1696 16C23.1696 15.7348 23.0643 15.4804 22.8767 15.2929C22.6892 15.1053 22.4348 15 22.1696 15H17.1696V9.99998C17.1696 9.73476 17.0643 9.48041 16.8767 9.29287C16.6892 9.10534 16.4348 8.99998 16.1696 8.99998C15.9044 8.99998 15.6501 9.10534 15.4625 9.29287C15.275 9.48041 15.1696 9.73476 15.1696 9.99998V16ZM20.1696 5.99998C20.4663 5.99998 20.7563 5.91201 21.003 5.74718C21.2496 5.58236 21.4419 5.34809 21.5554 5.074C21.669 4.79992 21.6987 4.49832 21.6408 4.20734C21.5829 3.91637 21.4401 3.6491 21.2303 3.43932C21.0205 3.22954 20.7532 3.08668 20.4623 3.0288C20.1713 2.97092 19.8697 3.00063 19.5956 3.11416C19.3215 3.22769 19.0872 3.41995 18.9224 3.66662C18.7576 3.9133 18.6696 4.20331 18.6696 4.49998C18.6696 4.8978 18.8277 5.27934 19.109 5.56064C19.3903 5.84194 19.7718 5.99998 20.1696 5.99998Z'
        fill='currentColor'
      />
    </svg>
  );
}
