export default function FinanceIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={48}
      height={48}
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M23.822 22.3714C23.822 22.8169 24.1831 23.178 24.6286 23.178H39.1488C40.1738 23.178 41.0644 24.0191 40.9963 25.1097C40.7981 28.2772 39.765 31.3437 37.9944 33.9936C36.0149 36.9561 33.2016 39.2651 29.9098 40.6287C26.6179 41.9922 22.9955 42.3489 19.5008 41.6538C16.0061 40.9587 12.7961 39.243 10.2765 36.7235C7.75701 34.2039 6.04131 30.9939 5.34617 27.4992C4.65106 24.0045 5.00777 20.3821 6.37132 17.0902C7.73489 13.7984 10.0439 10.9851 13.0064 9.00558C15.6563 7.23497 18.7228 6.20187 21.8903 6.00366C22.9809 5.93565 23.822 6.82617 23.822 7.85124V22.3714Z'
        fill='#56687A'
      />
      <path
        d='M27 19.017C27 19.5598 27.4402 20 27.983 20H41.2244C42.2082 20 43.0709 19.191 42.9954 18.1351C42.8787 16.504 42.5006 14.8999 41.873 13.3848C41.1062 11.5336 39.9823 9.8515 38.5654 8.43459C37.1485 7.01768 35.4664 5.89384 33.6152 5.12701C32.1001 4.49944 30.496 4.12133 28.8649 4.00462C27.809 3.92907 27 4.79178 27 5.7756V19.017Z'
        fill='white'
      />
    </svg>
  );
}
