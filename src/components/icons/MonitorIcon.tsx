export default function MonitorIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={48}
      height={48}
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M42 27.0323C42 28.3498 41.4647 29.613 40.5126 30.5446C39.5605 31.4762 38.2696 32 36.9231 32H11.0769C9.73044 32 8.43949 31.4762 7.48738 30.5446C6.53527 29.613 6 28.3498 6 27.0323V8.96774C6 7.65021 6.53527 6.38703 7.48738 5.45539C8.43949 4.52376 9.73044 4 11.0769 4H36.9231C38.2696 4 39.5605 4.52376 40.5126 5.45539C41.4647 6.38702 42 7.65021 42 8.96774V27.0323Z'
        fill='#53687D'
      />
      <path
        d='M28.2632 35L28.3844 35.0048C28.9845 35.0489 29.4933 35.4173 29.6417 35.9292L30.9674 40.5091L32.5837 41.905C32.9901 42.256 33.1117 42.7839 32.8918 43.2425C32.6718 43.7011 32.1537 44 31.579 44H16.421C15.8463 44 15.3282 43.7011 15.1082 43.2425C14.8883 42.7839 15.0099 42.256 15.4163 41.905L17.0326 40.5091L18.3583 35.9292L18.3926 35.8294C18.5876 35.337 19.1254 35 19.7368 35H28.2632Z'
        fill='white'
      />
    </svg>
  );
}
