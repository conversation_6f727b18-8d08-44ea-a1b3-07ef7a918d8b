import { motion } from 'framer-motion';

export default function Logo3DIcon() {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='359'
      height='573'
      viewBox='0 0 359 573'
      fill='none'
    >
      <g opacity='0.4' filter='url(#filter0_f_122_36)'>
        <path
          opacity='0.15'
          d='M131.152 280.276L72.202 307.652L72.1548 307.612V242.644L121.65 274.201L123.144 275.148L129.296 279.092L131.152 280.276Z'
          fill='#003384'
        />
        <motion.path
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 4, repeat: Infinity }}
          d='M72.1548 307.671V209.016L287 72V170.655L72.1548 307.671Z'
          fill='#327FEF'
        />
        <motion.path
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 4, repeat: Infinity }}
          d='M287 264.478V363.113L276.05 370.115L72.1548 500.149V401.494L192.887 324.495L200.329 319.762L205.772 316.271L209.737 313.746L287 264.478Z'
          fill='#327FEF'
        />
        <path
          opacity='0.15'
          d='M287 264.478V363.054L286.685 362.896L199.495 320.275L209.737 313.746L287 264.478Z'
          fill='#4F2500'
        />
        <motion.path
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 4, repeat: Infinity }}
          d='M286.999 363.113L286.684 362.916L209.563 314.2L209.453 314.14L148.504 275.641L226.066 226.175L286.999 264.478V363.113Z'
          fill='#327FEF'
        />
      </g>
      <path
        opacity='0.4'
        d='M193 308.545L135.032 338L76.4729 308.24L66.0537 302.947L66 302.911V244L122.321 272.615L124.022 273.473L131.022 277.05L133.134 278.123L193 308.545Z'
        fill='#FFA155'
      />
      <path
        opacity='0.6'
        d='M189 308.425L134.683 336L79.8132 308.14L70.0503 303.184L70 303.151V248L122.773 274.788L124.367 275.592L130.926 278.941L132.905 279.945L189 308.425Z'
        fill='#FFA155'
      />
      <path
        d='M184.249 308.001L133.549 333.755L82.3317 307.735L73.2188 303.107L73.1719 303.076V251.568L122.432 276.587L123.919 277.337L130.041 280.465L131.889 281.403L184.249 308.001Z'
        fill='#FF8300'
      />
      <path
        opacity='0.15'
        d='M131.889 281.403L73.2188 303.107L73.1719 303.076V251.568L122.432 276.587L123.919 277.337L130.041 280.465L131.889 281.403Z'
        fill='#003384'
      />
      <path
        d='M70 302.845V224.629L283.824 116V194.216L70 302.845Z'
        fill='url(#paint0_linear_122_36)'
      />
      <g style={{ mixBlendMode: 'luminosity' }}>
        <path
          d='M286.996 268.877V347.078L276.098 352.629L73.1719 455.723V377.507L193.331 316.461L200.737 312.708L206.154 309.94L210.1 307.938L286.996 268.877Z'
          fill='#2B2B2B'
        />
        <path
          d='M286.996 268.877V347.078L276.098 352.629L73.1719 455.723V377.507L193.331 316.461L200.737 312.708L206.154 309.94L210.1 307.938L286.996 268.877Z'
          fill='url(#paint1_linear_122_36)'
        />
      </g>
      <path
        opacity='0.15'
        d='M286.999 268.877V347.031L286.686 346.906L199.91 313.114L210.103 307.938L286.999 268.877Z'
        fill='#00124C'
      />
      <g style={{ mixBlendMode: 'luminosity' }}>
        <path
          d='M286.993 347.078L286.68 346.922L209.925 308.299L209.815 308.252L149.156 277.729L226.35 238.511L286.993 268.878V347.078Z'
          fill='#2B2B2B'
        />
        <path
          d='M286.993 347.078L286.68 346.922L209.925 308.299L209.815 308.252L149.156 277.729L226.35 238.511L286.993 268.878V347.078Z'
          fill='url(#paint2_linear_122_36)'
        />
      </g>
      <circle cx='71' cy='223' r='4' fill='#FF8300' />
      <g filter='url(#filter1_f_122_36)'>
        <circle cx='71' cy='223' r='12' fill='url(#paint3_linear_122_36)' />
      </g>
      <path d='M286.5 116L72 223' stroke='url(#paint4_linear_122_36)' />
      <circle cx='286' cy='348' r='4' fill='#FF8300' />
      <g filter='url(#filter2_f_122_36)'>
        <circle cx='286' cy='348' r='12' fill='url(#paint5_linear_122_36)' />
      </g>
      <path d='M74 457L287.5 348.5' stroke='url(#paint6_linear_122_36)' />
      <circle cx='150' cy='278' r='4' transform='rotate(180 150 278)' fill='#FF8300' />
      <g filter='url(#filter3_f_122_36)'>
        <circle
          cx='150'
          cy='278'
          r='12'
          transform='rotate(180 150 278)'
          fill='url(#paint7_linear_122_36)'
        />
      </g>
      <path d='M227 238L150 278' stroke='url(#paint8_linear_122_36)' />
      <defs>
        <filter
          id='filter0_f_122_36'
          x='0.154785'
          y='0'
          width='358.845'
          height='572.149'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur stdDeviation='36' result='effect1_foregroundBlur_122_36' />
        </filter>
        <filter
          id='filter1_f_122_36'
          x='35'
          y='187'
          width='72'
          height='72'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur stdDeviation='12' result='effect1_foregroundBlur_122_36' />
        </filter>
        <filter
          id='filter2_f_122_36'
          x='250'
          y='312'
          width='72'
          height='72'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur stdDeviation='12' result='effect1_foregroundBlur_122_36' />
        </filter>
        <filter
          id='filter3_f_122_36'
          x='114'
          y='242'
          width='72'
          height='72'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur stdDeviation='12' result='effect1_foregroundBlur_122_36' />
        </filter>
        <linearGradient
          id='paint0_linear_122_36'
          x1='70'
          y1='265.5'
          x2='284'
          y2='160'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#042951' />
          <stop offset='1' stopColor='#012447' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_122_36'
          x1='73.1719'
          y1='418.377'
          x2='287.172'
          y2='312.877'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#042951' />
          <stop offset='1' stopColor='#012447' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_122_36'
          x1='149.156'
          y1='325.379'
          x2='281.15'
          y2='253.188'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#042951' />
          <stop offset='1' stopColor='#012447' />
        </linearGradient>
        <linearGradient
          id='paint3_linear_122_36'
          x1='71'
          y1='219'
          x2='71'
          y2='235'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FBFBFB' />
          <stop offset='0.5' stopColor='#FF9F00' />
          <stop offset='1' stopColor='#FFBB00' />
        </linearGradient>
        <linearGradient
          id='paint4_linear_122_36'
          x1='284.5'
          y1='116'
          x2='72'
          y2='223'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#103263' />
          <stop offset='1' stopColor='#FEA73C' />
        </linearGradient>
        <linearGradient
          id='paint5_linear_122_36'
          x1='286'
          y1='344'
          x2='286'
          y2='360'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FBFBFB' />
          <stop offset='0.5' stopColor='#FF9F00' />
          <stop offset='1' stopColor='#FFBB00' />
        </linearGradient>
        <linearGradient
          id='paint6_linear_122_36'
          x1='75.9907'
          y1='457'
          x2='289.073'
          y2='351.683'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#103263' />
          <stop offset='1' stopColor='#FEA73C' />
        </linearGradient>
        <linearGradient
          id='paint7_linear_122_36'
          x1='150'
          y1='274'
          x2='150'
          y2='290'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FBFBFB' />
          <stop offset='0.5' stopColor='#FF9F00' />
          <stop offset='1' stopColor='#FFBB00' />
        </linearGradient>
        <linearGradient
          id='paint8_linear_122_36'
          x1='226.282'
          y1='238'
          x2='148.779'
          y2='275.474'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#103263' />
          <stop offset='1' stopColor='#FEA73C' />
        </linearGradient>
      </defs>
    </svg>
  );
}
