export default function UploadIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox='0 0 43 42' fill='none' xmlns='http://www.w3.org/2000/svg' {...props}>
      <g clipPath='url(#clip0_753_5014)'>
        <path
          d='M33.9432 3.12073H14.6758V11.1108H38.0582V7.23415C38.0582 4.96579 36.2122 3.12073 33.9432 3.12073Z'
          fill='#FCFDFF'
        />
        <path
          d='M23.0352 12.3403H0.5V4.92636C0.5 2.20972 2.71068 0 5.42828 0H12.6336C13.3497 0 14.0396 0.150925 14.6664 0.434509C15.5418 0.828964 16.2939 1.47913 16.8213 2.3286L23.0352 12.3403Z'
          fill='#7AAAF3'
        />
        <path
          d='M42.5 14.0001V37.8815C42.5 40.1527 40.6511 42 38.3789 42H4.62111C2.34891 42 0.5 40.1527 0.5 37.8815V9.88062H38.3789C40.6511 9.88062 42.5 11.7286 42.5 14.0001Z'
          fill='#1D6CDB'
        />
        <path
          d='M42.5 14.0001V37.8815C42.5 40.1527 40.6511 42 38.3789 42H21.5V9.88062H38.3789C40.6511 9.88062 42.5 11.7286 42.5 14.0001Z'
          fill='#327FEF'
        />
        <path
          d='M32.549 25.9398C32.549 32.0322 27.5928 36.9887 21.5011 36.9887C15.4093 36.9887 10.4531 32.0322 10.4531 25.9398C10.4531 19.8483 15.4093 14.8918 21.5011 14.8918C27.5928 14.8918 32.549 19.8483 32.549 25.9398Z'
          fill='#F5F9FF'
        />
        <path
          d='M32.5479 25.9398C32.5479 32.0322 27.5918 36.9887 21.5 36.9887V14.8918C27.5918 14.8918 32.5479 19.8483 32.5479 25.9398Z'
          fill='#FCFDFF'
        />
        <path
          d='M25.0593 26.0753C24.8289 26.2704 24.5466 26.3656 24.2668 26.3656C23.9166 26.3656 23.5686 26.2173 23.3251 25.9282L22.7287 25.2213V29.8494C22.7287 30.5287 22.1776 31.0799 21.4983 31.0799C20.8189 31.0799 20.2678 30.5287 20.2678 29.8494V25.2213L19.6715 25.9282C19.2325 26.4476 18.4567 26.514 17.9373 26.0753C17.4182 25.6373 17.3515 24.8612 17.7896 24.3418L20.2252 21.4543C20.5427 21.0788 21.0061 20.8628 21.4983 20.8628C21.9905 20.8628 22.4538 21.0788 22.7714 21.4543L25.207 24.3418C25.645 24.8612 25.5784 25.6373 25.0593 26.0753Z'
          fill='#327FEF'
        />
        <path
          d='M25.061 26.0753C24.8306 26.2704 24.5483 26.3656 24.2686 26.3656C23.9183 26.3656 23.5703 26.2173 23.3268 25.9282L22.7305 25.2213V29.8494C22.7305 30.5287 22.1793 31.0799 21.5 31.0799V20.8628C21.9922 20.8628 22.4555 21.0788 22.7731 21.4543L25.2087 24.3418C25.6467 24.8612 25.5801 25.6373 25.061 26.0753Z'
          fill='#7AAAF3'
        />
      </g>
      <defs>
        <clipPath id='clip0_753_5014'>
          <rect width={42} height={42} fill='white' transform='translate(0.5)' />
        </clipPath>
      </defs>
    </svg>
  );
}
