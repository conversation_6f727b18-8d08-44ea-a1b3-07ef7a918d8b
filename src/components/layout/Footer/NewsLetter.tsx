import { IconArrowNarrowRight } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import { FC, useState } from 'react';

import { Button } from '@/components/common/Button';
import { Checkbox } from '@/components/common/Checkbox';
// import SuccessModal from '@/components/common/Modal/SuccessModal';
import { Glasscard } from '@/components/common/Card';
import { createNewsletterSubscription } from '@/services/directus';

type NewsLetterType = {
  email: string;
  agreeToTerms: boolean;
};

const EMAIL_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

const NewsLetter: FC = () => {
  const t = useTranslations('common');
  const [newsLetter, setNewsLetter] = useState<NewsLetterType>({
    email: '',
    agreeToTerms: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isOpenModal, setIsOpenModal] = useState(false);

  const onSubmitNewsletter = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    try {
      if (newsLetter.email === '') return alert('Email is required.');
      if (!EMAIL_PATTERN.test(newsLetter.email)) return alert('Invalid email address.');
      if (!newsLetter.agreeToTerms)
        return alert('You have to agree to the privacy policy.');
      setIsSubmitting(true);
      const response = await createNewsletterSubscription({
        email: newsLetter.email,
        agreeToPrivacyPolicy: newsLetter.agreeToTerms,
      });
      if (!response.success) {
        throw new Error('Failed to subscribe to newsletter');
      }
      setNewsLetter({ email: '', agreeToTerms: false });
      setIsOpenModal(true);
    } catch (err) {
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className='col-span-1 lg:col-span-4'>
      <Glasscard className='w-full rounded-2xl bg-[#203C860D] p-6'>
        <div className='flex flex-col gap-4'>
          <h3 className='text-base font-medium text-gray-50 md:text-lg'>
            {t('stay_updated_with_vietnam_silicon')}
          </h3>
          <p className='text-xs font-medium text-gray-50 opacity-60'>
            {t('subscribe_content')}
          </p>
          <form className='flex w-full flex-col gap-4'>
            <div className='flex w-full'>
              <input
                type='email'
                placeholder='Email Address'
                className='w-full rounded-l-lg bg-white px-4 py-2 text-black focus:outline-none'
                value={newsLetter.email}
                onChange={(e) => setNewsLetter({ ...newsLetter, email: e.target.value })}
                maxLength={256}
              />
              <Button
                className='relative h-12 rounded-l-none rounded-r-lg'
                variant='default'
                aria-label='Subscribe to newsletter'
                onClick={onSubmitNewsletter}
              >
                {isSubmitting ? (
                  <div className='size-5 animate-spin rounded-full border-[3px] border-transparent border-t-white' />
                ) : (
                  <IconArrowNarrowRight />
                )}
              </Button>
            </div>
            <div className='flex items-center justify-center gap-2 lg:justify-start'>
              <Checkbox
                checked={newsLetter.agreeToTerms}
                onCheckedChange={() =>
                  setNewsLetter({ ...newsLetter, agreeToTerms: !newsLetter.agreeToTerms })
                }
                className='cursor-pointer'
                aria-label='Agree to privacy policy'
              />
              <label htmlFor='agreeTerms' className='text-xs text-gray-50'>
                {t('i_agree_to_the')}{' '}
                <a href='/privacy' target='_blank' className='text-primary underline'>
                  {t('privacy_policy')}
                </a>
              </label>
            </div>
          </form>
        </div>
      </Glasscard>
      {/* <SuccessModal
        isOpen={isOpenModal}
        onClose={onCloseModal}
        content={t('submit_email_success')}
      /> */}
    </div>
  );
};

export default NewsLetter;
