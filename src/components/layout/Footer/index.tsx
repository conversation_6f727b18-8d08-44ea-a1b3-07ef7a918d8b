'use client';

import { Link } from '@/i18n/navigation';
import { getAssetUrl, getCdnUrl } from '@/utils/url/asset';
import {
  IconBrandFacebookFilled,
  IconBrandLinkedinFilled,
  IconMailFilled,
  IconMapPinFilled,
  IconPhoneFilled,
} from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import NewsLetter from './NewsLetter';
import { useTranslatedData } from '@/hooks/useTranslatedData';
import ScrollReveal from '@/components/motion/ScrollReveal';

export default function Footer({ media, navigation }: { media: any; navigation: any }) {
  const t = useTranslations('common');
  const translateMedia = useTranslatedData(media?.translations);
  const socialLinks = [
    {
      Icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='7'
          height='13'
          viewBox='0 0 7 13'
          fill='none'
        >
          <path
            d='M5.0932 2.02078H6.70158C6.84955 2.02078 6.96964 1.90762 6.96964 1.76819V0.252598C6.96964 0.113164 6.84955 0 6.70158 0H5.0932C3.46766 0 2.1445 1.24632 2.1445 2.77858V4.54676H0.268063C0.120092 4.54676 0 4.65993 0 4.79936V6.31495C0 6.45438 0.120092 6.56755 0.268063 6.56755H2.1445V11.8721C2.1445 12.0115 2.2646 12.1247 2.41257 12.1247H4.02095C4.16892 12.1247 4.28901 12.0115 4.28901 11.8721V6.56755H6.16545C6.28072 6.56755 6.38312 6.49783 6.42011 6.39477L6.95624 4.87918C6.98358 4.80239 6.96964 4.71752 6.91924 4.65134C6.86831 4.58566 6.78789 4.54676 6.70158 4.54676H4.28901V2.77858C4.28901 2.36078 4.64982 2.02078 5.0932 2.02078Z'
            fill='#DFDFDF'
          />
        </svg>
      ),
      href: media?.facebook,
      label: 'Facebook',
    },
    {
      Icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='12'
          height='12'
          viewBox='0 0 12 12'
          fill='none'
        >
          <path
            d='M1.48355 2.9671C2.30289 2.9671 2.9671 2.30289 2.9671 1.48355C2.9671 0.664208 2.30289 0 1.48355 0C0.664208 0 0 0.664208 0 1.48355C0 2.30289 0.664208 2.9671 1.48355 2.9671Z'
            fill='#DFDFDF'
          />
          <path
            d='M2.71984 3.95801H0.247258C0.110772 3.95801 0 4.06878 0 4.20527V11.623C0 11.7595 0.110772 11.8703 0.247258 11.8703H2.71984C2.85633 11.8703 2.9671 11.7595 2.9671 11.623V4.20527C2.9671 4.06878 2.85633 3.95801 2.71984 3.95801Z'
            fill='#DFDFDF'
          />
          <path
            d='M10.0876 3.5464C9.03077 3.18442 7.70893 3.50239 6.91622 4.07257C6.88902 3.96625 6.7921 3.88712 6.67687 3.88712H4.20429C4.0678 3.88712 3.95703 3.9979 3.95703 4.13438V11.5521C3.95703 11.6886 4.0678 11.7994 4.20429 11.7994H6.67687C6.81336 11.7994 6.92413 11.6886 6.92413 11.5521V6.22124C7.3237 5.87706 7.83849 5.76728 8.25982 5.94629C8.66829 6.11888 8.9022 6.5402 8.9022 7.10148V11.5521C8.9022 11.6886 9.01297 11.7994 9.14946 11.7994H11.622C11.7585 11.7994 11.8693 11.6886 11.8693 11.5521V6.6035C11.8411 4.57153 10.8852 3.81937 10.0876 3.5464Z'
            fill='#DFDFDF'
          />
        </svg>
      ),
      href: media?.linkedin,
      label: 'LinkedIn',
    },
  ];
  const footerNavItems = navigation?.map((nav: any) => ({
    ...(nav?.item || {}),
    label: t(`main_nav.${nav?.item?.label}`),
  }));
  return (
    <footer className='border-t border-gray-500/10 text-gray-50'>
      {/* Main Footer */}
      <div className='container mx-auto px-4 py-14 text-center sm:px-6 lg:px-8 lg:text-start'>
        <ScrollReveal className='grid grid-cols-1 gap-10 lg:grid-cols-12'>
          {/* Logo */}
          <div className='col-span-1 flex flex-col items-center gap-2 lg:col-span-2 lg:items-start'>
            <Image
              src={getAssetUrl(media?.['default_logo']) || getCdnUrl('/logo/logo.svg')}
              unoptimized
              alt='Vietnam Silicon'
              width={160}
              height={54}
            />
          </div>
          <div className='hidden lg:block'></div>
          {/* Media */}
          <div className='col-span-1 flex flex-col items-center gap-4 lg:col-span-4 lg:items-start'>
            <p className='flex items-start gap-2'>
              <IconMapPinFilled className='flex-none' />
              {translateMedia?.address}
            </p>
            <a href={`tel:${media?.phone}`} className='flex items-center gap-2'>
              <IconPhoneFilled />
              {media?.phone}
            </a>
            <a href={`mailto:${media?.mail}`} className='flex items-center gap-2'>
              <IconMailFilled />
              {media?.mail}
            </a>
            <div className='flex items-center gap-2'>
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  target='_blank'
                  className='hover:bg-primary border-opacity-10 flex size-9 items-center justify-center rounded-full border border-gray-400/10 p-2 transition-all duration-100 hover:text-white'
                  aria-label={social.label}
                >
                  {social.Icon}
                </a>
              ))}
            </div>
          </div>
          <div className='hidden lg:block'></div>

          {/* Newsletter */}
          <NewsLetter />
        </ScrollReveal>

        {/* Navigation Links & Copyright */}
        <ScrollReveal className='mt-12 border-t border-gray-50/5 pt-8'>
          <div className='flex flex-col items-center justify-between gap-4 lg:flex-row'>
            <div className='flex flex-wrap items-center justify-center gap-2 text-sm text-gray-50 lg:justify-start lg:gap-8'>
              {footerNavItems.map((item: any) => (
                <Link key={item.id} href={item.href} className='mr-3 lg:mr-0'>
                  {item.label}
                </Link>
              ))}
            </div>
            <div className='flex flex-col items-center gap-6'>
              <p className='text-sm whitespace-pre-wrap opacity-50 md:whitespace-normal'>
                Copyright © {new Date().getFullYear()} Vietnam Silicon.{`\n`} All Rights
                Reserved
              </p>
            </div>
          </div>
        </ScrollReveal>
      </div>
    </footer>
  );
}
