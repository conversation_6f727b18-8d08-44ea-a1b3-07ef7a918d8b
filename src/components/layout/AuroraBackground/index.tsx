'use client';
import { cn } from '@/utils/cn';
import React, { ReactNode } from 'react';

interface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {
  children?: ReactNode;
}

export const AuroraBackground = ({
  className,
  children,
  ...props
}: AuroraBackgroundProps) => {
  return (
    <main className='dark w-full'>
      <div
        className={cn(
          'transition-bg relative flex h-[80vh] w-full flex-col items-center',
          className
        )}
        {...props}
      >
        <div className='absolute inset-0 overflow-hidden'>
          <div
            className={cn(
              `[--aurora:repeating-linear-gradient(100deg,#f3f9ff_10%,#a5a1ff_15%,#a8d8ff_20%,#e8d8ff_25%,#5db3ff_30%)] [--dark-gradient:repeating-linear-gradient(100deg,var(--black)_0%,var(--black)_7%,var(--transparent)_10%,var(--transparent)_12%,var(--black)_16%)] [--white-gradient:repeating-linear-gradient(100deg,#6861a1_0%,#6861a1_7%,var(--transparent)_10%,var(--transparent)_12%,#6861a1_16%)]`,
              `pointer-events-none absolute inset-0 [background-image:var(--white-gradient),var(--aurora)] [background-size:300%,_200%] bg-center opacity-50 blur-[10px] invert filter will-change-transform`,
              `md:after:animate-aurora after:animate-aurora2 after:absolute after:inset-0 after:[background-image:var(--white-gradient),var(--aurora)] after:[background-size:200%,_100%] after:[background-attachment:fixed] after:mix-blend-difference after:content-[""] dark:[background-image:var(--dark-gradient),var(--aurora)] dark:invert-0 after:dark:[background-image:var(--dark-gradient),var(--aurora)]`,
              `[mask-image:radial-gradient(ellipse_at_50%_0%,black_10%,var(--transparent)_60%)]`
            )}
          ></div>
        </div>
        {children}
      </div>
    </main>
  );
};
