'use client';
import { Dialog, DialogContent, DialogTitle } from '@/components/common/Dialog';
import { cn } from '@/utils/cn';
import { HTMLMotionProps, motion } from 'framer-motion';
import { forwardRef, useState } from 'react';
const GalleryDialog = forwardRef<
  HTMLDivElement,
  {
    children?: React.ReactNode;
    className?: React.HTMLAttributes<HTMLDivElement>['className'];
    src: string;
    isOpen?: boolean;
    onOpenChange?: (isOpen: boolean) => void;
    type: 'image' | 'video';
  } & HTMLMotionProps<'div'>
>(({ children, className, src, isOpen, onOpenChange, type, ...props }, ref) => {
  const [isChildrenOpen, setIsChildrenOpen] = useState(false);
  return (
    <>
      {children && (
        <motion.div
          ref={ref}
          onClick={() => setIsChildrenOpen(true)}
          className={cn(className, 'cursor-pointer')}
          role='button'
          tabIndex={0}
          aria-label={`Open ${type} in gallery`}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setIsChildrenOpen(true);
            }
          }}
          {...props}
        >
          {children}
        </motion.div>
      )}
      <Dialog
        open={isChildrenOpen || isOpen}
        onOpenChange={onOpenChange ? onOpenChange : setIsChildrenOpen}
      >
        <DialogTitle className='sr-only'>Gallery</DialogTitle>
        <DialogContent className='container h-full max-h-[80vh] w-full overflow-hidden p-2 md:p-5 lg:p-10'>
          {src &&
            (type === 'image' ? (
              <img
                src={src}
                alt={`Gallery image`}
                className='h-full max-h-[70vh] w-full overflow-hidden rounded-lg object-contain'
              />
            ) : (
              <video
                src={src}
                controls
                loop
                autoPlay
                className='h-full max-h-[70vh] w-full overflow-hidden rounded-lg object-contain'
              />
            ))}
        </DialogContent>
      </Dialog>
    </>
  );
});

GalleryDialog.displayName = 'GalleryDialog';

export default GalleryDialog;
