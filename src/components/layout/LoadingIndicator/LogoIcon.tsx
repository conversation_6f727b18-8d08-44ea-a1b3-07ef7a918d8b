import { useEffect } from 'react';

import { animate, motion, useMotionValue, useTransform } from 'framer-motion';

export default function LogoIcon() {
  const count = useMotionValue(0);

  useEffect(() => {
    const animation = animate(0, 1, {
      duration: 0.75,
      type: 'spring',
      bounce: 0,
      delay: 0,
      onUpdate: (latest: number) => {
        count.set(latest);
      },
    });

    return () => {
      animation.stop();
    };
  }, [count]);
  const line1 = useTransform(count, [0, 0.3], [0, 1]);
  const line1Display = useTransform(count, [0, 0.2], ['none', 'block']);
  const line2 = useTransform(count, [0.3, 0.4], [0, 1]);
  const line3 = useTransform(count, [0.27, 0.5], [0, 1]);
  const line4 = useTransform(count, [0.4, 0.7], [0, 1]);
  const line5 = useTransform(count, [0.6, 0.8], [0, 1]);
  const line6 = useTransform(count, [0.68, 1], [0, 1]);
  const line6Display = useTransform(count, [0.68, 1], ['none', 'block']);

  return (
    <motion.svg
      xmlns='http://www.w3.org/2000/svg'
      width='40'
      height='64'
      viewBox='0 0 40 64'
      fill='none'
      opacity={count}
    >
      <path
        d='M20.6155 36.0834L11.2057 40.8632L1.70004 36.034L0.00871816 35.175L0 35.1692V25.6095L9.14244 30.2529L9.41852 30.3923L10.5548 30.9727L10.8977 31.1468L20.6155 36.0834Z'
        fill='url(#paint0_linear_24_24)'
      />
      <path
        opacity='0.15'
        d='M10.8977 31.1468L0.00871816 35.175L0 35.1692V25.6095L9.14244 30.2529L9.41852 30.3923L10.5548 30.9727L10.8977 31.1468Z'
        fill='url(#paint1_linear_24_24)'
      />
      <path
        d='M0 35.1779V20.6613L39.6851 0.5V15.0166L0 35.1779Z'
        fill='url(#paint2_linear_24_24)'
      />
      <path
        d='M39.6851 28.8221V43.3358L37.6625 44.3661L0 63.5V48.9834L22.3011 37.6534L23.6756 36.9569L24.6811 36.4432L25.4134 36.0717L39.6851 28.8221Z'
        fill='url(#paint3_linear_24_24)'
      />
      <path
        opacity='0.15'
        d='M39.6851 28.8221V43.3271L39.627 43.3039L23.5216 37.0323L25.4134 36.0717L39.6851 28.8221Z'
        fill='url(#paint4_linear_24_24)'
      />
      <path
        d='M39.685 43.3358L39.6269 43.3068L25.3814 36.1385L25.3611 36.1298L14.103 30.4648L28.4299 23.1862L39.685 28.8221V43.3358Z'
        fill='url(#paint5_linear_24_24)'
      />
      <defs>
        {/*1.5*/}
        <linearGradient
          id='paint0_linear_24_24'
          x1='-2'
          y1='28.5'
          x2='24'
          y2='43'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FF8300' />
          <motion.stop offset={line3} stopColor='#FF8300' />
          <motion.stop offset={line3} stopColor='white' stopOpacity='0' />
        </linearGradient>
        {/*1*/}
        <linearGradient
          id='paint1_linear_24_24'
          x1='-1'
          y1='31'
          x2='14.5'
          y2='38.5'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#003384' />
          <motion.stop offset={line2} stopColor='#003384' />
          <motion.stop offset={line2} stopColor='white' stopOpacity='0' />
        </linearGradient>
        {/*0*/}
        <motion.linearGradient
          id='paint2_linear_24_24'
          x1='39.5'
          y1='14.5'
          x2='-3'
          y2='14.5'
          gradientUnits='userSpaceOnUse'
          style={{ display: line1Display }}
        >
          <stop stopColor='#FCFDFF' />
          <motion.stop offset={line1} stopColor='#FCFDFF' />
          <motion.stop offset={line1} stopColor='white' stopOpacity='0' />
        </motion.linearGradient>
        {/*3*/}
        <motion.linearGradient
          id='paint3_linear_24_24'
          x1='39.5'
          y1='44.5'
          x2='-4.5'
          y2='44.5'
          gradientUnits='userSpaceOnUse'
          style={{ display: line6Display }}
        >
          <stop stopColor='#FCFDFF' />
          <motion.stop offset={line6} stopColor='#FCFDFF' />
          <motion.stop offset={line6} stopColor='white' stopOpacity='0' />
        </motion.linearGradient>
        {/*2.5*/}
        <linearGradient
          id='paint4_linear_24_24'
          x1='31.6033'
          y1='28.8221'
          x2='31.6033'
          y2='43.3271'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#00124C' />
          <motion.stop offset={line5} stopColor='#00124C' />
          <motion.stop offset={line5} stopColor='white' stopOpacity='0' />
        </linearGradient>
        {/*2*/}
        <linearGradient
          id='paint5_linear_24_24'
          x1='13'
          y1='27'
          x2='43.2903'
          y2='24.4795'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FCFDFF' />
          <motion.stop offset={line4} stopColor='#FCFDFF' />
          <motion.stop offset={line4} stopColor='white' stopOpacity='0' />
        </linearGradient>
      </defs>
    </motion.svg>
  );
}
