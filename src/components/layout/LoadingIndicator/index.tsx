'use client';
import LogoIcon from './LogoIcon';
import { motion, AnimatePresence } from 'framer-motion';
import { ReactNode, useEffect, useState } from 'react';

export const LoadingIndicator = ({ children }: { children: ReactNode }) => {
  const [loadingFlow, setLoadingFlow] = useState(true);
  useEffect(() => {
    setTimeout(() => {
      setLoadingFlow(false);
    }, 300);
  }, []);
  return (
    <>
      <AnimatePresence>
        {loadingFlow ? (
          <motion.div
            initial={{ opacity: 1, filter: 'blur(10px)' }}
            animate={{
              opacity: 1,
              filter: 'blur(0px)',
              transition: { duration: 0.05 },
            }}
            exit={{
              opacity: [1, 0.5, 0.2, 0],
              filter: 'blur(10px)',
              transition: {
                duration: 0.1,
              },
            }}
            className='bg-background fixed top-0 z-[10000] flex h-screen w-screen flex-col items-center justify-center overflow-hidden'
          >
            <div className='flex scale-150 flex-col items-center gap-2 md:flex-row'>
              <LogoIcon />
            </div>
          </motion.div>
        ) : null}
      </AnimatePresence>
      {children}
    </>
  );
};
