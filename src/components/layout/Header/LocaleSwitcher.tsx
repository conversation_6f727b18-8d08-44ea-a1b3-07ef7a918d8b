'use client';

import Link from 'next/link';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/common/DropdownMenu';
import { IconWorld } from '@tabler/icons-react';
import { useLocale } from 'next-intl';
import Image from 'next/image';
import clxs from 'clsx';

const LOCALES = [
  { code: 'en', label: 'English' },
  { code: 'vi', label: 'Vietnamese' },
];

export default function LocaleSwitcher({ className }: { className: string }) {
  const locale = useLocale();

  return (
    <div className={clxs('relative', className)}>
      <DropdownMenu>
        <DropdownMenuTrigger className='inline-flex w-full justify-center gap-x-2 rounded-xl py-2 text-sm'>
          <IconWorld /> {locale.toUpperCase()}
        </DropdownMenuTrigger>
        <DropdownMenuContent
          avoidCollisions={true}
          align='end'
          className='w-40 overflow-hidden rounded-lg bg-white p-0 shadow-lg transition-opacity'
        >
          {LOCALES.map(({ code, label }) => (
            <DropdownMenuItem
              key={code}
              asChild
              className='hover:bg-primary hover:text-primary hover:border-primary block w-full cursor-pointer border-l-2 border-white px-4 py-4'
            >
              <Link href='/' locale={code}>
                <div className='flex items-center gap-2'>
                  <Image width={20} height={20} alt={label} src={`/icons/${code}.svg`} />
                  {label}
                </div>
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
