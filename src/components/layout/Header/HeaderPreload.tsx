import Head from 'next/head';

interface HeaderPreloadProps {
  logoUrl?: string;
  shortLogoUrl?: string;
}

export default function HeaderPreload({ logoUrl, shortLogoUrl }: HeaderPreloadProps) {
  return (
    <Head>
      {/* Preload critical logo images */}
      {logoUrl && (
        <link
          rel='preload'
          href={logoUrl}
          as='image'
          type='image/svg+xml'
          fetchPriority='high'
        />
      )}
      {shortLogoUrl && (
        <link
          rel='preload'
          href={shortLogoUrl}
          as='image'
          type='image/svg+xml'
          fetchPriority='high'
        />
      )}

      {/* Preload critical fonts */}
      <link
        rel='preload'
        href='/fonts/inter-var.woff2'
        as='font'
        type='font/woff2'
        crossOrigin='anonymous'
      />

      {/* DNS prefetch for external resources */}
      <link rel='dns-prefetch' href='//fonts.googleapis.com' />
      <link rel='dns-prefetch' href='//fonts.gstatic.com' />

      {/* Preconnect to critical domains */}
      <link rel='preconnect' href='https://fonts.googleapis.com' />
      <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='anonymous' />
    </Head>
  );
}
