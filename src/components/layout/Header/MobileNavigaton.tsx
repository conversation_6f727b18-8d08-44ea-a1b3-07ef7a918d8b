'use client';
import { Button } from '@/components/common/Button';
import { Link } from '@/i18n/navigation';
import { cn } from '@/utils/cn';
import { AnimatePresence, motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface NavItem {
  id: string;
  label: string;
  href: string;
  children?: NavItem[];
}

interface MobileNavigationProps {
  isOpen: boolean;
  navItems: NavItem[];
  onClose: () => void;
}

export default function MobileNavigation({
  isOpen,
  navItems,
  onClose,
}: MobileNavigationProps) {
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const t = useTranslations('common.main_nav');
  const pathname = usePathname();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          id='mobile-navigation'
          initial={{ opacity: 0, filter: 'blur(10px)' }}
          animate={{ opacity: 1, filter: 'blur(0px)' }}
          exit={{ opacity: 0, filter: 'blur(10px)' }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className='text-default bg-background/40 absolute top-0 left-0 z-10 h-screen w-full pt-16 shadow-lg backdrop-blur-lg lg:hidden'
          role='navigation'
          aria-label='Mobile navigation menu'
        >
          <ul className='flex flex-col items-center space-y-4 px-6 py-4'>
            {navItems.map((item) => {
              const isActive =
                item.href === '/'
                  ? pathname === '/'
                  : pathname.startsWith(item.href) && pathname !== '/';
              return (
                <li key={item.id} className='w-full'>
                  <div className='flex w-full items-center justify-between'>
                    <Link
                      href={item.href}
                      className={cn(
                        `group relative flex w-full items-center justify-center py-1 text-center transition-colors`,
                        isActive && `font-bold`
                      )}
                      onClick={onClose}
                    >
                      {item.label}
                    </Link>
                  </div>
                </li>
              );
            })}
            <Link href={'/contact'}>
              <Button
                variant='default'
                className='w-full cursor-pointer text-white'
                onClick={onClose}
                aria-label='Contact us'
              >
                {t('contact_us')}
              </Button>
            </Link>
          </ul>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
