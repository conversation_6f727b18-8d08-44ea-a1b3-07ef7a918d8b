'use client';

import { usePathname } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import { IconCaretDownFilled } from '@tabler/icons-react';
import clsx from 'clsx';

interface NavItem {
  id: string;
  label: string;
  href: string;
  children?: NavItem[];
}

interface NavigationProps {
  navItems: NavItem[];
  scrolled: boolean;
}

export default function Navigation({ navItems, scrolled }: NavigationProps) {
  const pathname = usePathname();

  return (
    <nav>
      <ul className='flex gap-3 space-x-6 text-lg'>
        {navItems.map((item) => {
          const isActive =
            item.href === '/'
              ? pathname === '/'
              : pathname.startsWith(item.href) && pathname !== '/';

          return (
            <li key={item.id} className='relative py-7'>
              <Link
                href={item.href}
                className={`group relative flex items-center gap-2 transition-colors ${
                  isActive
                    ? `font-bold`
                    : `${!scrolled ? 'hover:text-white' : 'hover:text-primary'}`
                } `}
              >
                {item.label}
                {item.children && <IconCaretDownFilled />}
                {/* Underline effect on hover */}
                <div
                  className={clsx(
                    'absolute -bottom-2 left-0 h-[2px] w-full scale-x-0 transition-transform duration-300 group-hover:scale-x-100',
                    !scrolled ? 'bg-white' : 'bg-primary'
                  )}
                />
              </Link>

              {item.children && (
                <ul className='invisible absolute left-0 mt-4 w-60 overflow-hidden rounded-lg bg-white shadow-lg transition-opacity group-hover:visible group-hover:opacity-100'>
                  {item.children.map((child) => {
                    const isChildActive = pathname === child.href;

                    return (
                      <li key={child.id}>
                        <Link
                          href={child.href}
                          className={`text-default block border-l-2 border-white px-4 py-4 transition-colors ${
                            isChildActive
                              ? 'border-primary text-primary font-bold'
                              : 'hover:bg-primary hover:text-primary hover:border-primary hover:bg-opacity-25'
                          }`}
                        >
                          {child.label}
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              )}
            </li>
          );
        })}
      </ul>
    </nav>
  );
}
