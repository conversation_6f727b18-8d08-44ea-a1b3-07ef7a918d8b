'use client';
import { Button } from '@/components/common/Button';
import { useLayout } from '@/components/context/LayoutContext';
import { Link } from '@/i18n/navigation';
import { IconMenuDeep } from '@tabler/icons-react';
import clsx from 'clsx';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useEffect, useState, useMemo } from 'react';
import { getAssetUrl, getCdnUrl } from '@/utils/url/asset';
import MobileNavigation from './MobileNavigaton';
import Navigation from './Navigation';
import { motion } from 'framer-motion';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import HeaderPreload from './HeaderPreload';

export default function Header({ navigation, media }: { navigation: any[]; media: any }) {
  const { isDisableHeaderScroll } = useLayout();
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations('common.main_nav');
  const [scrolled, setScrolled] = useState(isDisableHeaderScroll); // default false

  // Memoize nav items to prevent unnecessary re-renders
  const navItems = useMemo(
    () =>
      navigation?.map((nav: any) => ({
        ...(nav?.item || {}),
        label: t(nav?.item?.label),
      })),
    [navigation, t]
  );

  // Memoize logo URLs
  const logoUrls = useMemo(
    () => ({
      desktop: getAssetUrl(media?.['white_logo']) || getCdnUrl('/logo/logo-white.svg'),
      mobile:
        getAssetUrl(media?.['white_short_logo']) ||
        getCdnUrl('/logo/logo_short-white.svg'),
    }),
    [media]
  );

  useEffect(() => {
    setScrolled(false);
    let handleScroll = undefined;
    if (!isDisableHeaderScroll && typeof window !== 'undefined') {
      handleScroll = () => {
        setScrolled(window.scrollY > 100);
      };
      window.addEventListener('scroll', handleScroll);
      // Check initial scroll position
      if (window.scrollY > 100) {
        setScrolled(true);
      }
    } else {
      setScrolled(true);
    }
    return () => {
      if (typeof handleScroll === 'function' && typeof window !== 'undefined')
        window.removeEventListener('scroll', handleScroll);
    };
  }, [isDisableHeaderScroll]);

  const { width } = useWindowDimensions();

  return (
    <>
      {/* Preload critical resources */}
      <HeaderPreload logoUrl={logoUrls.desktop} shortLogoUrl={logoUrls.mobile} />

      <motion.header className='fixed top-0 left-0 z-[100] flex w-screen items-center justify-center px-4'>
        <motion.div
          layout
          animate={{
            y: scrolled ? 10 : 0,
            border: scrolled && !isOpen ? '1px solid #FFFFFF3D' : '1px solid transparent',
            width:
              scrolled && !isOpen ? (width && width > 1280 ? '1536px' : '100%') : '100%',
            backdropFilter: scrolled && !isOpen ? 'blur(18px)' : 'none',
            borderRadius: '24px',
            backgroundColor: scrolled && !isOpen ? '#00224367' : '#00224300',
          }}
          transition={{
            duration: 0.3,
          }}
          className={`z-20 flex items-center justify-center px-4 py-2 transition-all duration-300 will-change-transform ${
            scrolled && !isOpen ? 'shadow-md' : 'bg-none text-white'
          }`}
        >
          <div className='container flex items-center justify-between'>
            <Link href='/' aria-label='Vietnam Silicon' title='Vietnam Silicon'>
              <h1 className='hidden'>Vietnam Silicon</h1>
              {/* Desktop Logo - Optimized for LCP */}
              <div className='relative hidden h-[54px] w-[200px] lg:block'>
                <Image
                  unoptimized
                  src={logoUrls.desktop}
                  alt='Vietnam Silicon'
                  fill
                  className={clsx('object-contain lg:scale-100')}
                  priority
                  loading='eager'
                  fetchPriority='high'
                  sizes='200px'
                  quality={90}
                  placeholder='blur'
                  blurDataURL='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjU0IiB2aWV3Qm94PSIwIDAgMjAwIDU0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iNTQiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIi8+PC9zdmc+'
                  style={{
                    contentVisibility: 'auto',
                    containIntrinsicSize: '200px 54px',
                  }}
                />
              </div>
              {/* Mobile Logo - Optimized for LCP */}
              <div className='relative block h-[40px] w-[80px] lg:hidden'>
                <Image
                  unoptimized
                  src={logoUrls.mobile}
                  alt='Vietnam Silicon'
                  fill
                  className={clsx('object-contain lg:scale-100')}
                  priority
                  loading='eager'
                  fetchPriority='high'
                  sizes='80px'
                  quality={90}
                  placeholder='blur'
                  blurDataURL='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA4MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iODAiIGhlaWdodD0iNDAiIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4xIi8+PC9zdmc+'
                  style={{
                    contentVisibility: 'auto',
                    containIntrinsicSize: '80px 40px',
                  }}
                />
              </div>
            </Link>

            <div className='hidden items-center gap-8 lg:flex'>
              <Navigation navItems={navItems} scrolled={scrolled} />
              <Link href={'/contact'} className='flex-none'>
                <Button variant='default' className='!px-6' aria-label='Contact us'>
                  {t('contact_us')}
                </Button>
              </Link>
            </div>
            <button
              className={`cursor-pointer focus:outline-none lg:hidden ${!scrolled && !isOpen ? 'text-white' : 'text-default'}`}
              onClick={() => setIsOpen(!isOpen)}
              aria-label={isOpen ? 'Close navigation menu' : 'Open navigation menu'}
              aria-expanded={isOpen}
              aria-controls='mobile-navigation'
            >
              <IconMenuDeep />
            </button>
          </div>
        </motion.div>

        {/* Mobile Menu */}
        <MobileNavigation
          navItems={navItems}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />
      </motion.header>
    </>
  );
}
