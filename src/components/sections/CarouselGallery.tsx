'use client';
import { Glasscard } from '@/components/common/Card';
import { Section } from '@/components/common/Section';
import Tag from '@/components/common/Tag';
import { getAssetUrl } from '@/utils/url/asset';
import { AnimatePresence, motion, useInView } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { getTranslatedData } from '@/hooks/useTranslatedData';
import { CarouselButton } from '@/components/common/CarouselButton';
import BlurShine from '../common/BlurShine';
import { ScrollMotionDiv } from '../motion/ScrollMotionDiv';
export default function CarouselGallery({ carouselGroups }: { carouselGroups: any }) {
  const carouselGroup = carouselGroups?.data?.[0]?.items;
  const [activeIndex, setActiveIndex] = useState(0);
  const t = useTranslations('about');
  const locale = useLocale();

  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);
  const [animatedNumber, setAnimatedNumber] = useState(0);

  const sectionRef = useRef<HTMLDivElement>(null);

  const isInView = useInView(sectionRef);

  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying && isInView) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, isInView]);
  useEffect(() => {
    // Reset animation when slide changes
    progressRef.current = 0;
    setAnimatedNumber(0);
  }, [activeIndex]);
  useEffect(() => {
    if (Math.floor(animatedNumber) === 9) {
      setActiveIndex(
        activeIndex === carouselGroups?.data?.[0]?.items?.length - 1 ? 0 : activeIndex + 1
      );
    }
  }, [Math.floor(animatedNumber)]);

  return (
    <>
      <BlurShine size={1600} />
      <Section
        center
        className='relative !mx-0 mt-20 px-0 max-lg:h-[700px] lg:aspect-[1920/885]'
      >
        <AnimatePresence mode='wait'>
          <motion.img
            initial={{ opacity: 0, filter: 'blur(10px)', scale: 0.99 }}
            animate={{ opacity: 1, filter: 'blur(0px)', scale: 1 }}
            exit={{ opacity: 0, filter: 'blur(10px)', scale: 0.99 }}
            transition={{ duration: 0.25 }}
            key={`${activeIndex}-image`}
            layoutId={`${activeIndex}-image`}
            src={getAssetUrl(carouselGroup[activeIndex].item.asset.filename_disk)}
            alt={`${
              getTranslatedData(carouselGroup[activeIndex].item.translations, locale)
                ?.title || 'Our approach'
            }`}
            className='h-full w-screen bg-blue-500 object-cover'
          />
        </AnimatePresence>
        <motion.div
          ref={sectionRef}
          className='absolute inset-0 z-[1] size-full'
          style={{
            background:
              'linear-gradient(90deg, rgba(0, 34, 67, 0) 0%, rgba(0, 34, 67, 0.8) 59.17%)',
          }}
        ></motion.div>
        <ScrollMotionDiv
          transformFrom={[0, 0.5, 1]}
          transformTo={[-40, 0, 40]}
          className='absolute inset-0 z-[2] flex size-full items-center justify-center px-4'
        >
          <div className='center container flex items-center justify-end'>
            <motion.div layout className='flex flex-col gap-4'>
              <Glasscard
                className='max-w-[624px] bg-white/16 p-4 lg:p-10'
                borderColor='#FFFFFF54'
                autoRotate
              >
                <AnimatePresence mode='popLayout'>
                  <motion.div
                    key={`${activeIndex}-card`}
                    layoutId={`${activeIndex}-card`}
                    initial={{ opacity: 0, filter: 'blur(10px)' }}
                    animate={{ opacity: 1, filter: 'blur(0px)' }}
                    exit={{ opacity: 0, filter: 'blur(10px)' }}
                    transition={{ duration: 0.5 }}
                    className='flex flex-col items-start gap-3'
                  >
                    <Tag className='bg-[#FFFFFF0A]'>
                      {' '}
                      {
                        getTranslatedData(
                          carouselGroup[activeIndex].item.translations,
                          locale
                        )?.subtitle
                      }
                    </Tag>
                    <h2 className='text-4xl font-bold'>
                      {
                        getTranslatedData(
                          carouselGroup[activeIndex].item.translations,
                          locale
                        )?.title
                      }
                    </h2>
                    <p className='text-base text-white/70'>
                      {
                        getTranslatedData(
                          carouselGroup[activeIndex].item.translations,
                          locale
                        )?.description
                      }
                    </p>
                  </motion.div>
                </AnimatePresence>
              </Glasscard>
              <div className='flex w-full items-center justify-between'>
                <div className='flex gap-2'>
                  <CarouselButton
                    direction='left'
                    onClick={() =>
                      setActiveIndex(
                        activeIndex === 0
                          ? carouselGroups?.data?.[0]?.items?.length - 1
                          : activeIndex - 1
                      )
                    }
                  />
                  <CarouselButton
                    direction='right'
                    onClick={() =>
                      setActiveIndex(
                        activeIndex === carouselGroups?.data?.[0]?.items?.length - 1
                          ? 0
                          : activeIndex + 1
                      )
                    }
                  />
                </div>
                <div className='text-base text-white'>
                  <AnimatePresence mode='popLayout'>
                    <motion.span
                      key={activeIndex}
                      initial={{ opacity: 0, filter: 'blur(10px)' }}
                      animate={{ opacity: 1, filter: 'blur(0px)' }}
                      exit={{ opacity: 0, filter: 'blur(10px)' }}
                      transition={{ duration: 0.5 }}
                    >
                      {activeIndex + 1}
                    </motion.span>
                  </AnimatePresence>
                  /{carouselGroups?.data?.[0]?.items?.length}
                </div>
              </div>
            </motion.div>
          </div>
        </ScrollMotionDiv>
      </Section>
    </>
  );
}
