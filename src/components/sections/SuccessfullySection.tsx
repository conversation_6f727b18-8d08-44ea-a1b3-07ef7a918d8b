'use client';
import { Button } from '@/components/common/Button';
import { Section } from '@/components/common/Section';
import { AuroraBackground } from '@/components/layout/AuroraBackground';
import TextReveal from '@/components/motion/TextReveal';
import { getCdnUrl } from '@/utils/url/asset';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';

interface SuccessfullySectionProps {
  title: string;
  description: string;
  buttonText: string;
  link?: string;
}

export default function SuccessfullySection({
  title,
  description,
  buttonText,
  link,
}: SuccessfullySectionProps) {
  const t = useTranslations();

  return (
    <AuroraBackground className='bg-background relative z-0 h-[90vh] w-full overflow-hidden'>
      <Section
        center
        className='relative z-10 flex h-full flex-col items-center justify-center gap-6 text-center'
      >
        <div
          className='absolute top-1/2 right-0 -z-10 h-full w-full -translate-y-1/2 opacity-20'
          style={{
            maskImage:
              'linear-gradient(45deg, transparent 0%, transparent 35%, black 60%)',
          }}
        >
          <motion.img
            src='/logo-3d.webp'
            alt='hero-image'
            className='size-full object-contain'
          />
        </div>

        <motion.svg className='size-40' viewBox='0 0 100 100'>
          <motion.path
            d='M20 50 L40 70 L80 30'
            stroke='#34C759'
            strokeWidth='8'
            strokeLinecap='round'
            strokeLinejoin='round'
            fill='none'
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          />
        </motion.svg>

        <h3 className='text-4xl leading-11 font-bold text-gray-50'>
          {t.rich(title, {
            span: (children) => <span className='text-primary'>{children}</span>,
          })}
        </h3>
        <TextReveal className='text-lg leading-7 font-medium text-gray-500'>
          {t(description)}
        </TextReveal>
        <Link href={link || '#'}>
          <Button
            size='lg'
            className='h-auto py-4 text-base leading-6 font-semibold transition-all hover:opacity-85'
            aria-label={buttonText}
          >
            {t(buttonText)}
          </Button>
        </Link>
      </Section>
    </AuroraBackground>
  );
}
