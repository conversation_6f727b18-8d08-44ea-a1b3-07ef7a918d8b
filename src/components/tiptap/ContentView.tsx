'use client';

import { EditorContent, useEditor } from '@tiptap/react';
import Extensions from './extensions';
interface IContentViewProps {
  content: string;
}

function ContentView({ content }: IContentViewProps) {
  const editor = useEditor({
    extensions: Extensions,
    editable: false,
    content: content,
    immediatelyRender: false,
  });

  return <EditorContent editor={editor} translate='yes' />;
}

export default ContentView;
