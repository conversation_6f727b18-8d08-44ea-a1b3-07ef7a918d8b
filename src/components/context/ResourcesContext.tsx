'use client';

import { type Post } from '@/types/resources';
import {
  createContext,
  PropsWithChildren,
  useState,
  Dispatch,
  SetStateAction,
  useContext,
} from 'react';

type ResourcesContextProps = {
  highlightResources: Post[] | undefined;
  setHighlightResources: Dispatch<SetStateAction<Post[] | undefined>>;
  currentTab: string;
  setCurrentTab: Dispatch<SetStateAction<string>>;
  popularVideos: Post[][] | undefined;
  setPopularVideos: Dispatch<SetStateAction<Post[][] | undefined>>;
};

const ResourcesContext = createContext<ResourcesContextProps | undefined>(undefined);

export default function ResourcesProvider({ children }: PropsWithChildren) {
  const [highlightResources, setHighlightResources] = useState<Post[] | undefined>(
    undefined
  );
  const [popularVideos, setPopularVideos] = useState<Post[][] | undefined>(undefined);
  const [currentTab, setCurrentTab] = useState('all');

  return (
    <ResourcesContext
      value={{
        highlightResources,
        setHighlightResources,
        currentTab,
        setCurrentTab,
        popularVideos,
        setPopularVideos,
      }}
    >
      {children}
    </ResourcesContext>
  );
}

export const useResources = () => {
  const resources = useContext(ResourcesContext);

  if (!resources) throw new Error('useResources must be used within a Provider');

  return resources;
};
