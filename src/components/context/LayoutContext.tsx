'use client';
import { createContext, useContext, useState, ReactNode } from 'react';

// Define the shape of the context
interface LayoutContextProps {
  isDisableHeaderScroll: boolean;
  setIsDisableHeaderScroll: (status: boolean) => void;
}

// Create the context with default values
const LayoutContext = createContext<LayoutContextProps | undefined>(undefined);

interface LayoutProviderProps {
  children: ReactNode;
}

export const LayoutProvider = ({ children }: LayoutProviderProps) => {
  const [isDisableHeaderScroll, setIsDisableHeaderScroll] = useState<boolean>(false);

  return (
    <LayoutContext.Provider value={{ isDisableHeaderScroll, setIsDisableHeaderScroll }}>
      {children}
    </LayoutContext.Provider>
  );
};

// Custom hook to use the context
export const useLayout = (): LayoutContextProps => {
  const context = useContext(LayoutContext);
  if (!context) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
};
