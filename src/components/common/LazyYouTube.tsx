'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { cn } from '@/utils/cn';

interface LazyYouTubeProps {
  videoId: string;
  title: string;
  className?: string;
  aspectRatio?: '16/9' | '4/3' | '1/1';
  loop?: boolean;
  playlist?: string;
  thumbnail?: string;
}

export default function LazyYouTube({
  videoId,
  title,
  className = '',
  aspectRatio = '16/9',
  loop = false,
  playlist,
  thumbnail,
}: LazyYouTubeProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case '4/3':
        return 'aspect-[4/3]';
      case '1/1':
        return 'aspect-square';
      default:
        return 'aspect-video';
    }
  };

  const getYouTubeUrl = () => {
    const baseUrl = `https://www.youtube.com/embed/${videoId}`;
    const params = new URLSearchParams();

    if (loop) {
      params.append('loop', '1');
      if (playlist) {
        params.append('playlist', playlist);
      } else {
        params.append('playlist', videoId);
      }
    }

    return `${baseUrl}?${params.toString()}`;
  };

  return (
    <div
      className={`relative ${getAspectRatioClass()} ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Facade - shown before loading */}
      {!isLoaded && (
        <motion.div
          className='group absolute inset-0 flex cursor-pointer items-center justify-center rounded-xl bg-gray-200/20'
          onClick={handleLoad}
          role='button'
          tabIndex={0}
          aria-label={`Play ${title} video`}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleLoad();
            }
          }}
        >
          {thumbnail && (
            <Image
              unoptimized
              src={thumbnail}
              alt='Vietnam Silicon'
              fill
              className={cn('size-full rounded-xl object-cover')}
              priority
              loading='eager'
              fetchPriority='high'
            />
          )}
          {/* Play button */}
          <motion.div
            className='relative z-10 flex h-20 w-20 items-center justify-center rounded-full bg-red-600 shadow-lg'
            whileHover={{ scale: 1.1 }}
            animate={{ scale: isHovered ? 1.05 : 1 }}
            transition={{ duration: 0.2 }}
          >
            <div className='ml-1 h-0 w-0 border-t-[8px] border-b-[8px] border-l-[12px] border-t-transparent border-b-transparent border-l-white' />
          </motion.div>

          {/* Video title */}
          <div className='absolute right-4 bottom-4 left-4'>
            <h3 className='truncate text-lg font-semibold text-white'>{title}</h3>
          </div>
        </motion.div>
      )}

      {/* Actual iframe - loaded on demand */}
      {isLoaded && (
        <motion.iframe
          className='absolute inset-0 h-full w-full rounded-xl'
          src={`${getYouTubeUrl()}&autoplay=1`}
          title={title}
          frameBorder='0'
          allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
          allowFullScreen
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />
      )}
    </div>
  );
}
