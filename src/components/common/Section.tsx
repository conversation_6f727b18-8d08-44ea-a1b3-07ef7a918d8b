import { forwardRef } from 'react';
import { cn } from '@/utils/cn';

export const Section = forwardRef<
  HTMLDivElement,
  {
    children: React.ReactNode;
    className?: React.HTMLAttributes<HTMLDivElement>['className'];
    container?: boolean;
    center?: boolean;
  }
>(({ children, className, container, center }, ref) => {
  return (
    <section
      ref={ref}
      className={cn(
        'mx-auto w-full px-4',
        container && 'container',
        center && 'flex flex-col items-center',
        className
      )}
    >
      {children}
    </section>
  );
});

Section.displayName = 'Section';
