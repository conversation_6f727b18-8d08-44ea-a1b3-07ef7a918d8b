'use client';
import { cn } from '@/utils/cn';
import {
  HTMLMotionProps,
  motion,
  TargetAndTransition,
  useInView,
  VariantLabels,
} from 'framer-motion';
import * as React from 'react';
import { useEffect, useRef, useState } from 'react';
function Card({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot='card'
      className={cn(
        'text-foreground bg-card-container/5 flex flex-col gap-6 rounded-xl border border-white/10 py-6',
        className
      )}
      {...props}
    />
  );
}

function CardData({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot='card'
      className={cn(
        'text-foreground bg-card-data-bg/16 flex flex-col rounded-xl border border-white/10 p-4',
        className
      )}
      {...props}
    />
  );
}

function CardHeader({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot='card-header'
      className={cn(
        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',
        className
      )}
      {...props}
    />
  );
}

function CardTitle({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot='card-title'
      className={cn('leading-none font-semibold', className)}
      {...props}
    />
  );
}

function CardDescription({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot='card-description'
      className={cn('text-muted-foreground text-sm', className)}
      {...props}
    />
  );
}

function CardAction({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot='card-action'
      className={cn(
        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',
        className
      )}
      {...props}
    />
  );
}

function CardContent({ className, ...props }: React.ComponentProps<'div'>) {
  return <div data-slot='card-content' className={cn('px-6', className)} {...props} />;
}

function CardFooter({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot='card-footer'
      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}
      {...props}
    />
  );
}

type GlasscardProps = HTMLMotionProps<'div'> & {
  borderWidth?: number;
  borderColor?: string;
  children: React.ReactNode;
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
  autoRotate?: boolean | { duration: number };
  borderRadius?: number;
  shadowColor?: string;
  isShadow?: boolean;
  isBorder?: boolean;
  borderZIndex?: number;
  layoutId?: string;
  animate?: boolean | TargetAndTransition | VariantLabels | undefined;
  childContainerClassName?: React.HTMLAttributes<HTMLDivElement>['className'];
  transition?: any;
  layout?: boolean;
  isForCardSection?: boolean;
};
function Glasscard({
  className,
  borderWidth = 1,
  borderColor = 'var(--color-primary)',
  shadowColor = 'var(--blue-600)',
  autoRotate = false,
  borderRadius = 16,
  isShadow = true,
  borderZIndex = 2,
  isBorder = true,
  layoutId,
  animate,
  childContainerClassName = '',
  transition,
  layout = true,
  isForCardSection = false,
  ...props
}: GlasscardProps) {
  const [degree, setDegree] = useState(229);
  const cardRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const isInView = useInView(cardRef);
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!cardRef.current) return;
      // if (!isInView) return;
      const rect = cardRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      setPosition({ x, y });

      // Calculate angle based on mouse position relative to center
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      if (!autoRotate) {
        const angle = Math.atan2(y - centerY, x - centerX) * (180 / Math.PI);

        // Use the current degree as the starting point for the animation
        setDegree(angle + 90);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, [autoRotate, degree]);

  return (
    <>
      <motion.div
        ref={cardRef}
        className={cn(
          'bg-card-container/5 z-10 flex flex-col transition-[border-radius] will-change-transform',
          isForCardSection && '!rounded-t-none border-t border-t-white',
          className
        )}
        style={{
          borderRadius,
        }}
        initial={{ backdropFilter: 'blur(0px)' }}
        whileInView={{ backdropFilter: 'blur(10px)' }}
        viewport={{ once: true }}
        animate={animate}
        layout={layout}
        transition={{
          ...transition,
          backdropFilter: {
            duration: 1,
            ease: 'easeInOut',
          },
        }}
        layoutId={layoutId}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        {...props}
      >
        {isInView && isBorder && (
          <>
            <motion.div
              initial={{
                opacity: autoRotate ? 1 : 0,
              }}
              animate={{
                opacity: autoRotate || isHovered ? 1 : 0,
                background: autoRotate
                  ? [
                      `linear-gradient(0deg, #FFFFFF00 0%, ${borderColor} 100%)`,
                      `linear-gradient(360deg, #FFFFFF00 0%, ${borderColor} 100%)`,
                    ]
                  : undefined,
              }}
              transition={
                autoRotate
                  ? {
                      duration: typeof autoRotate === 'boolean' ? 5 : autoRotate.duration,
                      repeat: Infinity,
                      ease: 'linear',
                    }
                  : undefined
              }
              style={{
                padding: borderWidth,
                borderRadius,
                ...(!autoRotate && {
                  background: `linear-gradient(${degree}deg, #FFFFFF00 0%, ${borderColor}  100%)`,
                }),
                zIndex: borderZIndex,
              }}
              className={cn(
                'absolute inset-0 mask-[linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)] ![mask-composite:exclude] transition-[border-radius] will-change-transform content-[""] [-webkit-mask-composite:xor] [-webkit-mask:linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)]',
                isForCardSection && '!rounded-t-none'
              )}
            ></motion.div>
          </>
        )}
        <div className={cn('relative z-[3] h-full', childContainerClassName)}>
          {props.children}
        </div>
        {isShadow && isInView && (
          <div
            style={{
              borderRadius,
            }}
            className={cn(
              'absolute inset-0 z-[1] size-full overflow-hidden transition-[border-radius] duration-300',
              isForCardSection && '!rounded-t-none'
            )}
          >
            <motion.div
              animate={{
                x: position.x,
                y: position.y,
              }}
              transition={{
                duration: 0,
                ease: 'linear',
              }}
              style={{
                background: `radial-gradient(circle,${shadowColor} 0%, transparent 100%)`,
                borderRadius,
              }}
              className={cn(
                'absolute -inset-[150px] z-[1] size-[300px] rounded-full opacity-10 blur-3xl transition-[border-radius] will-change-transform',
                isForCardSection && '!rounded-t-none'
              )}
            ></motion.div>
          </div>
        )}
      </motion.div>
    </>
  );
}

export {
  Card,
  CardAction,
  CardContent,
  CardData,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Glasscard,
};
