import { cn } from '@/utils/cn';
import Tag from './Tag';
import TextReveal from '../motion/TextReveal';
import ScrollReveal from '../motion/ScrollReveal';
export default function HeroTitle({
  title,
  description,
  tag,
  className,
  titleClassName,
  descriptionClassName,
}: {
  title: React.ReactNode;
  description: React.ReactNode;
  tag?: React.ReactNode;
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
  titleClassName?: React.HTMLAttributes<HTMLDivElement>['className'];
  descriptionClassName?: React.HTMLAttributes<HTMLDivElement>['className'];
}) {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center gap-5 text-center',
        className
      )}
    >
      {tag && (
        <ScrollReveal delay={0.4}>
          <Tag>{tag}</Tag>
        </ScrollReveal>
      )}
      <TextReveal
        className={cn(
          'mt-1 text-3xl leading-tight font-bold md:text-4xl',
          titleClassName
        )}
      >
        {title}
      </TextReveal>
      <TextReveal
        delay={0.7}
        stepDuration={0.01}
        className={cn(
          'max-w-[520px] text-sm opacity-70 md:text-base',
          descriptionClassName
        )}
      >
        {description}
      </TextReveal>
    </div>
  );
}
