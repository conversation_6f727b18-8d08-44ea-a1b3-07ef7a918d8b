'use client';
import { cn } from '@/utils/cn';
import { animate, motion, useMotionValue, useTransform } from 'framer-motion';
import { HTMLAttributes, useEffect, useMemo, useRef } from 'react';

type props = {
  size: number | string;
  length?: number;
  color: string[];
  duration?: number;
  durationMask?: number;
  speed?: number;
  ratioColor?: number[];
  clockRotate?: boolean;
  className?: HTMLAttributes<HTMLDivElement>['className'];
};

export default function CircleRounding({
  size,
  length = 3,
  color,
  ratioColor = [2, 3],
  duration = 30,
  speed = 1,
  clockRotate = true,
  durationMask = 10,
  className,
}: props) {
  const animationsRef = useRef<Array<{ stop: () => void }>>([]);

  const count = useMotionValue(0);
  const countDeg = useMotionValue(0);
  const countDeg2 = useMotionValue(0);

  const firstColor = useTransform(count, [0, 1], [color[0], color[1]]);
  const secondColor = useTransform(count, [0, 1], [color[1], color[0]]);

  // Mask transforms - create all possible transforms at top level
  const maskTransformA1 = useTransform(countDeg2, [0, -360, -720], [100, 360, 100]);
  const maskTransformB1 = useTransform(countDeg2, [0, -360, -720], [720, 360, 0]);
  const maskTransformA2 = useTransform(countDeg2, [0, 360, 720], [100, 360, 100]);
  const maskTransformB2 = useTransform(countDeg2, [360, 720], [0, 360]);

  // Memoize static calculations
  const { firstSectionIndices, secondSectionIndices } = useMemo(() => {
    const firstSectionIndices = Array.from({ length: ratioColor[0] }, (_, i) => i);
    const secondSectionIndices = Array.from({ length: ratioColor[1] }, (_, i) => i);

    return { firstSectionIndices, secondSectionIndices };
  }, [ratioColor]);

  const { maskA, maskB } = useMemo(() => {
    if (!clockRotate) {
      return { maskA: maskTransformA1, maskB: maskTransformB1 };
    }
    return { maskA: maskTransformA2, maskB: maskTransformB2 };
  }, [clockRotate, maskTransformA1, maskTransformB1, maskTransformA2, maskTransformB2]);

  useEffect(() => {
    animationsRef.current.forEach((animation) => animation.stop());
    animationsRef.current = [];

    const colorAnimation = animate(0, 1, {
      duration: 0.3,
      repeat: Infinity,
      repeatType: 'reverse',
      ease: 'linear',
      repeatDelay: speed,
      onUpdate: (latest: number) => count.set(latest),
    });

    const rotationAnimation = animate(0, clockRotate ? 360 : -360, {
      duration,
      repeat: Infinity,
      ease: 'linear',
      onUpdate: (latest: number) => countDeg.set(latest),
    });

    const maskAnimation = animate(0, clockRotate ? 720 : -720, {
      duration: durationMask,
      repeat: Infinity,
      ease: 'linear',
      onUpdate: (latest: number) => countDeg2.set(latest),
    });

    animationsRef.current = [colorAnimation, rotationAnimation, maskAnimation];

    return () => {
      animationsRef.current.forEach((animation) => animation.stop());
    };
  }, [speed, duration, durationMask, clockRotate, count, countDeg, countDeg2]);

  const background = useTransform(() => {
    const firstColorValue = firstColor.get();
    const secondColorValue = secondColor.get();
    const degValue = countDeg.get();

    // Pre-calculate section strings
    const firstSections = firstSectionIndices
      .map((index) => {
        const startDeg = index * 2 * length;
        const midDeg = (index * 2 + 1) * length;
        const endDeg = (index * 2 + 2) * length;
        return `, transparent ${startDeg}deg ${midDeg}deg, ${firstColorValue} ${midDeg}deg ${endDeg}deg`;
      })
      .join('');

    const secondSections = secondSectionIndices
      .map((index) => {
        const baseOffset = ratioColor[0] * 2;
        const startDeg = (baseOffset + index * 2) * length;
        const midDeg = (baseOffset + index * 2 + 1) * length;
        const endDeg = (baseOffset + index * 2 + 2) * length;
        return `, transparent ${startDeg}deg ${midDeg}deg, ${secondColorValue} ${midDeg}deg ${endDeg}deg`;
      })
      .join('');

    return `repeating-conic-gradient(from ${degValue}deg${firstSections}${secondSections})`;
  });

  const backgroundMask = useTransform(() => {
    const aValue = maskA.get();
    const bValue = maskB.get();
    return `conic-gradient(from ${bValue}deg, black 0deg ${aValue}deg, transparent 0deg 360deg)`;
  });

  // Memoize transition object
  const rotationTransition = useMemo(
    () => ({
      repeat: Infinity,
      duration: duration,
      ease: 'linear' as const,
    }),
    [duration]
  );

  return (
    <motion.div
      className={cn('relative', className)}
      style={{
        width: size,
        aspectRatio: 1,
        WebkitMaskImage: backgroundMask,
        maskImage: backgroundMask,
      }}
    >
      <motion.div
        className='absolute inset-0 rounded-full mask-[linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)] ![mask-composite:exclude] p-0.5 content-[""] [-webkit-mask-composite:xor] [-webkit-mask:linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)]'
        style={{ background }}
        animate={{ rotate: clockRotate ? 360 : -360 }}
        transition={rotationTransition}
      />
    </motion.div>
  );
}
