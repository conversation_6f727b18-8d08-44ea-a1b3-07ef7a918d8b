'use client';

import { motion } from 'framer-motion';

export default function BlurShine({
  size = 2000,
  blur = 300,
  direction = 'left',
  color = 'var(--color-blue-900)',
}: {
  size?: number;
  blur?: number;
  direction?: 'left' | 'right';
  color?: string;
}) {
  return (
    <motion.div className='relative -z-10 w-full opacity-30'>
      <motion.div
        className='absolute h-fit w-full overflow-hidden rounded-full'
        style={{
          width: size,
          height: size,
          ...(direction === 'left' && { left: -size / 2 }),
          ...(direction === 'right' && { right: -size / 2 }),
          top: -size / 2,
        }}
      >
        <div
          className='size-full'
          style={{
            background: `radial-gradient(circle, ${color} 0%, transparent 70%, transparent 100%)`,
          }}
        ></div>
      </motion.div>
    </motion.div>
  );
}
