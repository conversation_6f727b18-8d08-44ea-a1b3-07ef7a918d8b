import * as React from 'react';

import { Slot } from '@radix-ui/react-slot';
import { cn } from '@/utils/cn';
import { IconChevronRight, IconDots } from '@tabler/icons-react';
import Link from 'next/link';

const Breadcrumb = React.forwardRef<
  HTMLElement,
  React.ComponentPropsWithoutRef<'nav'> & {
    separator?: React.ReactNode;
  }
>(({ ...props }, ref) => <nav ref={ref} aria-label='breadcrumb' {...props} />);
Breadcrumb.displayName = 'Breadcrumb';
type BreadcrumbListProps = {
  items: {
    href?: string;
    label: string;
    //Using Tabler Icon
    icon?: React.ComponentType<{ size?: number }>;
  }[];
  className?: string;
} & React.ComponentPropsWithoutRef<'ol'>;

const BreadcrumbList = ({ items, className, ...props }: BreadcrumbListProps) => {
  return (
    <ol
      className={cn(
        'text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5',
        className
      )}
      {...props}
    >
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <BreadcrumbItem>
            {index === items.length - 1 ? (
              <BreadcrumbPage>
                {item.icon && (
                  <span className='mr-2'>
                    <item.icon size={18} />
                  </span>
                )}
                {item.label}
              </BreadcrumbPage>
            ) : (
              <BreadcrumbLink href={item.href || ''}>
                {item.icon && (
                  <span className='mr-2'>
                    <item.icon size={18} />
                  </span>
                )}
                {item.label}
              </BreadcrumbLink>
            )}
          </BreadcrumbItem>
          {index < items.length - 1 && <BreadcrumbSeparator />}
        </React.Fragment>
      ))}
    </ol>
  );
};
BreadcrumbList.displayName = 'BreadcrumbList';

const BreadcrumbItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentPropsWithoutRef<'li'>
>(({ className, ...props }, ref) => (
  <li
    ref={ref}
    className={cn('inline-flex items-center gap-1.5', className)}
    {...props}
  />
));
BreadcrumbItem.displayName = 'BreadcrumbItem';

const BreadcrumbLink = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentPropsWithoutRef<typeof Link> & {
    asChild?: boolean;
  }
>(({ asChild, className, ...props }, ref) => {
  if (asChild) {
    return (
      <Slot
        ref={ref}
        className={cn(
          'hover:text-foreground flex items-center transition-colors',
          className
        )}
      />
    );
  }

  return (
    <Link
      ref={ref}
      className={cn(
        'hover:text-foreground flex items-center transition-colors',
        className
      )}
      {...props}
    />
  );
});
BreadcrumbLink.displayName = 'BreadcrumbLink';

const BreadcrumbPage = React.forwardRef<
  HTMLSpanElement,
  React.ComponentPropsWithoutRef<'span'>
>(({ className, ...props }, ref) => (
  <span
    ref={ref}
    role='link'
    aria-disabled='true'
    aria-current='page'
    className={cn('text-foreground flex items-center font-normal', className)}
    {...props}
  />
));
BreadcrumbPage.displayName = 'BreadcrumbPage';

const BreadcrumbSeparator = ({
  children,
  className,
  ...props
}: React.ComponentProps<'li'>) => (
  <li
    role='presentation'
    aria-hidden='true'
    className={cn('[&>svg]:h-3.5 [&>svg]:w-3.5', className)}
    {...props}
  >
    {children ?? <IconChevronRight />}
  </li>
);
BreadcrumbSeparator.displayName = 'BreadcrumbSeparator';

const BreadcrumbEllipsis = ({ className, ...props }: React.ComponentProps<'span'>) => (
  <span
    role='presentation'
    aria-hidden='true'
    className={cn('flex h-9 w-9 items-center justify-center', className)}
    {...props}
  >
    <IconDots className='h-4 w-4' />
    <span className='sr-only'>More</span>
  </span>
);
BreadcrumbEllipsis.displayName = 'BreadcrumbElipssis';

export {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
};
