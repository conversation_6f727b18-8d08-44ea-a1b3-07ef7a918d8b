import {
  IconChevronDown,
  IconChevronLeft,
  IconChevronRight,
  IconChevronUp,
} from '@tabler/icons-react';

export const CarouselButton = ({
  direction,
  onClick,
  'aria-label': ariaLabel,
}: {
  direction: 'left' | 'right' | 'top' | 'bottom';
  onClick: () => void;
  'aria-label'?: string;
}) => {
  const icons = {
    left: IconChevronLeft,
    right: IconChevronRight,
    top: IconChevronUp,
    bottom: IconChevronDown,
  };

  const IconComponent = icons[direction];

  // Generate default aria-label if not provided
  const defaultAriaLabel = ariaLabel || `Navigate ${direction}`;

  return (
    <button
      className='size-10 cursor-pointer rounded-lg bg-white p-2 transition-all duration-300 hover:shadow-[5.92px_11.84px_23.68px_0px_#D3D1D84D]'
      onClick={onClick}
      aria-label={defaultAriaLabel}
    >
      <IconComponent color='#1B1F26B8' />
    </button>
  );
};
