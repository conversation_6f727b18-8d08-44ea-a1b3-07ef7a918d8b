'use client';
import { forwardRef, useEffect, useRef, useState } from 'react';

import { motion, useScroll, useSpring, useTransform } from 'framer-motion';

export default function SlashBackground({ h = 1000 }: { h: number }) {
  const divRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: divRef,
    offset: ['start end', 'end start'],
  });

  const blur = useTransform(scrollYProgress, [0, 0.3], ['blur(50px)', 'blur(0px)']);
  const scrollHeight = useTransform(scrollYProgress, [0, 0.8], [0, h + 400]);

  return (
    <motion.div className='relative w-full bg-amber-700'>
      <motion.div
        className='absolute inset-0 flex h-fit w-full flex-col items-center overflow-hidden'
        style={{ height: scrollHeight }}
      >
        <motion.div
          ref={divRef}
          style={{
            filter: blur,
            background:
              'linear-gradient(30deg, rgba(0, 34, 67, 0) 15%, rgba(50, 127, 239, 1) 50%, rgba(0, 34, 67, 0) 75%)',
          }}
          className='flex h-[200vh] w-100 flex-none skew-x-[-30deg] transform items-center justify-center bg-green-500'
        ></motion.div>
      </motion.div>
    </motion.div>
  );
}

export function SlashBackground2({ h = 1000 }: { h?: number }) {
  const divRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: divRef,
    offset: ['start end', 'end center'],
  });

  const scrollYProgressSpring = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001,
  });

  const percent = useTransform(scrollYProgressSpring, [0, 0.2, 0.5, 1], [100, 95, 40, 5]);
  const backgroundMask = useTransform(() => {
    return `linear-gradient(0deg,rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) ${percent.get() - 5}% , rgba(0, 0, 0, 1) ${percent.get() - 2}%)`;
  });
  return (
    <motion.div
      ref={divRef}
      style={{
        height: h,
        background:
          'linear-gradient(30deg, rgba(0, 34, 67, 0) 15%, rgba(50, 127, 239, 1) 50%, rgba(0, 34, 67, 0) 75%)',
        WebkitMaskImage: backgroundMask,
        maskImage: backgroundMask,
      }}
      className='absolute flex w-60 skew-x-[-30deg] transform items-center justify-center opacity-65 will-change-transform md:w-100'
    ></motion.div>
  );
}

export const SlashDiv = forwardRef<
  HTMLDivElement,
  {
    h?: number;
    className?: string;
    additionalHeight?: number;
  } & React.HTMLAttributes<HTMLDivElement>
>(({ h = 1000, className, children, additionalHeight = 0, ...props }, ref) => {
  const divRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<number>(0);

  useEffect(() => {
    const updateHeight = () => {
      if (divRef.current) {
        setHeight(divRef.current.offsetHeight);
      }
    };

    // Initial measurement
    updateHeight();

    // Add resize listener
    window.addEventListener('resize', updateHeight);

    // Cleanup
    return () => window.removeEventListener('resize', updateHeight);
  }, [divRef.current]);

  return (
    <motion.div
      ref={(node) => {
        // Handle both refs
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          ref.current = node;
        }
        divRef.current = node;
      }}
      className={`relative flex items-center justify-center ${className || ''}`}
    >
      <SlashBackground2 h={(height || h) + (additionalHeight || 0)} />
      {children}
    </motion.div>
  );
});
SlashDiv.displayName = 'SlashDiv';
