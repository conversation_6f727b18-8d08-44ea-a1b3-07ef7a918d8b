import { cn } from '@/utils/cn';

export default function Tag({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
}) {
  return (
    <div
      className={cn(
        'rounded-full border border-[#FFFFFF14] bg-[#FFFFFF0A] px-6 py-2 text-xs font-light tracking-[0.18rem] text-[#FFFFFF] uppercase backdrop-blur-xl',
        className
      )}
    >
      {children}
    </div>
  );
}
