import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

export default function ScrollReveal({
  children,
  className,
  delay = 0,
  duration = 0.5,
  y = 10,
  blurred,
  layout = false,
}: {
  children?: React.ReactNode;
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
  delay?: number;
  duration?: number;
  blurred?: boolean;
  y?: number;
  layout?: boolean;
}) {
  return (
    <motion.div
      initial={{
        filter: 'blur(20px)',
        y,
        // maskImage: 'linear-gradient(to top, #00000000 0%,  #00000000 100%)',
      }}
      whileInView={{
        opacity: 1,
        filter: 'blur(0px)',
        y: 0,
        // maskImage: 'linear-gradient(to top, #000000 0%,  #000000 100%)',
      }}
      transition={{
        duration,
        delay,
      }}
      viewport={{ once: true }}
      className={cn('will-change-transform', className)}
      style={{
        ...(blurred && { backdropFilter: 'blur(16px)' }),
      }}
      layout={layout}
    >
      {children}
    </motion.div>
  );
}
