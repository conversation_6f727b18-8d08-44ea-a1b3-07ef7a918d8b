'use client';
import { useEffect, useRef } from 'react';
import Lenis from '@studio-freight/lenis';

interface SmoothScrollProps {
  children: React.ReactNode;
}

export const SmoothScroll = ({ children }: SmoothScrollProps) => {
  const lenisRef = useRef<Lenis | null>(null);

  useEffect(() => {
    // Initialize Lenis for smooth scrolling with faster settings
    lenisRef.current = new Lenis({
      duration: 0,
      orientation: 'vertical',
      touchMultiplier: 2.5,
      wheelMultiplier: 1.5,
    });

    // Set up the animation frame to update Lenis
    function raf(time: number) {
      lenisRef.current?.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    return () => {
      // Clean up Lenis instance on unmount
      lenisRef.current?.destroy();
    };
  }, []);

  return <>{children}</>;
};
