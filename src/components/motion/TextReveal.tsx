'use client';
import { cn } from '@/utils/cn';
import { motion } from 'framer-motion';

export default function TextReveal({
  children,
  className,
  delay = 0.5,
  stepDuration = 0.05,
}: {
  children: React.ReactNode;
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
  delay?: number;
  stepDuration?: number;
}) {
  // Split text into words
  const words = typeof children === 'string' ? children.split(/([ \n])/) : [];
  const finalWords: string[] = [];
  words.forEach((word, i) => {
    if (word !== ' ') {
      if (words[i + 1] !== ' ') {
        finalWords.push(word);
      } else {
        finalWords.push(`${word} `);
      }
    }
  });
  return (
    <div className={cn('whitespace-pre-wrap', className)}>
      {typeof children === 'string'
        ? finalWords.map((word, i) =>
            word === '\n' ? (
              '\n'
            ) : (
              <motion.span
                key={i}
                initial={{
                  opacity: 0,
                  filter: 'blur(10px)',
                  y: 10,
                }}
                whileInView={{
                  opacity: 1,
                  filter: 'blur(0px)',
                  y: 0,
                }}
                transition={{
                  duration: 0.5,
                  delay: delay + i * stepDuration,
                }}
                viewport={{ once: true }}
                className='inline-block whitespace-pre will-change-transform'
              >
                {word}
              </motion.span>
            )
          )
        : children}
    </div>
  );
}
