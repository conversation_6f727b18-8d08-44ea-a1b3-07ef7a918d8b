import { motion, useScroll, useSpring, useTransform } from 'framer-motion';
import { ReactNode, useRef } from 'react';
interface ScrollMotionDivProps extends React.ComponentProps<typeof motion.div> {
  children?: ReactNode;
  style?: React.CSSProperties;
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
  offset?: [
    (
      | 'start end'
      | 'end start'
      | `${number} ${number}`
      | `${number} start`
      | `${number} end`
    ),
    (
      | 'start end'
      | 'end start'
      | `${number} ${number}`
      | `${number} start`
      | `${number} end`
    ),
  ];
  transformFrom?: number[];
  transformTo?: number[];
  targetRef?: React.RefObject<HTMLElement | null>;
  scrollX?: boolean;
}

export const ScrollMotionDiv = ({
  children,
  className = '',
  offset = ['start end', 'end start'],
  transformFrom = [0, 1],
  transformTo = [0, 0],
  style,
  targetRef,
  scrollX = false,
}: ScrollMotionDivProps) => {
  const divRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: targetRef || divRef,
    offset,
  });
  const scrollYProgressSpring = useSpring(scrollYProgress, {
    stiffness: 300,
    damping: 30,
    restDelta: 0.001,
  });
  const y = useTransform(scrollYProgressSpring, transformFrom, transformTo);

  return (
    <motion.div
      className={className}
      style={{ ...(scrollX ? { x: y } : { y }), ...style }}
      ref={divRef}
    >
      {children}
    </motion.div>
  );
};
