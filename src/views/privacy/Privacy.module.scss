.privacy-content {
    body {
        color: #4f5464;
        background-color: #fff;
        margin: 20px;
        font-family: "Inter", system-ui;
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
    }

    .mce-offscreen-selection {
        display: none;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-weight: 700;
        margin-bottom: 0;
    }

    h1+p,
    h2+p,
    h3+p,
    h4+p,
    h5+p,
    h6+p {
        margin-top: 0.5em;
    }

    h1:not(:first-child) {
        font-size: 36px;
        line-height: 46px;
        margin-top: 1em;
        color: var(--foreground)
    }

    h2:not(:first-child) {
        font-size: 24px;
        line-height: 34px;
        margin-top: 1.25em;
        color: var(--foreground)
    }

    h3:not(:first-child) {
        font-size: 19px;
        line-height: 29px;
        margin-top: 1.25em;
        color: var(--foreground)
    }

    h4:not(:first-child) {
        font-size: 16px;
        line-height: 26px;
        margin-top: 1.5em;
        color: var(--foreground)
    }

    h5:not(:first-child) {
        font-size: 14px;
        line-height: 24px;
        margin-top: 2em;
        color: var(--foreground)
    }

    h6:not(:first-child) {
        font-size: 12px;
        line-height: 22px;
        margin-top: 2em;
        color: var(--foreground)
    }

    p:not(:first-child) {
        font-size: 15px;
        line-height: 24px;
        font-weight: 500;
        margin: 0.5em 0;
    }

    a {
        color: color-mix(in srgb, #3B53FC, #2e3c43 25%);
        text-decoration: none;
    }

    ul,
    ol {
        font-size: 15px;
        line-height: 24px;
        font-weight: 500;
        margin: 1.5em 0;
        flex-direction: column;
        gap: 16px;
        padding-left: 2em;
        list-style-position: outside;
        display: flex;
        list-style-type: disc !important;

    }

    ul ul,
    ol ol,
    ul ol,
    ol ul {
        margin: 0;
    }

    b,
    strong {
        font-weight: 700;
    }

    code {
        font-size: 15px;
        line-height: 24px;
        font-weight: 500;
        padding: 2px 4px;
        font-family: "Fira Mono", monospace, monospace;
        background-color: #e4eaf1;
        border-radius: 6px;
        overflow-wrap: break-word;
    }

    pre {
        font-size: 15px;
        line-height: 24px;
        font-weight: 500;
        padding: 1em;
        font-family: "Fira Mono", monospace, monospace;
        background-color: #e4eaf1;
        border-radius: 6px;
        overflow: auto;
    }

    blockquote {
        border: 0;
        font-size: 18px;
        font-weight: 400;
        letter-spacing: .1em;
        margin: 2.5em auto;
        max-width: 700px;
        padding: 0 2.5rem;
        position: relative;
        text-align: center;
        font-style: italic;
        line-height: 27px;
    }

    blockquote::before {
        color: #ff8300;
        content: '”';
        font-size: 5em;
        left: 0%;
        pointer-events: none;
        position: absolute;
        top: 0em;
        font-family: 'georgia';
        font-weight: bold;
        transform: rotateY(180deg);
        padding-right: 7px;
    }

    blockquote::after {
        bottom: -0.5em;
        color: #ff8300;
        content: '”';
        font-size: 5em;
        pointer-events: none;
        position: absolute;
        right: 0;
        font-family: 'georgia';
        font-weight: bold;
    }

    .right-section {
        blockquote {
            border: 0;
            font-size: 16px;
            font-weight: 400;
            letter-spacing: .1em;
            margin: 2.5em auto;
            max-width: 700px;
            padding: 0 2.5rem;
            position: relative;
            text-align: center;
            line-height: 24px;
        }

        blockquote::before {
            color: #ff8300;
            content: '”';
            font-size: 4em;
            left: 0%;
            pointer-events: none;
            position: absolute;
            top: 0em;
            font-family: 'georgia';
            font-weight: bold;
            transform: rotateY(180deg);
        }

        blockquote::after {
            bottom: -0.5em;
            color: #ff8300;
            content: '”';
            font-size: 4em;
            pointer-events: none;
            position: absolute;
            right: 0;
            font-family: 'georgia';
            font-weight: bold;
        }
    }

    video,
    img {
        max-width: 100%;
        border-radius: 8px;
        height: auto;
    }

    iframe {
        max-width: 100%;
        border-radius: 6px;
    }

    hr {
        background-color: #e4eaf1;
        height: 1px;
        border: none;
        margin-top: 2em;
        margin-bottom: 2em;
    }

    table {
        border-collapse: collapse;
        font-size: 15px;
        line-height: 24px;
        font-weight: 500;
    }

    table th,
    table td {
        border: 1px solid #e4eaf1;
        padding: 0.4rem;
    }

    figure {
        display: table;
        margin: 1rem auto;
    }

    figure figcaption {
        color: #999;
        display: block;
        margin-top: 0.25rem;
        text-align: center;
    }
}