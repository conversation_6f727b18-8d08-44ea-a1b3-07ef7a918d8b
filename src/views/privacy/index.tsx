'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import DOMPurify from 'isomorphic-dompurify';
import style from './Privacy.module.scss';

export default function PrivacyView({
  privacyPolicy,
}: {
  privacyPolicy: { content: string };
}) {
  const t = useTranslations('common');
  return (
    <>
      <div className='relative mt-20 h-[50vh] border-b-[0.5px] border-[#FFFFFF4D]'>
        <div
          className='absolute top-0 left-1/2 -z-10 container mx-auto h-full w-full -translate-x-1/2 opacity-20'
          style={{
            maskImage:
              'linear-gradient(45deg, transparent 0%, transparent 35%, black 60%)',
          }}
        >
          <motion.img
            src='/logo-3d.webp'
            alt='hero-image'
            className='size-full object-contain'
          />
        </div>
        <div className='container mx-auto flex size-full flex-col items-center justify-center gap-2'>
          {/* <div className='bg-primary min-h-1 min-w-20 rounded-2xl'></div> */}
          <motion.h1
            initial={{ y: 10, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            viewport={{ once: true }}
            className='text-3xl leading-11 font-bold text-gray-50 md:text-4xl'
          >
            {t('privacy_header')}
          </motion.h1>
        </div>
      </div>
      <div
        className={`container mx-auto mt-10 mb-10 p-4 md:mt-20 md:p-0 lg:p-4 xl:p-0 ${style['privacy-content']}`}
        dangerouslySetInnerHTML={{
          __html: DOMPurify.sanitize(privacyPolicy?.content, {
            USE_PROFILES: { html: true },
          }),
        }}
      ></div>
    </>
  );
}
