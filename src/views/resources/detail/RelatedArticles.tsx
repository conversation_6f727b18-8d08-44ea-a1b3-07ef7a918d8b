'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import PostCard from '../main/PostCard';
import { useEffect, useState } from 'react';
import { getRelatedArticles } from '@/services/directus';
import { type Post } from '@/types/resources';
import Link from 'next/link';

export default function RelatedArticles({ detailArticle }: { detailArticle: Post }) {
  const t = useTranslations('resources');
  const [articles, setArticles] = useState<Post[]>([]);

  useEffect(() => {
    const getRelated = async () => {
      const res = await getRelatedArticles(detailArticle.tag, [detailArticle.id], 3);
      setArticles(res);
    };
    getRelated();
  }, []);

  if (!articles.length) return null;

  return (
    <div className='bg-card-container-40 mt-10 rounded-3xl p-6 md:mt-24'>
      <div className='flex flex-col items-center justify-between gap-1 sm:flex-row'>
        <h1 className='text-2xl leading-[150%] font-medium text-white md:text-[32px]'>
          {t('related-post')}
        </h1>
        <Link
          href='/resources'
          className='focus-visible:ring-ring bg-ư text-primary-foreground hover:bg-primary/90 bg-primary inline-flex cursor-pointer items-center justify-center gap-2 rounded-sm px-3 py-1.5 text-sm font-medium whitespace-nowrap text-white shadow transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 md:px-6 md:py-3 [&_svg]:pointer-events-none [&_svg]:shrink-0'
        >
          <span>{t('view-all')}</span>
          <Image
            src='/resources/forward-dark.svg'
            alt=''
            width={24}
            height={24}
            className='inline-block p-0.5'
          />
        </Link>
      </div>

      <div className='2lg:grid-cols-3 mt-4 grid grid-cols-1 gap-6 md:grid-cols-2'>
        {articles.map((article) => (
          <PostCard key={article.id} article={article} />
        ))}
      </div>
    </div>
  );
}
