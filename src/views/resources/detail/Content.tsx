'use client';

import { Section } from '@/components/common/Section';
import { type Post } from '@/types/resources';
import Image from 'next/image';
import Link from 'next/link';
import DOMPurify from 'isomorphic-dompurify';
import style from './DetailResource.module.scss';
import { cn } from '@/utils/cn';
import { ElementType, ComponentPropsWithoutRef } from 'react';
import { motion } from 'framer-motion';
import RelatedArticles from './RelatedArticles';
import clsx from 'clsx';
import { useTranslations } from 'next-intl';

export default function Content({ article }: { article: Post }) {
  const t = useTranslations('resources');

  const getRelatedPostsURL = (tags: string[] = []) => {
    return `/resources?${decodeURIComponent(`tags=${tags?.join(',')?.toLowerCase()}`)}`;
  };

  return (
    <Section container className={`${style['detail-container']}`}>
      <Breadcrumb article={article} />
      <h1 className='my-10 text-[32px] leading-[150%]'>{article.title}</h1>
      <div className={`mt-4 flex flex-col gap-10 ${style['content']}`}>
        {article.content.map((content, idx: number) => (
          <div className='grid grid-cols-12 gap-6' key={idx}>
            {/* Main Section */}
            <div
              className='2lg:col-span-8 col-span-12'
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(content.main_section || '', {
                  USE_PROFILES: { html: true },
                }),
              }}
            ></div>

            {/* Right section */}
            <div
              className={clsx(
                'hidden',
                style['right-section'],
                content.right_sections.length &&
                  '2lg:col-span-4 col-span-12 !flex flex-col gap-y-10'
              )}
            >
              {content.right_sections.map((section) => (
                <motion.div
                  initial={{ y: 10, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  whileHover={{ transform: 'translateY(-15px)' }}
                  transition={{ duration: 0.5 }}
                  key={section.id}
                >
                  <StarBorder as={'div'} className='w-full'>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(section.content || '', {
                          USE_PROFILES: { html: true },
                        }),
                      }}
                    ></div>
                  </StarBorder>
                </motion.div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className='mt-10 inline-flex flex-wrap items-center text-gray-50'>
        <span className='mr-3 font-bold'>{t('tag')}:</span>
        {article?.tag.map((tag: string) => (
          <Link
            key={tag}
            href={getRelatedPostsURL([tag])}
            className='group hover:text-opacity-70 mr-3 inline-flex cursor-pointer items-center hover:text-gray-500'
          >
            <Image
              className='mr-1 inline-block group-hover:invert-0'
              src='/resources/tag.svg'
              width={12}
              height={12}
              alt=''
            />
            {tag}
          </Link>
        ))}
      </div>
      <RelatedArticles detailArticle={article} />
    </Section>
  );
}

const Breadcrumb = ({ article }: { article: Post }) => (
  <div className='mt-10 flex flex-row items-center gap-2 md:mt-20'>
    <Link href='/resources' className='size-6'>
      <Image src='/resources/home.svg' width={24} height={24} alt='Home icon' />
    </Link>

    <div className='size-4'>
      <Image
        src='/resources/chevron-right.svg'
        width={16}
        height={16}
        alt=''
        className='aspect-square h-auto w-full object-cover'
      />
    </div>

    <div className='flex flex-1 flex-row items-center gap-2'>
      <span className='font- text-sm'>{article.title}</span>
    </div>
  </div>
);

interface StarBorderProps<T extends ElementType> {
  as?: T;
  color?: string;
  speed?: string;
  className?: string;
  children: React.ReactNode;
}

export function StarBorder<T extends ElementType = 'button'>({
  as,
  className,
  color,
  speed = '3s',
  children,
  ...props
}: StarBorderProps<T> & Omit<ComponentPropsWithoutRef<T>, keyof StarBorderProps<T>>) {
  const Component = as || 'button';
  const defaultColor = color || 'var(--foreground)';

  return (
    <Component
      className={cn(
        'group relative inline-block overflow-hidden rounded-2xl py-[1px]',
        className
      )}
      {...props}
    >
      <div
        className={cn(
          'group-hover:animate-star-movement-bottom absolute right-[-200%] bottom-0 z-0 h-[3px] w-[300%] translate-y-1/2 rounded-full opacity-70'
        )}
        style={{
          background: `radial-gradient(circle, ${defaultColor} 2px, transparent 10%)`,
          animationDuration: speed,
        }}
      />
      <div
        className={cn(
          'group-hover:animate-star-movement-top absolute top-0 left-[-200%] z-0 h-[3px] w-[300%] -translate-y-1/2 rounded-full',
          'opacity-70'
        )}
        style={{
          background: `radial-gradient(circle, ${defaultColor} 2px, transparent 10%)`,
          animationDuration: speed,
        }}
      />
      <div
        className={cn(
          'border-border/40 to-mute relative z-1 size-full rounded-2xl bg-[#0007123D] p-5 md:p-10'
        )}
      >
        {children}
      </div>
    </Component>
  );
}
