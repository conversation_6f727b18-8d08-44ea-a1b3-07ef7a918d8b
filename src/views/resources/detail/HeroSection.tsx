import { type Post } from '@/types/resources';
import { getAssetUrl } from '@/utils/url/asset';
import Image from 'next/image';
import { motion } from 'framer-motion';

export default function HeroSection({ article }: { article: Post }) {
  return (
    <>
      <div className='relative mt-20 h-auto w-full lg:mt-33'>
        <Image
          src={getAssetUrl(article.thumbnail)}
          alt={article.title}
          width={1920}
          height={598}
          className='size-full object-cover object-center md:aspect-[1920/598] md:h-auto md:w-full'
          priority
        />
        <div
          className='absolute inset-0'
          style={{
            background:
              'linear-gradient(180deg, rgba(20, 20, 20, 0) 0%, rgba(20, 20, 20, 0.880208) 75.52%, #141414 100%)',
          }}
        />
        <div className='relative container mx-auto'>
          <motion.div
            initial={{ filter: 'blur(10px)', opacity: 0, y: 10 }}
            whileInView={{ filter: 'blur(0px)', opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 1 }}
            viewport={{ once: true }}
            className='absolute bottom-4 z-10 flex w-full justify-center px-4 text-center text-2xl leading-[150%] font-semibold text-white md:text-3xl lg:text-4xl xl:bottom-16 xl:text-[64px]'
          >
            {article?.title || ''}
          </motion.div>
        </div>
      </div>
    </>
  );
}
