'use client';

import { Post } from '@/types/resources';
import { useTranslations } from 'next-intl';
import NotFoundArticle from '../main/NotFoundArticle';
import Content from './Content';
import HeroSection from './HeroSection';

export default function ResourcesDetailView({ article }: { article: Post }) {
  const t = useTranslations('resources');

  if (!article.id) {
    return (
      <div className='flex min-h-[calc(100vh-505px)] justify-center'>
        <NotFoundArticle text={t('not-found-article')} />;
      </div>
    );
  }
  return (
    <div className='mb-4 min-h-screen'>
      <HeroSection article={article} />
      <Content article={article} />
    </div>
  );
}
