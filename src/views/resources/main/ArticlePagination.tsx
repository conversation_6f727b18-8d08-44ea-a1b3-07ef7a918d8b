import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/common/pagination';
import { cn } from '@/utils/cn';
import { getPaginationButtons } from '@/utils/resources';
import { IconArrowLeft, IconArrowRight } from '@tabler/icons-react';

type ArticlePaginationProps = {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
};

export default function ArticlePagination({
  currentPage,
  totalPages,
  onPageChange,
}: ArticlePaginationProps) {
  const pages = getPaginationButtons(totalPages, currentPage);

  const onChangePerviousPage = (): void => {
    if (currentPage !== 1) onPageChange(currentPage - 1);
  };

  const onChangeNextPage = (): void => {
    if (currentPage !== totalPages) onPageChange(currentPage + 1);
  };

  return (
    <div className='mt-12 flex items-center justify-center'>
      <Pagination>
        <PaginationContent>
          <PaginationItem onClick={onChangePerviousPage}>
            <PaginationPrevious
              className={cn(
                'hover:text-primary h-12 w-12 rounded-full border-none bg-transparent text-blue-500',
                {
                  'cursor-not-allowed border-0 text-gray-200 !shadow-none hover:bg-transparent hover:text-gray-300':
                    currentPage === 1,
                }
              )}
            >
              <IconArrowLeft className='size-6' />
            </PaginationPrevious>
          </PaginationItem>
          {pages.map((page) => {
            if (typeof page === 'string') {
              return (
                <PaginationItem key={page}>
                  <PaginationEllipsis />
                </PaginationItem>
              );
            }

            const isActive = page === currentPage;

            return (
              <PaginationItem key={page} onClick={() => onPageChange(page)}>
                <PaginationLink
                  isActive={page === currentPage}
                  className={cn(
                    'h-12 w-12 rounded-full border-none bg-transparent text-sm leading-5 font-medium text-gray-50 shadow-none hover:bg-blue-500 hover:text-gray-950',
                    { 'bg-primary text-gray-50': isActive }
                  )}
                >
                  {page.toString().padStart(2, '0')}
                </PaginationLink>
              </PaginationItem>
            );
          })}
          <PaginationItem onClick={onChangeNextPage}>
            <PaginationNext
              className={cn(
                'hover:text-primary h-12 w-12 rounded-full border-none bg-transparent',
                {
                  'cursor-not-allowed border-none text-gray-200 !shadow-none hover:bg-transparent hover:text-gray-300':
                    currentPage === totalPages,
                }
              )}
            >
              <IconArrowRight className='size-6' />
            </PaginationNext>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
