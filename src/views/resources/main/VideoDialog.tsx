'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/common/Dialog';
import { Button } from '@/components/common/Button';

import { Post } from '@/types/resources';
import { getAssetUrl } from '@/utils/url/asset';
import { IconArrowLeft, IconLoader2 } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

type VideoDialogProps = {
  open: boolean;
  video?: Post;
  onClose: () => void;
};

export default function VideoDialog({ open, video, onClose }: VideoDialogProps) {
  const t = useTranslations('resources');
  const [isLoading, setIsLoading] = useState(false);

  return (
    <Dialog open={!!video && open} data-lenis-prevent>
      <DialogTitle>{video?.title || ''}</DialogTitle>
      <DialogContent
        showCloseButton={false}
        className='video-dialog bg-card-container-40 container max-h-[calc(80vh+60px)] w-full overflow-x-hidden rounded-4xl border border-gray-900 p-2 md:p-5 lg:p-10'
        data-lenis-prevent
      >
        <DialogHeader>
          <div className='flex items-start justify-between'>
            <Button
              variant='ghost'
              onClick={onClose}
              className='flex-1'
              aria-label='Close'
            >
              <IconArrowLeft />
            </Button>
            <div className='flex flex-col'>
              <h1 className='text-[40px] leading-[150%] font-bold text-white'>
                {video?.title || ''}
              </h1>
              <p className='text-lg font-normal text-gray-700'>
                {video?.description || ''}
              </p>
            </div>
          </div>
        </DialogHeader>
        {!!video?.video ? (
          <div className='relative'>
            {isLoading && (
              <div className='bg-muted absolute inset-0 flex items-center justify-center rounded-lg'>
                <div className='flex items-center gap-2'>
                  <IconLoader2 className='h-4 w-4 animate-spin' />
                  <span>Loading video...</span>
                </div>
              </div>
            )}
            <video
              className='mx-auto mt-10 aspect-video h-auto w-full rounded-2xl'
              controls
              onLoad={() => setIsLoading(true)}
              onLoadedData={() => setIsLoading(false)}
            >
              <source src={getAssetUrl(video?.video) || ''} />
            </video>
          </div>
        ) : (
          <div className='mx-auto mt-10 aspect-video h-auto w-full rounded-2xl bg-gray-700'></div>
        )}
      </DialogContent>
    </Dialog>
  );
}
