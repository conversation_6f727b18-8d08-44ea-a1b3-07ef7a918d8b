import { Button } from '@/components/common/Button';
import { useResources } from '@/components/context/ResourcesContext';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

export default function FeatureBlock({
  title,
  description,
  type = 'videos',
}: {
  title: string;
  description: string;
  type?: 'videos';
}) {
  const t = useTranslations('resources');
  const { setCurrentTab } = useResources();

  return (
    <div className='border-t border-b border-[#FFFFFF4D] bg-[#0000001A] py-10 backdrop-blur-[34px] md:py-15 xl:py-30'>
      <div className='mx-auto flex flex-col items-center justify-between gap-4 px-8 lg:container lg:flex-row lg:gap-0 lg:px-0'>
        <ScrollReveal
          blurred
          className='flex flex-col items-center !backdrop-blur-none lg:items-start'
        >
          <span className='max-w-fit rounded-lg bg-blue-800 px-2.5 py-1.5 text-xl text-white'>
            {title}
          </span>
          <h1 className='mt-4 text-center text-3xl text-white md:text-4xl xl:text-6xl'>
            {description}
          </h1>
        </ScrollReveal>

        <Button
          variant='default'
          className='!px-6'
          onClick={() => setCurrentTab(type)}
          aria-label='View all'
        >
          <span>{t('view-all')}</span>
          <Image
            src='/resources/forward-dark.svg'
            alt=''
            width={24}
            height={24}
            className='inline-block p-0.5'
          />
        </Button>
      </div>
    </div>
  );
}
