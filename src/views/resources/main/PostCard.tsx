import { type Post } from '@/types/resources';
import { formatDate } from '@/utils/resources';
import { getAssetUrl } from '@/utils/url/asset';
import Image from 'next/image';
import { PlayButton } from './VideoCard';
import clsx from 'clsx';
import style from './MainResources.module.scss';
import { Glasscard as GlassCard } from '@/components/common/Card';
import Link from 'next/link';
import { BLUR_IMAGE } from '@/utils/constants/resources';

type PostCardProps = {
  article: Post;
  onClick?: (article: Post) => void;
};

export default function PostCard({ article, onClick }: PostCardProps) {
  const isVideo = article.type.label === 'Video';
  return (
    <GlassCard
      className={`group bg-card-container-40 relative flex cursor-pointer flex-col gap-1 overflow-hidden rounded-4xl transition-colors duration-300 hover:bg-[#10326366] ${style['resource-card']}`}
      borderRadius={32}
      childContainerClassName='flex flex-col'
    >
      <Link
        href={isVideo ? '' : `/resources/${article.slug}`}
        className={clsx(
          'flex h-full flex-col overflow-hidden rounded-4xl border border-[#FFFFFF4D] p-4 hover:border-transparent',
          isVideo && 'pointer-events-none'
        )}
        onClick={() => {
          onClick?.(article);
        }}
      >
        <div className='relative overflow-hidden rounded-2xl'>
          <Image
            src={getAssetUrl(article.thumbnail)}
            alt=''
            width={487}
            height={240}
            placeholder='blur'
            blurDataURL={BLUR_IMAGE}
            className={clsx(
              'aspect-[487/240] h-auto w-full rounded-2xl',
              !isVideo && 'transition-transform duration-300 group-hover:scale-105'
            )}
          />
          {isVideo && (
            <>
              <div className='absolute inset-0 w-full bg-linear-to-b from-[#14141400] to-[#141414] opacity-100 transition-opacity duration-300 group-hover:opacity-20'></div>
              <div className='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2'>
                <PlayButton />
              </div>
            </>
          )}
        </div>
        <div className='mt-2 flex h-full flex-1 flex-col justify-between'>
          <div>
            <span className='max-w-fit rounded-md bg-blue-100 px-2.5 py-1 text-xs font-normal text-blue-900 md:text-sm'>
              {article.type.label}
            </span>
            <h1
              className={`my-4 text-xl font-semibold text-gray-50 md:text-2xl ${style['title']}`}
              title={article.title}
            >
              {article.title}
            </h1>
          </div>
          <div className='md:text-md inline-block text-xs font-normal text-gray-50'>
            {formatDate(article.date_created || '')}
          </div>
        </div>
      </Link>
    </GlassCard>
  );
}
