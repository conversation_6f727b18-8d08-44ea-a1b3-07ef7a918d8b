'use client';

import { SlashBackground2 } from '@/components/common/SlashBackground';
import { useResources } from '@/components/context/ResourcesContext';
import { getResourcesVideos } from '@/services/directus';
import { type Post } from '@/types/resources';
import { cn } from '@/utils/cn';
import { POPULAR_VIDEO, POPULAR_VIDEO_IN_ROW, TAB } from '@/utils/constants/resources';
import { chunk } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';
import ArticleLayout from './ArticleLayout';
import FeatureBlock from './FeatureBlock';
import NotFoundArticle from './NotFoundArticle';
import VideoCard from './VideoCard';
import VideoDialog from './VideoDialog';
import VideoSkeleton from './VideoSkeleton';

export default function VideoSection() {
  const t = useTranslations('resources');
  const { currentTab, popularVideos, setPopularVideos } = useResources();
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [video, setVideo] = useState<Post | undefined>();
  const isFirstLoad = useRef(true);

  useEffect(() => {
    const getVideos = async () => {
      try {
        setIsLoading(true);
        const res = await getResourcesVideos(POPULAR_VIDEO, 1, [], {
          popular: { _eq: true },
        });
        setPopularVideos(chunk(res, 2));
      } catch (error) {
        console.error('got error getVideos: ', error);
      } finally {
        setIsLoading(false);
        isFirstLoad.current = false;
      }
    };
    getVideos();
  }, []);

  if (!popularVideos?.length && !isFirstLoad.current) {
    if (currentTab === TAB.VIDEO) {
      return <NotFoundArticle text={t('not-found-video')} />;
    }
    return null;
  }

  const onClose = () => {
    setIsOpen(false);
    setVideo(undefined);
  };

  const onOpen = (video: Post) => {
    setVideo(video);
    setIsOpen(!!video);
  };

  return (
    <>
      <VideoDialog open={isOpen} video={video} onClose={onClose} />
      <div
        className={cn('relative container mx-auto flex flex-row justify-center p-4', {
          'justify-center': currentTab === TAB.VIDEO,
        })}
      >
        <SlashBackground2 />
      </div>
      <FeatureBlock title={t('feature-videos')} description={t('video-section-desc')} />
      <div className='container mx-auto flex flex-col gap-5 px-4 py-4 md:gap-0 md:px-8 md:py-0 lg:px-0'>
        {isLoading ? (
          <Loading />
        ) : (
          <Video articles={popularVideos} onClickCard={onOpen} />
        )}
      </div>

      {currentTab === TAB.VIDEO && (
        <ArticleLayout title={t('latest-videos')} type='videos' onClickCard={onOpen} />
      )}
    </>
  );
}

const Video = ({
  articles,
  onClickCard,
}: {
  articles: Post[][] | undefined;
  onClickCard: (video: Post) => void;
}) => {
  return (
    <>
      {articles?.map((row, idx) => (
        <div key={idx}>
          <div className='flex flex-col justify-center gap-5 md:flex-row md:gap-0 md:py-10'>
            {row.map((video: Post, idx1: number) => (
              <div key={idx1} className='flex flex-row'>
                <VideoCard video={video} onClick={onClickCard} />
                {idx1 % 2 === 0 && idx1 !== row.length - 1 && (
                  <div className='hidden w-[1px] bg-blue-50 md:mx-5 md:block lg:mx-10 xl:mx-20'></div>
                )}
              </div>
            ))}
          </div>
          {idx % 2 === 0 && idx !== articles.length - 1 && (
            <div className='hidden h-[1px] bg-[#262626] md:block'></div>
          )}
        </div>
      ))}
    </>
  );
};

const Loading = () => {
  const layout = chunk(Array.from({ length: POPULAR_VIDEO }), POPULAR_VIDEO_IN_ROW);
  return (
    <>
      {layout.map((row, idx) => (
        <div key={idx} className=''>
          <div className='flex flex-col justify-center gap-5 md:flex-row md:gap-0 md:py-10'>
            {row.map((_, idx1: number) => (
              <div key={idx1} className='flex flex-row'>
                <VideoSkeleton />
                {idx1 % 2 === 0 && idx1 !== row.length - 1 && (
                  <div className='hidden w-[1px] bg-blue-50 md:mx-5 md:block lg:mx-10 xl:mx-20'></div>
                )}
              </div>
            ))}
          </div>
          {idx % 2 === 0 && <div className='hidden h-[1px] bg-[#262626] md:block'></div>}
        </div>
      ))}
    </>
  );
};
