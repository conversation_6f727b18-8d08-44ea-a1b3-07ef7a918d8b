import { useResources } from '@/components/context/ResourcesContext';
import { getDynamicSections, getResourcesArticles } from '@/services/directus';
import { type Post } from '@/types/resources';
import { TAB } from '@/utils/constants/resources';
import { formatDate } from '@/utils/resources';
import { getAssetUrl } from '@/utils/url/asset';
import clsx from 'clsx';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import { Autoplay } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import style from './MainResources.module.scss';

export default function HeroSection() {
  const t = useTranslations('resources');
  const { highlightResources, setHighlightResources } = useResources();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getHighlightResources = async () => {
      try {
        setIsLoading(true);
        let items = [];
        const res = await getDynamicSections('highlight resources', {
          Resources: [
            'title',
            'description',
            'id',
            'slug',
            'thumbnail',
            'date_created',
            { type: ['*'] },
          ],
        });

        items = res?.data
          ?.map((data: any) => data.item)
          .filter((item: any) => item.type.value !== TAB.VIDEO);

        if (!items.length) {
          items = await getResourcesArticles(3, 1, []);
        }

        setHighlightResources(items);
      } catch (error) {
        console.error('Got error in getHighlightResources: ', error);
      } finally {
        setIsLoading(false);
      }
    };

    getHighlightResources();
  }, []);

  return (
    <>
      <div className='relative mx-auto w-full lg:container'>
        <Swiper
          spaceBetween={30}
          speed={1000}
          slidesPerView={1}
          loop={true}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
          }}
          modules={[Autoplay]}
          className='w-full'
        >
          {isLoading ? (
            <div className='bg-card-container-40 relative container mx-auto mt-20 mb-30 flex aspect-[152/75] h-auto w-full max-w-7xl flex-col items-center justify-center rounded-xl sm:px-8 lg:mt-33'>
              <div className='animate-pulse'>{t('loading')}</div>
            </div>
          ) : (
            highlightResources?.map((article, idx) => (
              <SwiperSlide key={article.id}>
                <BlogSlide article={article} />
              </SwiperSlide>
            ))
          )}
        </Swiper>
      </div>
    </>
  );
}

const BlogSlide = ({ article }: { article: Post }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <div className={`relative mx-auto h-auto overflow-hidden ${style['resource-card']}`}>
      <div className='relative mx-auto mt-20 w-full max-w-7xl sm:px-8 lg:mt-33'>
        <Link
          className='group relative size-full cursor-pointer'
          href={`/resources/${article.slug}`}
        >
          <Image
            src={getAssetUrl(article?.thumbnail)}
            width={1216}
            height={600}
            alt={article?.title}
            className='aspect-[152/75] h-auto w-full rounded-xl'
            priority
            unoptimized
          />
          {/* Blur background */}
          <div
            className={clsx(
              'absolute top-0 left-0 size-full rounded-xl bg-[#14162466] opacity-100 transition-opacity duration-300 group-hover:opacity-0',
              hovered && '!opacity-0'
            )}
          ></div>
        </Link>
        {/* Card */}
        <div
          className={`relative left-1/2 flex w-10/12 max-w-lg -translate-x-1/2 -translate-y-1/2 flex-col gap-1 overflow-hidden rounded-xl bg-white sm:w-full sm:gap-4 lg:left-16 lg:max-w-lg lg:translate-x-0 lg:-translate-y-3/4 xl:max-w-xl ${style['card-container']}`}
        >
          <Link
            href={`/resources/${article.slug}`}
            className={`group cursor-pointer p-4 xl:p-10 ${style['content']}`}
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
          >
            <span className='max-w-fit rounded-md bg-blue-800 via-white/50 px-2.5 py-1 text-xs font-normal sm:text-sm'>
              {article?.type?.label || ''}
            </span>
            <div
              className={`text-secondary-800 text-md mt-4 mb-6 font-semibold sm:text-xl md:text-2xl xl:text-4xl ${style['title']}`}
              title={article?.title}
            >
              {article?.title}
            </div>
            <span className='text-secondary-400 sm:text-md text-sm font-medium'>
              {formatDate(article?.date_created)}
            </span>
          </Link>
        </div>
      </div>
    </div>
  );
};
