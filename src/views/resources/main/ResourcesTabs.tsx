import clsx from 'clsx';
import { useTranslations } from 'next-intl';
import { TABS } from '.';
import { useResources } from '@/components/context/ResourcesContext';

export default function ResourcesTabs() {
  const t = useTranslations('resources');
  const { currentTab, setCurrentTab } = useResources();

  const onChangeTab = (value: string) => {
    const searchQuery = new URLSearchParams(window.location.search);
    if (searchQuery.has('tags')) {
      searchQuery.delete('tags');
      window.history.pushState(
        null,
        '',
        `${window.location.origin}${window.location.pathname}?${searchQuery.toString()}`
      );
    }
    setCurrentTab(value);
  };

  return (
    <div className='flex flex-row gap-4'>
      {TABS.map((tab) => (
        <div
          key={tab.value}
          className={clsx(
            'md:text-md cursor-pointer rounded-md border border-gray-50 px-2 py-0.5 text-sm text-gray-50 md:px-3 md:px-5 md:py-1 md:py-3',
            currentTab === tab.value && 'bg-primary font-semibold text-orange-950'
          )}
          onClick={() => {
            onChangeTab(tab.value);
          }}
        >
          {t(tab.label)}
        </div>
      ))}
    </div>
  );
}
