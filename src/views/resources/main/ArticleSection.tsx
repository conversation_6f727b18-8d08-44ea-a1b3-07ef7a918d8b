'use client';
import { Section } from '@/components/common/Section';
import { useTranslations } from 'next-intl';
import ArticleLayout from './ArticleLayout';
import { SlashBackground2 } from '@/components/common/SlashBackground';

export default function ArticleSection() {
  const t = useTranslations('resources');

  return (
    <>
      <div className='relative z-0 container mx-auto flex flex-row justify-center'>
        <SlashBackground2 />
      </div>
      <Section container center className='relative z-10 lg:!px-0'>
        <ArticleLayout title={t('popular-post')} filter={{ popular: { _eq: true } }} />
        <ArticleLayout title={t('latest-post')} filter={{ popular: { _neq: true } }} />
      </Section>
    </>
  );
}
