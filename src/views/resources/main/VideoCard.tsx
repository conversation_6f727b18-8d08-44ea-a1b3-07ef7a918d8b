import { type Post } from '@/types/resources';
import { getAssetUrl } from '@/utils/url/asset';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import React from 'react';
import style from './MainResources.module.scss';
import { Glasscard } from '@/components/common/Card';

export default function VideoCard({
  video,
  onClick,
}: {
  video: Post;
  onClick: (video: Post) => void;
}) {
  const t = useTranslations('resources');
  return (
    <Glasscard
      className={`group bg-card-container-40 max-w-3xl cursor-pointer rounded-4xl ${style['resource-card']}`}
      borderRadius={32}
      onClick={() => onClick(video)}
    >
      <div className='h-full overflow-hidden rounded-4xl border border-[#B4B4B4] p-4 hover:border-transparent'>
        <div className='relative'>
          <Image
            src={getAssetUrl(video.thumbnail)}
            alt=''
            width={686}
            height={412}
            className='aspect-[686/412] h-auto w-full rounded-2xl'
          />
          <div className='absolute top-0 left-0 size-full rounded-2xl bg-linear-to-b from-[#14141400] to-[#141414] opacity-0 transition-opacity duration-300 group-hover:opacity-20 md:opacity-100'></div>
          <div className='absolute bottom-0 left-0 z-50 flex w-full flex-row items-end justify-between p-3 md:p-5 lg:p-7'>
            <PlayButton />
            <span className='text-md inline-block font-normal text-gray-50 md:text-lg'>
              {video.duration} {t('min')}
            </span>
          </div>
        </div>

        <div className='mt-4'>
          <h2
            className={`text-xl font-semibold text-white lg:text-2xl ${style['title']}`}
            title={video.title}
          >
            {video.title}
          </h2>
          <p className={`text-md text-[#B4B4B4] lg:text-lg ${style['description']}`}>
            {video.description}
          </p>
        </div>
      </div>
    </Glasscard>
  );
}

export const PlayButton = () => (
  <div className='group relative flex size-10 rounded-full bg-white transition-all duration-300 group-hover:size-15 sm:size-14 lg:size-14'>
    <span className='group-hover:border-l-primary relative top-1/2 left-1/2 ml-0.5 inline-block h-0 w-0 -translate-x-1/2 -translate-y-1/2 rounded-sm border-t-8 border-b-8 border-l-[15px] border-t-transparent border-b-transparent border-l-black sm:border-t-[15px] sm:border-b-[15px] sm:border-l-[24px]'></span>
  </div>
);
