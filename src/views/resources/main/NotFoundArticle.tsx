import { useTranslations } from 'next-intl';

function NotFoundArticle({ text, customClass }: { text: string; customClass?: string }) {
  const t = useTranslations('resources');
  return (
    <div className='mt-5 flex w-full flex-col items-center justify-center'>
      <h1 className={`mb-2 text-2xl font-bold ${customClass}`}>{text}</h1>
      <p className='text-center'>{t('not-found')}</p>
    </div>
  );
}

export default NotFoundArticle;
