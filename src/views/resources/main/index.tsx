'use client';

import { useLayout } from '@/components/context/LayoutContext';
import { useEffect } from 'react';
import ArticleSection from './ArticleSection';
import HeroSection from './HeroSection';
import ResourcesTabs from './ResourcesTabs';
import VideoSection from './VideoSection';
import ResourcesProvider, { useResources } from '@/components/context/ResourcesContext';

export const TABS = [
  {
    value: 'all',
    label: 'all',
    content: (
      <>
        <ArticleSection />
        <VideoSection />
      </>
    ),
  },
  {
    value: 'articles',
    label: 'articles',
    content: <ArticleSection />,
  },
  {
    value: 'videos',
    label: 'videos',
    content: <VideoSection />,
  },
];

export default function ResourcesView() {
  const { setIsDisableHeaderScroll } = useLayout();

  useEffect(() => {
    setIsDisableHeaderScroll(true);

    return () => {
      setIsDisableHeaderScroll(false);
    };
  }, []);

  return (
    <ResourcesProvider>
      <HeroSection />
      <Content />
    </ResourcesProvider>
  );
}

const Content = () => {
  const { currentTab } = useResources();
  return (
    <>
      <div className='relative z-99 container mx-auto -mt-8 mb-4 px-4 md:mb-8 md:px-8 lg:-mt-20 lg:px-0'>
        <ResourcesTabs />
      </div>
      {TABS.find((tab) => tab.value === currentTab)?.content}
    </>
  );
};
