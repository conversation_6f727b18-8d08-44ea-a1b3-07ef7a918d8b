'use client';

import { useResources } from '@/components/context/ResourcesContext';
import {
  getResourcesArticles,
  getResourcesVideos,
  getTotalArticle,
} from '@/services/directus';
import { Post } from '@/types/resources';
import { ITEMS_PER_PAGE, TAB } from '@/utils/constants/resources';
import { getIgnoreIDs } from '@/utils/resources';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useRef, useState } from 'react';
import ArticlePagination from './ArticlePagination';
import ArticleSkeleton from './ArticleSkeleton';
import NotFoundArticle from './NotFoundArticle';
import PostCard from './PostCard';

type PostLayoutProps = {
  title: string;
  filter?: Record<string, any>;
  type?: 'articles' | 'videos';
  onClickCard?: (video: Post) => void;
};

export default function ArticleLayout({
  title,
  filter = {},
  type = 'articles',
  onClickCard,
}: PostLayoutProps) {
  const isVideo = type === TAB.VIDEO;
  const { highlightResources, popularVideos, setCurrentTab } = useResources();
  const t = useTranslations('resources');
  const [articles, setArticles] = useState<Post[]>([]);
  const [total, setTotal] = useState(0);
  const [tag, setTag] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const isFirstLoad = useRef(true);
  const container = useRef<HTMLDivElement | null>(null);

  const totalPages = Math.ceil(total / ITEMS_PER_PAGE);
  const highlightIds = useMemo(
    () => getIgnoreIDs(popularVideos, highlightResources, isVideo),
    [popularVideos, highlightResources, isVideo]
  );

  useEffect(() => {
    if (typeof window !== undefined) {
      const searchQuery = new URLSearchParams(window?.location?.search);
      const tagSearch = searchQuery.get('tags');
      setTag(tagSearch);
      if (tagSearch) setCurrentTab(TAB.ARTICLE);
    }
  }, []);

  useEffect(() => {
    if (isVideo && popularVideos) {
      getArticles();
    } else if (highlightResources) {
      const searchQuery = new URLSearchParams(window?.location?.search);
      const tag = searchQuery.get('tags');
      getArticles(tag ? { tag_values: { _contains: tag } } : {});
    }
  }, [highlightIds, currentPage]);

  const getArticles = async (pFilter: Record<string, any> = {}) => {
    try {
      const mergeFilter = [filter, pFilter];
      setIsLoading(true);
      const resourceRequest = isVideo
        ? await getResourcesVideos(
            ITEMS_PER_PAGE,
            currentPage,
            highlightIds ?? [],
            mergeFilter
          )
        : await getResourcesArticles(
            ITEMS_PER_PAGE,
            currentPage,
            highlightIds ?? [],
            mergeFilter
          );
      const totalRequest = getTotalArticle(highlightIds ?? [], [
        ...mergeFilter,
        { type: { value: { _eq: type } } },
      ]);
      const res = await Promise.all([resourceRequest, totalRequest]);

      setArticles([...res[0]]);
      setTotal(res[1]);
    } catch (error) {
      console.error('got error getArticles: ', error);
      setIsError(true);
    } finally {
      setIsLoading(false);
      isFirstLoad.current = false;
    }
  };

  const onChangePage = (page: number): void => {
    setCurrentPage(page);
    // const params = new URLSearchParams(window.location.search);
    // params.set('page', `${page}`);
    // window.history.pushState(
    //   { page: page },
    //   '',
    //   `${window.location.origin}${window.location.pathname}?${params.toString()}`
    // );
    if (container.current) {
      const headerOffset = -80;
      const scrollAmount =
        container.current.getBoundingClientRect().top + window.scrollY + headerOffset;
      window.scrollTo({ top: scrollAmount, behavior: 'smooth' });
    }
  };

  if (!articles.length && !isFirstLoad.current) return null;

  if (isError) return <NotFoundArticle text={t('not-found-article')} />;

  return (
    <div className='container mx-auto mb-25'>
      {tag && !isVideo && (
        <h1 className='text-3xl font-bold'>
          Search By Tag: <span className='text-2xl font-normal'>{tag}</span>
        </h1>
      )}
      <h1 className='my-4 text-2xl font-bold text-white md:my-10'>{title}</h1>
      {isLoading ? (
        <div className='grid gap-5 md:grid-cols-2 xl:grid-cols-3'>
          {Array.from({ length: 6 }, (_, idx) => (
            <ArticleSkeleton key={idx} />
          ))}
        </div>
      ) : (
        <>
          <div className='grid gap-5 md:grid-cols-2 xl:grid-cols-3'>
            {articles?.map((article: Post, idx) => (
              <PostCard key={idx} article={article} onClick={onClickCard} />
            ))}
          </div>
          <ArticlePagination
            totalPages={totalPages}
            currentPage={currentPage}
            onPageChange={onChangePage}
          />
        </>
      )}
    </div>
  );
}
