import { getCarouselGroups } from '@/services/directus';
import HeroS<PERSON><PERSON> from './HeroSection';
import OurCoreSolutionsSection from './OurCoreSolutionsSection';
import SuccessStoriesSection from './SuccessStories';
import IndustryExcellenceSection from './IndustryExcellenceSection';
import HowWeWorkSection from './HowWeWorkSection';
import ConnectToExpertSection from './ConnectToExpertSection';
export default async function SolutionsView() {
  const carouselGroups = await getCarouselGroups('solutions');

  return (
    <>
      <HeroSection />
      <OurCoreSolutionsSection />
      <IndustryExcellenceSection />
      <HowWeWorkSection />
      <SuccessStoriesSection carouselGroups={carouselGroups} />
      <ConnectToExpertSection />
    </>
  );
}
