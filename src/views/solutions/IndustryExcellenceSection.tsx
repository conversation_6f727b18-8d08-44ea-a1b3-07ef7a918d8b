'use client';
import { Glasscard } from '@/components/common/Card';
import { CarouselButton } from '@/components/common/CarouselButton';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { cn } from '@/utils/cn';
import { AnimatePresence, motion, useInView } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';
import {
  AgricultureIcon,
  FinanceIcon,
  ManufacturingIcon,
  RetailIcon,
} from './IndustryExcellenceIcon';
import BlurShine from '@/components/common/BlurShine';
import { ScrollMotionDiv } from '@/components/motion/ScrollMotionDiv';

export default function IndustryExcellenceSection() {
  const t = useTranslations('solutions');
  const [activeIndex, setActiveIndex] = useState(0);

  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);
  const [animatedNumber, setAnimatedNumber] = useState(0);

  const sectionRef = useRef<HTMLDivElement>(null);

  const isInView = useInView(sectionRef);

  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;
      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying && isInView) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, isInView]);
  useEffect(() => {
    // Reset animation when slide changes
    progressRef.current = 0;
    setAnimatedNumber(0);
  }, [activeIndex]);
  useEffect(() => {
    if (Math.floor(animatedNumber) === 9) {
      setActiveIndex(activeIndex === features.length - 1 ? 0 : activeIndex + 1);
    }
  }, [Math.floor(animatedNumber)]);
  const features = [
    {
      title: t('agriculture'),
      icon: <AgricultureIcon />,
      description: t('agriculture_description'),
      image: '/solutions/agriculture.png',
    },
    {
      title: t('manufacturing'),
      icon: <ManufacturingIcon />,
      description: t('manufacturing_description'),
      image: '/solutions/manufacturing.png',
    },
    {
      title: t('retail'),
      icon: <RetailIcon />,
      description: t('retail_description'),
      image: '/solutions/retail.png',
    },
    {
      title: t('finance'),
      icon: <FinanceIcon />,
      description: t('finance_description'),
      image: '/solutions/finance.png',
    },
  ];
  return (
    <Section container center className='py-10 md:py-20 lg:py-40'>
      <HeroTitle
        tag={t('industry_excellence')}
        title={t('outstanding_domain')}
        description={t('outstanding_domain_content')}
        descriptionClassName='max-w-[762px]'
        className='z-10'
      />
      <motion.div
        ref={sectionRef}
        className='relative mt-10 flex w-full flex-col items-center justify-center md:mt-20 lg:mt-40'
      >
        <BlurShine direction='right' />
        <ScrollMotionDiv
          transformFrom={[0, 0.9]}
          transformTo={[-180, 0]}
          targetRef={sectionRef}
          className='relative z-10 h-[400px] max-lg:hidden md:aspect-[920/810] lg:h-[800px] lg:self-start'
        >
          <AnimatePresence mode='wait'>
            <motion.img
              key={`image-${activeIndex}`}
              initial={{
                opacity: 0,
                filter: 'blur(10px)',
              }}
              animate={{
                opacity: 1,
                filter: 'blur(0px)',
              }}
              exit={{
                opacity: 0,
                filter: 'blur(10px)',
              }}
              src={features[activeIndex].image}
              alt={features[activeIndex].title}
              className='h-full w-full object-contain object-bottom md:object-center'
            />
          </AnimatePresence>
        </ScrollMotionDiv>
        <div className='relative z-10 h-[400px] md:aspect-[920/810] lg:hidden lg:h-[800px] lg:self-start'>
          <AnimatePresence mode='wait'>
            <motion.img
              key={`image-${activeIndex}`}
              initial={{
                opacity: 0,
                filter: 'blur(10px)',
              }}
              animate={{
                opacity: 1,
                filter: 'blur(0px)',
              }}
              exit={{
                opacity: 0,
                filter: 'blur(10px)',
              }}
              src={features[activeIndex].image}
              alt={features[activeIndex].title}
              className='h-full w-full object-contain object-bottom md:object-center'
            />
          </AnimatePresence>
        </div>
        <motion.div
          layout
          className='relative z-10 flex flex-col items-end gap-6 self-end max-lg:-mt-20 max-md:-mt-10 lg:absolute'
        >
          <div
            className='absolute -top-10 h-40 w-full blur-3xl lg:hidden'
            style={{
              background: 'linear-gradient(0deg, #327FEF 0%, #FF8300 50%, #327FEF 100%)',
            }}
          ></div>
          <Glasscard className='bg-[#00000066] p-5 md:p-10' borderColor='white'>
            <div className='flex flex-col text-sm'>
              <div>{t('expertise')}</div>
              <AnimatePresence mode='wait'>
                <motion.div
                  key={`title-${activeIndex}`}
                  initial={{
                    opacity: 0,
                    filter: 'blur(10px)',
                  }}
                  animate={{
                    opacity: 1,
                    filter: 'blur(0px)',
                  }}
                  exit={{
                    opacity: 0,
                    filter: 'blur(10px)',
                  }}
                  className='bg-gradient-to-b from-white to-white/50 bg-clip-text text-2xl font-semibold text-transparent'
                >
                  {features[activeIndex].title}
                </motion.div>
              </AnimatePresence>
              <AnimatePresence mode='wait'>
                <motion.div
                  key={`description-${activeIndex}`}
                  initial={{
                    opacity: 0,
                    filter: 'blur(10px)',
                  }}
                  animate={{
                    opacity: 1,
                    filter: 'blur(0px)',
                  }}
                  exit={{
                    opacity: 0,
                    filter: 'blur(10px)',
                  }}
                  className='mt-2 text-white/70 md:leading-loose lg:max-w-[555px]'
                >
                  {features[activeIndex].description}
                </motion.div>
              </AnimatePresence>
            </div>
          </Glasscard>
          <div className='flex gap-6'>
            <CarouselButton
              direction='left'
              onClick={() =>
                setActiveIndex(activeIndex === 0 ? features.length - 1 : activeIndex - 1)
              }
            />
            <CarouselButton
              direction='right'
              onClick={() =>
                setActiveIndex(activeIndex === features.length - 1 ? 0 : activeIndex + 1)
              }
            />
          </div>
        </motion.div>
        <div className='mt-10 hidden w-full grid-cols-4 gap-4 md:grid lg:mt-24'>
          {features.map((feature, index) => (
            <div key={index} className='' onClick={() => setActiveIndex(index)}>
              <Glasscard
                isForCardSection={activeIndex === index}
                className='h-[212px] cursor-pointer'
                borderColor='#ffffff54'
              >
                <div className='relative z-10 flex h-full flex-col items-start justify-between p-4'>
                  <motion.div
                    animate={{
                      opacity: activeIndex === index ? 1 : 0.4,
                    }}
                    className='flex size-11 flex-none items-center justify-center rounded-xl border border-white/10'
                  >
                    {feature.icon}
                  </motion.div>
                  <motion.div
                    animate={{
                      opacity: activeIndex === index ? 1 : 0.4,
                    }}
                    className={cn(
                      'text-3xl font-medium text-white transition-[font-size] duration-300',
                      activeIndex === index
                        ? 'text-xl lg:text-2xl xl:text-[40px]'
                        : 'text-xl xl:text-[32px]'
                    )}
                  >
                    {feature.title}
                  </motion.div>
                </div>
                <motion.div
                  animate={{
                    opacity: activeIndex === index ? 1 : 0,
                  }}
                  transition={{
                    duration: 0.3,
                    ease: 'easeIn',
                  }}
                  className='absolute inset-0 z-0 rounded-b-2xl'
                  style={{
                    background:
                      'radial-gradient(100% 100% at 50% 0%, rgba(0, 64, 128, 0) 0%, #0068FF 100%)',
                  }}
                >
                  <motion.img
                    src={feature.image}
                    alt={feature.title}
                    className='absolute inset-0 z-0 h-full w-full object-contain blur-xl'
                  />
                </motion.div>
              </Glasscard>
            </div>
          ))}
        </div>
      </motion.div>
    </Section>
  );
}
