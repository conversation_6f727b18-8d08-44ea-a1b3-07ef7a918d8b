'use client';

import { Glasscard } from '@/components/common/Card';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { SlashDiv } from '@/components/common/SlashBackground';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { ScrollMotionDiv } from '@/components/motion/ScrollMotionDiv';
export default function HowWeWorkSection() {
  const t = useTranslations('solutions');
  const features = [
    {
      title: t('phase_strategy'),
      content: 'phase_strategy_content',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'
        >
          <path
            d='M19.5014 4.49994L21.7327 4.94617C21.8525 4.97014 21.951 5.05806 21.9856 5.17661C22.0031 5.2342 22.0047 5.29545 21.9901 5.35385C21.9756 5.41225 21.9455 5.46562 21.9031 5.50828L20.0752 7.33849C19.8562 7.55641 19.56 7.67896 19.2512 7.67949H17.7348L13.9273 11.4904C14 11.7648 14.0133 12.0516 13.9665 12.3316C13.9197 12.6116 13.8138 12.8784 13.6558 13.1142C13.4979 13.3501 13.2915 13.5495 13.0505 13.6993C12.8095 13.8491 12.5393 13.9457 12.258 13.9829C11.9767 14.02 11.6907 13.9967 11.4191 13.9145C11.1475 13.8324 10.8966 13.6933 10.6829 13.5065C10.4693 13.3197 10.2978 13.0895 10.1801 12.8312C10.0623 12.5729 10.0009 12.2925 10 12.0086C9.99994 11.702 10.0704 11.3995 10.2059 11.1245C10.3414 10.8495 10.5384 10.6094 10.7815 10.4228C11.0247 10.2362 11.3075 10.1081 11.608 10.0484C11.9086 9.98866 12.2189 9.99896 12.5148 10.0785L16.3236 6.26621V4.75169C16.3236 4.44266 16.4461 4.14562 16.6645 3.92717L18.4937 2.09696C18.5363 2.05451 18.5896 2.02441 18.648 2.00987C18.7064 1.99532 18.7676 1.99688 18.8251 2.01437C18.9436 2.04901 19.0315 2.14758 19.0555 2.26746L19.5014 4.49994Z'
            fill='white'
          />
          <path
            d='M3.99616 12.0043C3.99753 13.1486 4.24427 14.2793 4.71975 15.3202C5.19522 16.361 5.88837 17.2878 6.75243 18.0379C7.61649 18.7881 8.63137 19.3442 9.7286 19.6687C10.8258 19.9933 11.9799 20.0787 13.113 19.9193C14.246 19.7598 15.3317 19.3592 16.2968 18.7444C17.2618 18.1297 18.0839 17.3151 18.7074 16.3556C19.3309 15.3961 19.7414 14.314 19.9112 13.1824C20.0811 12.0508 20.0062 10.8958 19.6918 9.79558C19.6476 9.66703 19.6301 9.53085 19.6403 9.39531C19.6504 9.25978 19.6881 9.12773 19.7509 9.0072C19.8137 8.88668 19.9004 8.7802 20.0057 8.69425C20.111 8.6083 20.2326 8.54468 20.3633 8.50726C20.4939 8.46983 20.6308 8.45939 20.7656 8.47657C20.9004 8.49375 21.0304 8.53819 21.1474 8.60718C21.2645 8.67617 21.3664 8.76828 21.4467 8.87788C21.5271 8.98749 21.5843 9.1123 21.6149 9.24473C22.2237 11.3755 22.1081 13.648 21.2861 15.7059C20.4641 17.7638 18.9823 19.4906 17.0731 20.6154C15.1639 21.7401 12.9354 22.1992 10.7371 21.9206C8.53888 21.642 6.49532 20.6414 4.92704 19.0759C3.36047 17.5079 2.35894 15.4639 2.07972 13.265C1.80049 11.066 2.25937 8.83662 3.38431 6.92674C4.50925 5.01686 6.23655 3.53469 8.29501 2.71292C10.3535 1.89116 12.6265 1.77634 14.7573 2.38649C15.011 2.46067 15.2251 2.63227 15.3527 2.86379C15.4803 3.09531 15.511 3.36793 15.4382 3.62207C15.3655 3.8762 15.1951 4.0912 14.9643 4.22006C14.7335 4.34893 14.461 4.38119 14.2065 4.30979C13.015 3.96761 11.7603 3.90697 10.5413 4.13266C9.32239 4.35836 8.17254 4.8642 7.18249 5.6103C6.19243 6.35641 5.38925 7.32236 4.8363 8.43197C4.28334 9.54159 3.99573 10.7645 3.99616 12.0043Z'
            fill='white'
          />
          <path
            d='M8.00064 11.9996C8.00071 12.7284 8.19977 13.4434 8.57632 14.0673C8.95287 14.6913 9.49264 15.2006 10.1374 15.5404C10.7822 15.8801 11.5074 16.0373 12.235 15.9952C12.9626 15.953 13.6648 15.713 14.266 15.301C14.867 14.888 15.3437 14.3185 15.6445 13.6541C15.9453 12.9898 16.0588 12.2558 15.9726 11.5316C15.9493 11.3559 15.9733 11.1772 16.042 11.0138C16.1108 10.8504 16.2218 10.7082 16.3638 10.602C16.5057 10.4958 16.6734 10.4294 16.8495 10.4095C17.0257 10.3896 17.2039 10.4171 17.366 10.489C17.5279 10.56 17.6682 10.6727 17.7724 10.8155C17.8767 10.9584 17.9412 11.1264 17.9593 11.3023C18.1042 12.5398 17.8602 13.7917 17.2613 14.8842C16.6624 15.9767 15.7382 16.8557 14.6171 17.3991C13.4959 17.9426 12.2334 18.1235 11.0048 17.9169C9.7761 17.7102 8.64225 17.1262 7.76059 16.2459C6.87892 15.3655 6.29317 14.2326 6.08464 13.0042C5.87611 11.7759 6.05514 10.5131 6.59688 9.39112C7.13863 8.26914 8.01621 7.34364 9.10783 6.74307C10.1994 6.14251 11.4509 5.89666 12.6886 6.03965C12.8212 6.05195 12.9499 6.09058 13.0673 6.15328C13.1847 6.21597 13.2884 6.30147 13.3723 6.40477C13.4563 6.50806 13.5187 6.62708 13.5561 6.75483C13.5934 6.88258 13.6049 7.0165 13.5898 7.14874C13.5747 7.28097 13.5334 7.40887 13.4682 7.52493C13.4031 7.64098 13.3154 7.74286 13.2103 7.8246C13.1053 7.90633 12.985 7.96628 12.8565 8.00092C12.728 8.03556 12.5939 8.04419 12.462 8.02632C11.9015 7.96125 11.3336 8.01548 10.7956 8.18545C10.2576 8.35543 9.76164 8.6373 9.3403 9.01255C8.91896 9.38781 8.58177 9.84795 8.35088 10.3628C8.11999 10.8776 8.00063 11.4354 8.00064 11.9996Z'
            fill='white'
          />
        </svg>
      ),
    },
    {
      title: t('phase_discovery'),
      content: 'phase_discovery_content',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='14'
          height='20'
          viewBox='0 0 14 20'
          fill='none'
        >
          <path
            d='M7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 9.38 1.19 11.47 3 12.74V15C3 15.2652 3.10536 15.5196 3.29289 15.7071C3.48043 15.8946 3.73478 16 4 16H10C10.2652 16 10.5196 15.8946 10.7071 15.7071C10.8946 15.5196 11 15.2652 11 15V12.74C12.81 11.47 14 9.38 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0ZM4 19C4 19.2652 4.10536 19.5196 4.29289 19.7071C4.48043 19.8946 4.73478 20 5 20H9C9.26522 20 9.51957 19.8946 9.70711 19.7071C9.89464 19.5196 10 19.2652 10 19V18H4V19Z'
            fill='white'
          />
        </svg>
      ),
    },
    {
      title: t('phase_design'),
      content: 'phase_design_content',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='20'
          viewBox='0 0 20 20'
          fill='none'
        >
          <path
            d='M14.8783 0.500146C15.428 0.494086 15.9615 0.676926 16.3908 1.01402L16.5681 1.16691L18.7689 3.36806C18.9954 3.58741 19.1763 3.84865 19.3018 4.13797C19.4292 4.43182 19.4963 4.74863 19.4999 5.06891C19.5034 5.38909 19.4434 5.70697 19.3226 6.00347C19.2016 6.30003 19.0225 6.56953 18.7961 6.79599C18.5696 7.02246 18.3002 7.2016 18.0037 7.32253C17.7072 7.44343 17.3894 7.50336 17.0692 7.49986C16.749 7.49631 16.4322 7.42919 16.1384 7.30172C15.8491 7.17621 15.5879 6.99442 15.3686 6.76795L13.1777 4.57856C13.1741 4.57501 13.1703 4.57133 13.1668 4.5677C12.7325 4.11352 12.4932 3.50708 12.5001 2.87862C12.5071 2.25002 12.7604 1.64942 13.2048 1.20491C13.6493 0.760402 14.2498 0.507096 14.8783 0.500146Z'
            fill='white'
          />
          <path
            d='M9.53359 4.59725C9.76776 4.50078 10.0249 4.47518 10.2733 4.52449C10.4599 4.56162 10.6358 4.63935 10.7875 4.75154L10.9306 4.87508L15.1244 9.066H15.1234C15.2921 9.23459 15.4108 9.44722 15.4657 9.67928C15.5206 9.91143 15.5094 10.1546 15.434 10.3809L13.621 15.8189V15.8199C13.5556 16.0153 13.4439 16.1922 13.2958 16.3355C13.1475 16.4789 12.9662 16.5853 12.7684 16.6441H12.768L1.65126 19.9469C1.42591 20.0137 1.18576 20.0177 0.958391 19.9581C0.731258 19.8984 0.525007 19.7772 0.361711 19.6085C0.198329 19.4396 0.083711 19.2291 0.0316329 19.0001C-0.0203406 18.7712 -0.00791602 18.5323 0.0663009 18.3097L3.67568 7.48836L3.72597 7.36043C3.78318 7.23528 3.86072 7.11976 3.95497 7.01863C4.08056 6.88393 4.23348 6.7775 4.4037 6.70711H4.40468L9.53359 4.59627V4.59725ZM9.49989 9.00008C8.67147 9.00008 7.99989 9.67165 7.99989 10.5001C7.99989 10.6514 8.02266 10.7974 8.06435 10.9351L2.74989 16.2501C2.47375 16.5262 2.47375 16.9739 2.74989 17.2501C3.02604 17.5262 3.47375 17.5262 3.74989 17.2501L9.06435 11.9351C9.20225 11.9769 9.34834 12.0001 9.49989 12.0001C10.3283 12.0001 10.9999 11.3285 10.9999 10.5001C10.9999 9.67165 10.3283 9.00008 9.49989 9.00008Z'
            fill='#FDFDFD'
          />
        </svg>
      ),
    },
    {
      title: t('phase_development'),
      content: 'phase_development_content',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='22'
          height='18'
          viewBox='0 0 22 18'
          fill='none'
        >
          <path
            d='M4.46967 4.46967C4.76256 4.17678 5.23732 4.17678 5.53022 4.46967C5.82311 4.76256 5.82311 5.23732 5.53022 5.53022L2.06049 8.99994L5.53022 12.4697L5.58197 12.5268C5.82203 12.8214 5.80473 13.2557 5.53022 13.5302C5.2557 13.8047 4.82137 13.822 4.5268 13.582L4.46967 13.5302L0.46967 9.53022C0.176777 9.23732 0.176777 8.76256 0.46967 8.46967L4.46967 4.46967Z'
            fill='white'
          />
          <path
            d='M17.4731 4.41795C17.1786 4.1779 16.7442 4.1952 16.4697 4.46971C16.1952 4.74422 16.1779 5.17855 16.418 5.47313L16.4697 5.53026L19.9394 8.99999L16.4697 12.4697C16.1768 12.7626 16.1768 13.2374 16.4697 13.5303C16.7626 13.8232 17.2374 13.8232 17.5303 13.5303L21.5303 9.53026C21.8232 9.23737 21.8232 8.76261 21.5303 8.46971L17.5303 4.46971L17.4731 4.41795Z'
            fill='white'
          />
          <path
            d='M13.1822 0.27233C12.7805 0.171888 12.3731 0.416031 12.2726 0.817741L8.27257 16.8177C8.17213 17.2195 8.41628 17.6269 8.81798 17.7274C9.21975 17.8279 9.62709 17.5837 9.72765 17.182L13.7277 1.182C13.8281 0.780229 13.584 0.37289 13.1822 0.27233Z'
            fill='white'
          />
        </svg>
      ),
    },
    {
      title: t('launch_scale'),
      content: 'launch_scale_content',
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'
        >
          <path
            d='M20.9 2.25782C20.8923 2.22027 20.874 2.18572 20.8474 2.1582C20.8207 2.13068 20.7867 2.11134 20.7495 2.10245C18.2655 1.49457 12.5259 3.66078 9.41633 6.77192C8.8617 7.32256 8.35609 7.92053 7.90521 8.55906C6.94629 8.47416 5.98737 8.54505 5.1701 8.90163C2.8642 9.91745 2.19283 12.568 2.00579 13.7082C1.99518 13.7706 1.99919 13.8346 2.01748 13.8952C2.03578 13.9558 2.06787 14.0114 2.11124 14.0574C2.15461 14.1035 2.20806 14.1389 2.26741 14.1608C2.32675 14.1827 2.39036 14.1905 2.45323 14.1837L6.15617 13.7749C6.1588 14.0543 6.17565 14.3334 6.20664 14.6111C6.22527 14.804 6.3108 14.9842 6.44838 15.1205L7.88231 16.5523C8.01864 16.6898 8.19863 16.7754 8.39124 16.7943C8.66716 16.8252 8.94446 16.842 9.22208 16.8448L8.81578 20.5464C8.80901 20.6094 8.81688 20.673 8.83879 20.7323C8.8607 20.7917 8.89605 20.8452 8.94207 20.8885C8.98809 20.9319 9.04352 20.9641 9.10402 20.9824C9.16453 21.0007 9.22845 21.0048 9.29079 20.9943C10.4278 20.8118 13.0807 20.1398 14.0896 17.8318C14.4459 17.0138 14.5188 16.0587 14.4366 15.1035C15.076 14.6522 15.675 14.146 16.2267 13.5906C19.3461 10.4842 21.498 4.86763 20.9 2.25782ZM13.1146 9.88476C12.8298 9.59985 12.6357 9.23679 12.5571 8.8415C12.4784 8.44621 12.5187 8.03645 12.6727 7.66406C12.8267 7.29166 13.0877 6.97335 13.4225 6.7494C13.7573 6.52545 14.1509 6.40591 14.5536 6.40591C14.9563 6.40591 15.3499 6.52545 15.6848 6.7494C16.0196 6.97335 16.2805 7.29166 16.4345 7.66406C16.5886 8.03645 16.6288 8.44621 16.5501 8.8415C16.4715 9.23679 16.2775 9.59985 15.9926 9.88476C15.8038 10.0741 15.5794 10.2243 15.3325 10.3267C15.0856 10.4292 14.8209 10.482 14.5536 10.482C14.2863 10.482 14.0216 10.4292 13.7747 10.3267C13.5278 10.2243 13.3035 10.0741 13.1146 9.88476Z'
            fill='white'
          />
          <path
            d='M7.64291 17.9733C7.38607 18.2306 6.9741 18.3309 6.47824 18.4167C5.36419 18.6066 4.38043 17.6438 4.58055 16.5173C4.65695 16.0903 4.88285 15.4917 5.02345 15.3511C5.05419 15.321 5.07464 15.2819 5.08192 15.2395C5.08919 15.197 5.08291 15.1534 5.06397 15.1147C5.04504 15.0761 5.0144 15.0443 4.97642 15.0241C4.93844 15.0038 4.89504 14.9961 4.85239 15.0019C4.2294 15.0781 3.64982 15.3606 3.20591 15.8044C2.10405 16.9073 2 21 2 21C2 21 6.09439 20.8959 7.19625 19.793C7.64131 19.3493 7.92411 18.7688 7.9991 18.1448C8.01645 17.9489 7.77789 17.8322 7.64291 17.9733Z'
            fill='white'
          />
        </svg>
      ),
    },
  ];
  return (
    <Section center className='py-10 xl:py-60'>
      <SlashDiv
        additionalHeight={400}
        className='relative -mt-48 hidden w-full items-center justify-center lg:flex lg:scale-65 xl:scale-85 2xl:scale-100'
      >
        <ScrollMotionDiv transformFrom={[0, 1]} transformTo={[-180, 0]} className=''>
          <img
            src='/solutions/world-map.png'
            alt=''
            className='size-full max-w-[1700px] object-contain'
          />
        </ScrollMotionDiv>
        <div className='absolute mb-240'>
          <HeroTitle
            tag={t('how_we_work')}
            title={t('our_delivery_approach')}
            description={t('our_delivery_approach_content')}
            descriptionClassName='max-w-[655px]'
          />
        </div>
        <CenterCard className='absolute z-10' />
        <FeaturesCard
          feature={features[0]}
          className='mr-100 mb-[120px]'
          angleFrom={-37}
          angleTo={120}
          clockRotate
          childClassName='right-full bottom-full '
          cardClassName='-mr-10 -mb-36'
        />
        <FeaturesCard
          feature={features[1]}
          className='mt-[120px] mr-100'
          angleFrom={110}
          angleTo={90}
          childClassName='right-[80%] top-full'
          cardClassName='-mr-10 -mt-36'
        />
        <FeaturesCard
          feature={features[2]}
          className='mt-100 mr-64'
          angleFrom={50}
          angleTo={85}
          childClassName='top-full'
          cardClassName=' -mt-4 ml-10'
        />
        <FeaturesCard
          feature={features[4]}
          className='mb-[120px] ml-100'
          angleFrom={-70}
          angleTo={110}
          childClassName='left-full bottom-full'
          cardClassName='-ml-10 -mb-36'
        />
        <FeaturesCard
          feature={features[3]}
          className='mt-[120px] ml-100'
          angleFrom={160}
          angleTo={100}
          childClassName='left-[80%] top-full'
          cardClassName='-ml-10 -mt-36'
          clockRotate
        />
      </SlashDiv>

      <SlashDiv className='flex w-full flex-col items-center gap-10 py-10 max-lg:mt-20 lg:hidden'>
        <HeroTitle
          tag={t('how_we_work')}
          title={t('our_delivery_approach')}
          description={t('our_delivery_approach_content')}
          descriptionClassName='max-w-[655px]'
        />
        <img
          src='/solutions/world-map.png'
          alt=''
          className='absolute top-0 min-w-[600px] object-contain'
        />
        <div className='relative flex w-full flex-col items-center gap-10'>
          <div className='absolute top-0 left-0 flex size-full items-center justify-center py-20'>
            <motion.div
              className='h-full w-[5px] bg-amber-50'
              animate={{
                background: [
                  'repeating-linear-gradient(#1259C4 10%, #DCEBFF 30% ,#1259C4 60%)',
                  'repeating-linear-gradient(#1259C4 110%, #DCEBFF 130%, #1259C4 160%)',
                ],

                transition: {
                  duration: 5,
                  repeat: Infinity,
                  ease: 'linear',
                },
              }}
            ></motion.div>
          </div>
          <CenterCard className='' />

          {features.map((feature, index) => (
            <Glasscard
              key={index}
              className={cn('w-full max-w-[530px] bg-[#4F80C629] p-6 !backdrop-blur-2xl')}
              borderColor='#ffffff54'
              borderWidth={2}
            >
              <div className='text-foreground flex flex-col gap-3'>
                <div className='flex size-11 items-center justify-center rounded-xl border border-white/10 bg-white/11'>
                  {feature.icon}
                </div>
                <div className='text-3xl font-medium lg:text-[40px]'>{feature.title}</div>
                <div className='text-sm'>
                  {t.rich(feature.content, {
                    ul: (chunks) => <ul className='list-disc'>{chunks}</ul>,
                    li: (chunks) => <li className='ml-7 list-disc'>{chunks}</li>,
                  })}
                </div>
              </div>
            </Glasscard>
          ))}
        </div>
      </SlashDiv>
    </Section>
  );
}

const FeaturesCard = ({
  feature,
  className,
  childClassName,
  cardClassName,
  angleFrom,
  angleTo,
  clockRotate,
}: {
  feature: any;
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
  childClassName?: React.HTMLAttributes<HTMLDivElement>['className'];
  angleFrom?: number;
  angleTo?: number;
  clockRotate?: boolean;
  cardClassName?: React.HTMLAttributes<HTMLDivElement>['className'];
}) => {
  const t = useTranslations('solutions');
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      className={cn('absolute', className)}
    >
      <motion.div className='relative size-[260px]' style={{}}>
        <motion.div
          className='relative size-full rounded-2xl'
          style={{
            maskImage: `conic-gradient(from ${angleFrom}deg, black 0deg ${angleTo}deg, transparent 0deg 360deg)`,
            WebkitMaskImage: `conic-gradient(from ${angleFrom}deg, black 0deg ${angleTo}deg, transparent 0deg 360deg)`,
          }}
        >
          <motion.div
            animate={{
              background: clockRotate
                ? [
                    `conic-gradient(from 0deg, #DCEBFF 10deg, #1259C4 60deg 80deg, #DCEBFF 85deg 360deg)`,
                    `conic-gradient(from 360deg, #DCEBFF 10deg, #1259C4 60deg 80deg, #DCEBFF 85deg 360deg)`,
                  ]
                : [
                    `conic-gradient(from 360deg, #DCEBFF 10deg, #1259C4 15deg 35deg, #DCEBFF 85deg 360deg)`,
                    `conic-gradient(from 0deg, #DCEBFF 10deg, #1259C4 15deg 35deg, #DCEBFF 85deg 360deg)`,
                  ],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: 'linear',
            }}
            className={cn(
              'absolute inset-0 rounded-2xl mask-[linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)] ![mask-composite:exclude] p-1 transition-[border-radius] will-change-transform content-[""] [-webkit-mask-composite:xor] [-webkit-mask:linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)]'
            )}
          ></motion.div>
        </motion.div>
        <div className={cn('absolute flex items-center justify-center', childClassName)}>
          <Glasscard
            className={cn('w-[430px] bg-[#4F80C629] p-6', cardClassName)}
            borderColor='#ffffff54'
            borderWidth={2}
          >
            <div className='text-foreground flex flex-col gap-3'>
              <div className='flex size-11 items-center justify-center rounded-xl border border-white/10 bg-white/11'>
                {feature.icon}
              </div>
              <div className='text-[40px] font-medium'>{feature.title}</div>
              <div className='text-sm'>
                {t.rich(feature.content, {
                  ul: (chunks) => <ul className='list-disc'>{chunks}</ul>,
                  li: (chunks) => <li className='ml-7 list-disc'>{chunks}</li>,
                })}
              </div>
            </div>
          </Glasscard>
        </div>
      </motion.div>
    </motion.div>
  );
};

const CenterCard = ({
  className,
}: {
  className?: React.HTMLAttributes<HTMLDivElement>['className'];
}) => {
  const t = useTranslations('solutions');
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      className={cn(className)}
    >
      <Glasscard
        className='bg-[#02245529]'
        borderColor='#ffffff54'
        autoRotate
        borderWidth={3}
      >
        <div className='relative flex flex-col items-center justify-center'>
          <motion.img
            src='/solutions/mask-card.svg'
            className='absolute size-[700px] object-contain'
            animate={{
              opacity: [0.8, 1],
              scale: [1.2, 1.4],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'easeInOut',
              repeatType: 'reverse',
            }}
            alt=''
          ></motion.img>
          <div className='text-foreground relative z-10 px-11 py-12 text-center text-[28px] font-bold whitespace-pre-wrap'>
            {t('our_solution_development_process')}
          </div>
        </div>
      </Glasscard>
    </motion.div>
  );
};
