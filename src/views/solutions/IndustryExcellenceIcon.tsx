export const FinanceIcon = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
    >
      <path
        d='M17.5 18.959H2.5C2.15833 18.959 1.875 18.6757 1.875 18.334C1.875 17.9923 2.15833 17.709 2.5 17.709H17.5C17.8417 17.709 18.125 17.9923 18.125 18.334C18.125 18.6757 17.8417 18.959 17.5 18.959Z'
        fill='#FDFDFD'
      />
      <path
        d='M4.66667 6.98242H3.33333C2.875 6.98242 2.5 7.35742 2.5 7.81576V14.9991C2.5 15.4574 2.875 15.8324 3.33333 15.8324H4.66667C5.125 15.8324 5.5 15.4574 5.5 14.9991V7.81576C5.5 7.34909 5.125 6.98242 4.66667 6.98242Z'
        fill='#FDFDFD'
      />
      <path
        d='M10.6667 4.32422H9.33333C8.875 4.32422 8.5 4.69922 8.5 5.15755V14.9992C8.5 15.4576 8.875 15.8326 9.33333 15.8326H10.6667C11.125 15.8326 11.5 15.4576 11.5 14.9992V5.15755C11.5 4.69922 11.125 4.32422 10.6667 4.32422Z'
        fill='#FDFDFD'
      />
      <path
        d='M16.6667 1.66602H15.3333C14.875 1.66602 14.5 2.04102 14.5 2.49935V14.9993C14.5 15.4577 14.875 15.8327 15.3333 15.8327H16.6667C17.125 15.8327 17.5 15.4577 17.5 14.9993V2.49935C17.5 2.04102 17.125 1.66602 16.6667 1.66602Z'
        fill='#FDFDFD'
      />
    </svg>
  );
};

export const AgricultureIcon = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='18'
      height='18'
      viewBox='0 0 18 18'
      fill='none'
    >
      <path
        d='M17.75 2.75C17.75 6.01884 15.6588 8.79925 12.7412 9.82635C12.2635 9.99452 11.7636 10.1157 11.2471 10.1843C10.6997 10.257 10.25 10.6977 10.25 11.25V14.75C10.25 15.0261 10.4739 15.25 10.75 15.25H14.75C15.0261 15.25 15.25 15.4739 15.25 15.75V16.75C15.25 17.3023 14.8023 17.75 14.25 17.75H3.25C2.97386 17.75 2.75 17.5261 2.75 17.25V15.75C2.75 15.4739 2.97386 15.25 3.25 15.25H7.25C7.52614 15.25 7.75 15.0261 7.75 14.75V13.75C7.75 13.1977 7.29986 12.757 6.75239 12.6842C3.08187 12.1964 0.25 9.0539 0.25 5.25V3.25C0.25 2.97386 0.473858 2.75 0.75 2.75H2.75C4.90894 2.75 6.85483 3.66221 8.22324 5.12223C8.82908 5.76863 9.32172 6.52239 9.66956 7.35191C9.76846 7.58778 9.99424 7.75 10.25 7.75C10.6744 7.75 11.0864 7.69713 11.4799 7.59761C11.9679 7.47417 12.2042 6.94428 12.0142 6.47813C11.578 5.40775 10.9616 4.42994 10.2028 3.58235C9.84516 3.18287 9.79942 2.57051 10.1954 2.20905C11.5287 0.992154 13.3027 0.25 15.25 0.25H17.25C17.5261 0.25 17.75 0.473858 17.75 0.75V2.75Z'
        fill='#FCFDFF'
      />
    </svg>
  );
};

export const ManufacturingIcon = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='21'
      height='20'
      viewBox='0 0 21 20'
      fill='none'
    >
      <path
        d='M18.1 7.21945C16.29 7.21945 15.55 5.93945 16.45 4.36945C16.97 3.45945 16.66 2.29945 15.75 1.77945L14.02 0.789448C13.23 0.319448 12.21 0.599448 11.74 1.38945L11.63 1.57945C10.73 3.14945 9.25 3.14945 8.34 1.57945L8.23 1.38945C7.78 0.599448 6.76 0.319448 5.97 0.789448L4.24 1.77945C3.33 2.29945 3.02 3.46945 3.54 4.37945C4.45 5.93945 3.71 7.21945 1.9 7.21945C0.86 7.21945 0 8.06945 0 9.11945V10.8794C0 11.9194 0.85 12.7794 1.9 12.7794C3.71 12.7794 4.45 14.0594 3.54 15.6294C3.02 16.5394 3.33 17.6994 4.24 18.2194L5.97 19.2094C6.76 19.6794 7.78 19.3995 8.25 18.6094L8.36 18.4194C9.26 16.8494 10.74 16.8494 11.65 18.4194L11.76 18.6094C12.23 19.3995 13.25 19.6794 14.04 19.2094L15.77 18.2194C16.68 17.6994 16.99 16.5294 16.47 15.6294C15.56 14.0594 16.3 12.7794 18.11 12.7794C19.15 12.7794 20.01 11.9294 20.01 10.8794V9.11945C20 8.07945 19.15 7.21945 18.1 7.21945ZM10 13.2494C8.21 13.2494 6.75 11.7894 6.75 9.99945C6.75 8.20945 8.21 6.74945 10 6.74945C11.79 6.74945 13.25 8.20945 13.25 9.99945C13.25 11.7894 11.79 13.2494 10 13.2494Z'
        fill='#FCFDFF'
      />
    </svg>
  );
};

export const RetailIcon = () => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        d='M16.25 22.5C17.2165 22.5 18 21.7165 18 20.75C18 19.7835 17.2165 19 16.25 19C15.2835 19 14.5 19.7835 14.5 20.75C14.5 21.7165 15.2835 22.5 16.25 22.5Z'
        fill='#FCFDFF'
      />
      <path
        d='M8.25 22.5C9.2165 22.5 10 21.7165 10 20.75C10 19.7835 9.2165 19 8.25 19C7.2835 19 6.5 19.7835 6.5 20.75C6.5 21.7165 7.2835 22.5 8.25 22.5Z'
        fill='#FCFDFF'
      />
      <path
        d='M4.84 3.94L4.64 6.39C4.6 6.86 4.97 7.25 5.44 7.25H20.75C21.17 7.25 21.52 6.93 21.55 6.51C21.68 4.74 20.33 3.3 18.56 3.3H6.27C6.17 2.86 5.97 2.44 5.66 2.09C5.16 1.56 4.46 1.25 3.74 1.25H2C1.59 1.25 1.25 1.59 1.25 2C1.25 2.41 1.59 2.75 2 2.75H3.74C4.05 2.75 4.34 2.88 4.55 3.1C4.76 3.33 4.86 3.63 4.84 3.94Z'
        fill='#FCFDFF'
      />
      <path
        d='M20.5101 8.75H5.17005C4.75005 8.75 4.41005 9.07 4.37005 9.48L4.01005 13.83C3.87005 15.54 5.21005 17 6.92005 17H18.0401C19.5401 17 20.8601 15.77 20.9701 14.27L21.3001 9.6C21.3401 9.14 20.9801 8.75 20.5101 8.75Z'
        fill='#FCFDFF'
      />
    </svg>
  );
};
