'use client';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { AuroraBackground } from '@/components/layout/AuroraBackground';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { cn } from '@/utils/cn';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';

export default function HeroSection() {
  const t = useTranslations('solutions');
  return (
    <Section className='flex lg:min-h-[820px]'>
      <AuroraBackground>
        <div
          className='absolute top-20 h-full w-full opacity-30 lg:hidden'
          style={{
            maskImage:
              'linear-gradient(to top, transparent 0%, transparent 35%,  black 60%)',
          }}
        >
          <motion.img src='/logo-3d.webp' alt='' className='size-full object-contain' />
        </div>
        <div className='container mt-32 grid w-full grid-cols-1 place-items-center md:mt-60 lg:grid-cols-2'>
          <div className='hidden items-center justify-center lg:flex'>
            <motion.img
              src='/solutions/hero.gif'
              alt=''
              className='absolute size-[130%] object-contain opacity-20'
            />
            <img src='/logo-3d.webp' alt='' className='z-10 size-80 object-contain' />
            <motion.img
              animate={{
                opacity: [0.4, 1],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: 'easeInOut',
                repeatType: 'reverse',
              }}
              src='/logo-3d.webp'
              alt=''
              className='absolute size-100 object-contain blur-2xl'
            />
          </div>
          <div>
            <ScrollReveal className='items-enter relative flex flex-col items-center justify-center gap-1 bg-[linear-gradient(90deg,rgba(0,34,67,0.4)_0%,rgba(16,50,99,0.4)_50%,rgba(0,34,67,0.4)_100%)] p-4 text-center lg:p-10'>
              <motion.div
                animate={{
                  background: [
                    'repeating-conic-gradient(from 0deg, #ffffff78 0%, transparent 10%, #ffffff78 50%)',
                    'repeating-conic-gradient(from 360deg, #ffffff78 0%, transparent 10%, #ffffff78 50%)',
                  ],
                }}
                transition={{
                  duration: 10,
                  repeat: Infinity,
                  ease: 'linear',
                }}
                style={{}}
                className={cn(
                  'absolute inset-0 mask-[linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)] ![mask-composite:exclude] p-0.5 transition-[border-radius] content-[""] [-webkit-mask-composite:xor] [-webkit-mask:linear-gradient(#000_0_0)_content-box,linear-gradient(#000_0_0)]'
                )}
              ></motion.div>

              <h1 className='text-2xl font-bold lg:text-[32px]'>
                {t.rich('enterprise_technology_solutions', {
                  primary: (chunks) => <span className='text-primary'>{chunks}</span>,
                })}
              </h1>
              <p className='text-lg lg:text-xl'>{t('built_for_your_success')}</p>
              <div className='text-base opacity-80 lg:text-lg'>
                {t('built_for_your_success_content')}
              </div>
            </ScrollReveal>
          </div>
        </div>
      </AuroraBackground>
    </Section>
  );
}
