'use client';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { Button } from '@/components/common/Button';
import { Link } from '@/i18n/navigation';
import { getCdnUrl } from '@/utils/url/asset';
import { ScrollMotionDiv } from '@/components/motion/ScrollMotionDiv';

export default function ConnectToExpertSection() {
  const t = useTranslations('solutions');
  return (
    <Section center className='relative overflow-hidden'>
      <ScrollMotionDiv
        transformFrom={[0, 1]}
        transformTo={[0, -300]}
        scrollX
        className='absolute top-0 left-0 h-full w-[calc(100%+300px)]'
      >
        <motion.img
          src={getCdnUrl('/solutions/connect.webp')}
          className='h-full w-full object-cover'
        />
      </ScrollMotionDiv>
      <div
        className='absolute z-10 h-full w-full'
        style={{
          background:
            'linear-gradient(90deg, rgba(50, 127, 239, 0.7) 0%, rgba(16, 50, 99, 0.7) 100%)',
        }}
      ></div>

      <div className='z-20 my-15 flex max-w-[869px] flex-col items-center gap-4'>
        <div className='text-center text-lg font-bold lg:text-3xl'>
          {t('start_your_digital_transformation')}
        </div>
        <div className='text-center text-sm font-medium tracking-[2.16%] opacity-80 lg:text-base'>
          {t('start_your_digital_transformation_content')}
        </div>
        <Link href='/contact'>
          <Button className='mt-4' aria-label='Connect with our experts'>
            {t('connect_with_our_experts')}
          </Button>
        </Link>
      </div>
    </Section>
  );
}
