'use client';
import { CarouselButton } from '@/components/common/CarouselButton';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { SlashDiv } from '@/components/common/SlashBackground';
import Tag from '@/components/common/Tag';
import LogoIcon from '@/components/layout/LoadingIndicator/LogoIcon';
import TextReveal from '@/components/motion/TextReveal';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { cn } from '@/utils/cn';
import { getCdnUrl } from '@/utils/url/asset';
import { AnimatePresence, motion, useInView } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { useState, useRef, useEffect, useCallback } from 'react';
import { match, P } from 'ts-pattern';

export default function OurCoreSolutionsSection() {
  const t = useTranslations('solutions');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [width, setWidth] = useState(0);
  const divRef = useRef<HTMLImageElement>(null);
  const [direction, setDirection] = useState<'left' | 'right'>('right');
  useEffect(() => {
    if (divRef.current) {
      setWidth(divRef.current.offsetWidth);
    }

    const handleResize = () => {
      if (divRef.current) {
        setWidth(divRef.current.offsetWidth);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const solutionsSections = [
    {
      title: 'ai_insights',
      content: 'ai_insights_content',
      keyTitle: 'key_innovations',
      innovations: [
        'predictive_analytics_forecasting',
        'computer_vision_pattern_recognition',
        'real_time_decision_support_systems',
        'natural_language_understanding',
        'intelligent_process_automation',
      ],
      icons: [
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='18'
          height='19'
          viewBox='0 0 18 19'
          fill='none'
        >
          <path
            fillRule='evenodd'
            clipRule='evenodd'
            d='M0 3.5C0 2.70435 0.316071 1.94129 0.87868 1.37868C1.44129 0.816071 2.20435 0.5 3 0.5H15C15.7956 0.5 16.5587 0.816071 17.1213 1.37868C17.6839 1.94129 18 2.70435 18 3.5V15.5C18 16.2956 17.6839 17.0587 17.1213 17.6213C16.5587 18.1839 15.7956 18.5 15 18.5H3C2.20435 18.5 1.44129 18.1839 0.87868 17.6213C0.316071 17.0587 0 16.2956 0 15.5V3.5ZM10 5.5C10 5.23478 9.89464 4.98043 9.70711 4.79289C9.51957 4.60536 9.26522 4.5 9 4.5C8.73478 4.5 8.48043 4.60536 8.29289 4.79289C8.10536 4.98043 8 5.23478 8 5.5V13.5C8 13.7652 8.10536 14.0196 8.29289 14.2071C8.48043 14.3946 8.73478 14.5 9 14.5C9.26522 14.5 9.51957 14.3946 9.70711 14.2071C9.89464 14.0196 10 13.7652 10 13.5V5.5ZM6 8.5C6 8.23478 5.89464 7.98043 5.70711 7.79289C5.51957 7.60536 5.26522 7.5 5 7.5C4.73478 7.5 4.48043 7.60536 4.29289 7.79289C4.10536 7.98043 4 8.23478 4 8.5V13.5C4 13.7652 4.10536 14.0196 4.29289 14.2071C4.48043 14.3946 4.73478 14.5 5 14.5C5.26522 14.5 5.51957 14.3946 5.70711 14.2071C5.89464 14.0196 6 13.7652 6 13.5V8.5ZM14 11.5C14 11.2348 13.8946 10.9804 13.7071 10.7929C13.5196 10.6054 13.2652 10.5 13 10.5C12.7348 10.5 12.4804 10.6054 12.2929 10.7929C12.1054 10.9804 12 11.2348 12 11.5V13.5C12 13.7652 12.1054 14.0196 12.2929 14.2071C12.4804 14.3946 12.7348 14.5 13 14.5C13.2652 14.5 13.5196 14.3946 13.7071 14.2071C13.8946 14.0196 14 13.7652 14 13.5V11.5Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='18'
          height='21'
          viewBox='0 0 18 21'
          fill='none'
        >
          <path
            d='M15.4619 0.5C16.1349 0.500097 16.7799 0.761999 17.2559 1.22754C17.7318 1.69324 17.9999 2.32481 18 2.9834V12.0166C17.9999 12.6752 17.7318 13.3068 17.2559 13.7725C16.7799 14.238 16.1349 14.4999 15.4619 14.5H2.53809C1.86513 14.4999 1.22008 14.238 0.744141 13.7725C0.268201 13.3068 0.000128084 12.6752 0 12.0166V2.9834C0.000127955 2.32481 0.2682 1.69324 0.744141 1.22754C1.22008 0.761998 1.86513 0.500097 2.53809 0.5H15.4619ZM9.81934 4.80176C9.53444 4.14112 8.56273 4.16215 8.32227 4.86523L7.99805 5.81445L7.96582 5.90137C7.8791 6.1142 7.74719 6.30611 7.58008 6.46387C7.41312 6.62141 7.21426 6.74113 6.99707 6.81543L6.04785 7.13965L5.98438 7.16504C5.32381 7.44953 5.34453 8.42059 6.04785 8.66113L6.99707 8.98535L7.08301 9.01758C7.29584 9.10432 7.48775 9.23618 7.64551 9.40332C7.8033 9.57055 7.92377 9.76971 7.99805 9.9873L8.32227 10.9355L8.34668 11C8.63121 11.6603 9.60343 11.6389 9.84375 10.9355L10.167 9.98633L10.2002 9.90039C10.2869 9.68766 10.418 9.49561 10.585 9.33789C10.7521 9.18017 10.9515 9.05964 11.1689 8.98535L12.1182 8.66113L12.1816 8.63672C12.8418 8.35182 12.8211 7.3802 12.1182 7.13965L11.1689 6.81543L11.082 6.7832C10.8693 6.69651 10.6773 6.56549 10.5195 6.39844C10.3617 6.23129 10.2413 6.03197 10.167 5.81445L9.84277 4.86621L9.81934 4.80176ZM12.2979 3.47949C12.214 3.47944 12.1317 3.50589 12.0635 3.55469C11.9953 3.60347 11.9441 3.67264 11.917 3.75195L11.7764 4.16406L11.3643 4.30469L11.3164 4.32422C11.245 4.3592 11.1854 4.41466 11.1455 4.4834C11.1056 4.55219 11.0877 4.63156 11.0928 4.71094C11.0979 4.79035 11.1264 4.86651 11.1748 4.92969C11.2232 4.99281 11.289 5.04082 11.3643 5.06641L11.7773 5.20703L11.918 5.61914L11.9365 5.66602C11.9714 5.73742 12.027 5.79694 12.0957 5.83691C12.1645 5.87687 12.2439 5.89564 12.3232 5.89062C12.4026 5.88559 12.4788 5.85693 12.542 5.80859C12.6052 5.76026 12.6531 5.69445 12.6787 5.61914L12.8193 5.20605L13.2314 5.06543L13.2783 5.0459C13.3498 5.01091 13.4093 4.95555 13.4492 4.88672C13.4891 4.81791 13.508 4.73857 13.5029 4.65918C13.4978 4.58002 13.4691 4.50442 13.4209 4.44141C13.3725 4.37826 13.3067 4.33029 13.2314 4.30469L12.8184 4.16406L12.6777 3.75195L12.6582 3.70508C12.6252 3.63772 12.5743 3.58079 12.5107 3.54102C12.447 3.50125 12.373 3.47951 12.2979 3.47949Z'
            fill='white'
          />
          <path
            d='M11.1316 16L11.1922 16.0024C11.4923 16.0245 11.7466 16.2086 11.8208 16.4646L12.4837 18.7546L13.2919 19.4525C13.4951 19.628 13.5559 19.892 13.4459 20.1213C13.3359 20.3505 13.0768 20.5 12.7895 20.5H5.21051C4.92316 20.5 4.6641 20.3505 4.5541 20.1213C4.44413 19.892 4.50493 19.628 4.70814 19.4525L5.51628 18.7546L6.17916 16.4646L6.19628 16.4147C6.29382 16.1685 6.56271 16 6.86841 16H11.1316Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='22'
          height='21'
          viewBox='0 0 22 21'
          fill='none'
        >
          <path
            d='M21.2929 14.7929C21.6834 15.1834 21.6834 15.8166 21.2929 16.2071L17.7071 19.7929C17.3166 20.1834 16.6834 20.1834 16.2929 19.7929L14.2071 17.7071C13.8166 17.3166 13.8166 16.6834 14.2071 16.2929L14.2929 16.2071C14.6834 15.8166 15.3166 15.8166 15.7071 16.2071L16.2929 16.7929C16.6834 17.1834 17.3166 17.1834 17.7071 16.7929L19.7929 14.7071C20.1834 14.3166 20.8166 14.3166 21.2071 14.7071L21.2929 14.7929ZM11.5 17.5C11.5 16 12 14.7 12.9 13.6L9.97451 11.7931C9.67955 11.6109 9.5 11.289 9.5 10.9423V6.25C9.5 5.83579 9.83579 5.5 10.25 5.5C10.6642 5.5 11 5.83579 11 6.25V10.14C11 10.4879 11.1808 10.8108 11.4774 10.9926L14.1 12.6C15.1 11.9 16.2 11.5 17.5 11.5C18.5 11.5 19.5 11.8 20.3 12.2C20.4 11.6 20.5 11.1 20.5 10.5C20.5 5 16 0.5 10.5 0.5C5 0.5 0.5 5 0.5 10.5C0.5 16 5 20.5 10.5 20.5C11.1 20.5 11.7 20.4 12.2 20.3C11.8 19.5 11.5 18.5 11.5 17.5Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='22'
          height='21'
          viewBox='0 0 22 21'
          fill='none'
        >
          <path
            d='M11 0C8.9233 0 6.89323 0.615814 5.16652 1.76957C3.4398 2.92332 2.09399 4.5632 1.29927 6.48182C0.504549 8.40045 0.296614 10.5116 0.701759 12.5484C1.1069 14.5852 2.10693 16.4562 3.57538 17.9246C5.04383 19.3931 6.91476 20.3931 8.95156 20.7982C10.9884 21.2034 13.0996 20.9955 15.0182 20.2007C16.9368 19.406 18.5767 18.0602 19.7304 16.3335C20.8842 14.6068 21.5 12.5767 21.5 10.5C21.4968 7.7162 20.3896 5.04733 18.4211 3.07889C16.4527 1.11044 13.7838 0.00317604 11 0ZM2.015 10.7955L3.0185 11.13L4.25 12.9772V13.9395C4.25005 14.1384 4.32909 14.3291 4.46975 14.4697L6.5 16.5V18.2828C5.17871 17.5176 4.07378 16.429 3.28918 15.1192C2.50458 13.8094 2.06611 12.3215 2.015 10.7955ZM11 19.5C10.3512 19.4985 9.70442 19.4268 9.071 19.2863L9.5 18L10.8538 14.616C10.8989 14.503 10.9158 14.3808 10.9031 14.2598C10.8905 14.1388 10.8485 14.0227 10.781 13.9215L9.72276 12.3337C9.65424 12.2311 9.56144 12.1469 9.45259 12.0887C9.34374 12.0304 9.2222 12 9.09875 12H5.40125L4.46525 10.5953L6.0605 9H7.25V10.5H8.75V8.4495L11.651 3.372L10.349 2.628L9.70776 3.75H7.65125L6.8375 2.52825C7.93086 1.95123 9.13249 1.60846 10.3657 1.52181C11.599 1.43516 12.8367 1.60654 14 2.025V4.5C14 4.69891 14.079 4.88968 14.2197 5.03033C14.3603 5.17098 14.5511 5.25 14.75 5.25H15.8488C15.9722 5.25002 16.0937 5.21957 16.2026 5.16135C16.3114 5.10313 16.4042 5.01894 16.4728 4.91625L17.1305 3.92925C17.9876 4.72778 18.6806 5.68595 19.1705 6.75H16.115C15.9417 6.7501 15.7738 6.81023 15.6398 6.92017C15.5058 7.0301 15.414 7.18304 15.38 7.353L14.8378 10.7055C14.8121 10.8633 14.8375 11.0252 14.9104 11.1676C14.9833 11.3099 15.0997 11.4253 15.2428 11.4967L17.75 12.75L18.2638 15.792C17.4313 16.9392 16.3392 17.8732 15.0768 18.5176C13.8144 19.162 12.4174 19.4987 11 19.5Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='22'
          height='22'
          viewBox='0 0 22 22'
          fill='none'
        >
          <path
            d='M16.6468 10C16.823 10.0002 16.981 10.1101 17.0403 10.2744L17.4632 11.4482C17.6147 11.4858 17.7458 11.5243 17.8558 11.5635C17.9757 11.6063 18.1317 11.6726 18.3235 11.7617L19.3099 11.2393C19.3902 11.1965 19.4827 11.1813 19.5725 11.1953C19.6626 11.2095 19.7465 11.2529 19.8099 11.3184L20.677 12.2148C20.7921 12.3341 20.8246 12.5088 20.76 12.6611L20.2972 13.7461C20.3735 13.8868 20.435 14.0077 20.4817 14.1084C20.5321 14.2176 20.5948 14.3697 20.6692 14.5645L21.7474 15.0264C21.9093 15.0954 22.0091 15.2573 21.9983 15.4307L21.9192 16.6758C21.9138 16.7565 21.8852 16.834 21.8362 16.8984C21.7872 16.9629 21.7202 17.0112 21.6438 17.0381L20.6224 17.4014C20.5932 17.542 20.5619 17.6631 20.5296 17.7646C20.4772 17.9221 20.4176 18.0775 20.3509 18.2295L20.8645 19.3633C20.901 19.4429 20.911 19.5325 20.8929 19.6182C20.8747 19.7038 20.8286 19.7809 20.763 19.8389L19.7874 20.7109C19.7234 20.768 19.6431 20.8042 19.5579 20.8145C19.4726 20.8247 19.3857 20.809 19.3099 20.7686L18.305 20.2354C18.1478 20.319 17.9851 20.3921 17.8186 20.4551L17.3792 20.6201L16.9895 21.7002C16.9603 21.779 16.9085 21.8477 16.8401 21.8965C16.7717 21.9453 16.6898 21.9716 16.6058 21.9736L15.4661 22C15.3799 22.002 15.2947 21.9775 15.2229 21.9297C15.1511 21.8817 15.095 21.812 15.0638 21.7314L14.6048 20.5156C14.448 20.4619 14.2925 20.4041 14.139 20.3418C14.0134 20.2874 13.8894 20.2288 13.7679 20.166L12.6282 20.6533C12.5532 20.6854 12.4703 20.6948 12.3899 20.6807C12.3095 20.6665 12.2347 20.6292 12.1751 20.5732L11.3313 19.7812C11.2685 19.7228 11.2259 19.6457 11.2093 19.5615C11.1927 19.4773 11.2026 19.3895 11.2386 19.3115L11.7288 18.2441C11.6638 18.1175 11.6039 17.9883 11.5481 17.8574C11.4831 17.6971 11.4224 17.5351 11.3675 17.3711L10.2942 17.0439C10.2069 17.0176 10.1306 16.9628 10.0774 16.8887C10.0243 16.8146 9.99731 16.7249 10.0003 16.6338L10.0423 15.4814C10.0454 15.4063 10.0682 15.3333 10.1097 15.2705C10.1511 15.2078 10.2089 15.1574 10.2767 15.125L11.4036 14.584C11.4556 14.3928 11.5013 14.2428 11.5413 14.1348C11.5979 13.9898 11.6609 13.8475 11.7298 13.708L11.2415 12.6758C11.2043 12.5975 11.1931 12.509 11.2093 12.4238C11.2255 12.3389 11.2682 12.2612 11.3313 12.2021L12.1741 11.4062C12.2331 11.3505 12.3072 11.3125 12.387 11.2979C12.4668 11.2832 12.5494 11.2921 12.6243 11.3232L13.763 11.7939C13.8884 11.7105 14.0027 11.6418 14.1058 11.5879C14.2285 11.5231 14.3941 11.4535 14.6009 11.3799L14.9964 10.2754C15.0259 10.1949 15.0795 10.1254 15.1497 10.0762C15.22 10.0269 15.3041 10.0002 15.3899 10H16.6468ZM16.014 14.2109C15.0139 14.211 14.2035 15.0123 14.2034 16.001C14.2034 16.9898 15.0138 17.792 16.014 17.792C17.0141 17.792 17.8245 16.9898 17.8245 16.001C17.8244 15.0123 17.0141 14.2109 16.014 14.2109ZM8.86259 0C9.09743 0.000204421 9.30775 0.146417 9.387 0.365234L9.95243 1.93164C10.1541 1.98168 10.3285 2.0328 10.4749 2.08496C10.6348 2.14201 10.8433 2.22986 11.0989 2.34863L12.4143 1.65234C12.5215 1.5954 12.645 1.57487 12.7649 1.59375C12.8848 1.61275 12.996 1.67062 13.0804 1.75781L14.2366 2.95312C14.39 3.11228 14.4332 3.34575 14.347 3.54883L13.7308 4.99414C13.8326 5.18179 13.9145 5.34318 13.9768 5.47754C14.044 5.62312 14.1267 5.82632 14.2259 6.08594L15.6643 6.70117C15.88 6.79332 16.0137 7.01024 15.9993 7.24121L15.8938 8.90039C15.8902 8.95441 15.8775 9.00747 15.8587 9.05762C15.796 9.02113 15.7228 9.00007 15.6468 9H14.3909C14.3051 9.00015 14.221 9.02705 14.1507 9.07617C14.0805 9.12537 14.0269 9.1949 13.9974 9.27539L13.6009 10.3799C13.3944 10.4534 13.2294 10.5232 13.1067 10.5879C13.0037 10.6418 12.8893 10.7106 12.764 10.7939L11.6253 10.3232C11.5505 10.2922 11.4676 10.2834 11.388 10.2979C11.3083 10.3125 11.234 10.3506 11.1751 10.4062L10.3323 11.2021C10.2691 11.2612 10.2265 11.3389 10.2102 11.4238C10.1941 11.509 10.2052 11.5975 10.2425 11.6758L10.7308 12.708C10.6619 12.8475 10.5988 12.9899 10.5423 13.1348C10.5023 13.2427 10.4566 13.3928 10.4046 13.584L9.27763 14.125C9.20988 14.1574 9.15204 14.2079 9.11064 14.2705C9.06922 14.3333 9.04541 14.4063 9.04228 14.4814L9.00028 15.6338C8.99733 15.7242 9.0251 15.813 9.07743 15.8867C8.99568 15.9347 8.90323 15.9635 8.8079 15.9658L7.28837 16C7.17333 16.0027 7.05986 15.9691 6.96415 15.9053C6.86858 15.8414 6.79484 15.7497 6.75321 15.6426L6.13993 14.0205C5.93129 13.949 5.72421 13.8719 5.51982 13.7891C5.35251 13.7166 5.18667 13.6384 5.0247 13.5547L3.50517 14.2041C3.40518 14.247 3.29396 14.259 3.18681 14.2402C3.07982 14.2213 2.97993 14.172 2.90068 14.0977L1.77665 13.042C1.69262 12.964 1.63487 12.8615 1.61259 12.749C1.59038 12.6366 1.60467 12.5201 1.65263 12.416L2.30595 10.9922C2.21936 10.8234 2.13911 10.651 2.06474 10.4766C1.97813 10.2629 1.89777 10.0467 1.8245 9.82812L0.392864 9.3916C0.276647 9.35647 0.174668 9.28412 0.103801 9.18555C0.0329568 9.0868 -0.00363736 8.96717 0.000285586 8.8457L0.0559496 7.30859C0.0601315 7.20843 0.0915587 7.11102 0.14677 7.02734C0.202014 6.94371 0.279956 6.87719 0.370403 6.83398L1.87236 6.1123C1.94164 5.85756 2.00265 5.65763 2.05595 5.51367C2.13139 5.32038 2.21605 5.1304 2.3079 4.94434L1.65654 3.56836C1.60677 3.46399 1.59105 3.34603 1.61259 3.23242C1.63418 3.1188 1.69207 3.01443 1.77665 2.93555L2.8997 1.875C2.97819 1.80084 3.07671 1.75104 3.1829 1.73145C3.28919 1.71196 3.39951 1.72319 3.49931 1.76465L5.01786 2.3916C5.18514 2.28028 5.33841 2.18912 5.47587 2.11719C5.63955 2.03086 5.85961 1.93788 6.13505 1.83984L6.66239 0.367188C6.70182 0.259744 6.77371 0.167191 6.86747 0.101562C6.96125 0.0360264 7.07337 0.000197094 7.18779 0H8.86259ZM8.01982 5.61523C6.68626 5.61523 5.60478 6.68357 5.60478 8.00195C5.60501 9.32014 6.68641 10.3887 8.01982 10.3887C9.35282 10.3882 10.4327 9.31985 10.4329 8.00195C10.4329 6.68386 9.35296 5.61571 8.01982 5.61523Z'
            fill='white'
          />
        </svg>,
      ],
    },
    {
      title: 'emerging_technologies',
      content: 'emerging_technologies_content',
      keyTitle: 'key_innovations',
      innovations: [
        'cloud_native_solutions',
        'blockchain_web3',
        'advanced_automation',
        'iot',
        'edge_computing',
        'extended_reality',
      ],
      icons: [
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'
        >
          <path
            d='M18.7375 9.6625C18.4296 8.06661 17.5895 6.62956 16.3606 5.59679C15.1318 4.56403 13.5905 3.99963 12 4C9.35083 4 7.05 5.5375 5.90417 7.7875C4.55688 7.93641 3.31092 8.58926 2.4057 9.62061C1.50047 10.652 0.999843 11.9891 1 13.375C1 16.4781 3.46583 19 6.5 19H18.4167C20.9467 19 23 16.9 23 14.3125C23 11.8375 21.1208 9.83125 18.7375 9.6625Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='20'
          viewBox='0 0 20 20'
          fill='none'
        >
          <path
            d='M9.73733 0.0545429C9.82033 0.0185563 9.9097 0 10 0C10.0903 0 10.1797 0.0185563 10.2627 0.0545429L18.2751 3.52822C18.6766 3.70228 18.6766 4.27167 18.2751 4.44572L10.1989 7.94653C10.072 8.00152 9.92801 8.00152 9.80114 7.94653L1.72492 4.44572C1.32341 4.27167 1.32339 3.70228 1.72489 3.52822L9.73733 0.0545429ZM0.698858 5.46859C0.368695 5.32547 0 5.56749 0 5.92734V15.5092C0 15.779 0.16 16.0217 0.404 16.1296L8.63449 19.6971C8.96465 19.8402 9.33333 19.5982 9.33333 19.2383V9.5396C9.33333 9.34034 9.21502 9.1601 9.03219 9.08085L0.698858 5.46859ZM10.6667 9.5396C10.6667 9.34034 10.785 9.1601 10.9678 9.08085L19.3011 5.46859C19.6313 5.32547 20 5.56749 20 5.92734V15.5092C20.0001 15.6413 19.9619 15.7705 19.8901 15.8808C19.8183 15.991 19.716 16.0776 19.596 16.1296L11.3655 19.6971C11.0354 19.8402 10.6667 19.5982 10.6667 19.2383V9.5396Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'
        >
          <path
            d='M13.0775 2C13.3715 2.00001 13.6348 2.18304 13.7338 2.45703L14.4398 4.41406C14.6923 4.47669 14.9099 4.54116 15.0931 4.60645C15.2931 4.67777 15.5535 4.78692 15.8734 4.93555L17.517 4.06641C17.6511 3.99499 17.8054 3.96856 17.9554 3.99219C18.1055 4.01585 18.2434 4.08814 18.349 4.19727L19.7953 5.69238C19.987 5.89131 20.0408 6.1817 19.933 6.43555L19.1625 8.24316C19.2897 8.47759 19.3921 8.6788 19.4701 8.84668C19.5541 9.02862 19.6587 9.28193 19.7826 9.60645L21.5795 10.377C21.8493 10.492 22.0163 10.7619 21.9984 11.0508L21.8666 13.126C21.8576 13.2607 21.8086 13.3896 21.7269 13.4971C21.6453 13.6045 21.5339 13.6857 21.4066 13.7305L19.7045 14.3359C19.7016 14.3498 19.6976 14.3633 19.6947 14.377C19.1799 14.1357 18.6055 14 17.9994 14C15.7903 14.0001 13.9994 15.7909 13.9994 18C13.9994 18.5978 14.1315 19.1645 14.3666 19.6738L14.2992 19.7002L13.6498 21.5C13.6011 21.6315 13.5139 21.7457 13.3998 21.8271C13.2857 21.9085 13.1493 21.9537 13.0092 21.957L11.1097 22C10.9658 22.0033 10.8242 21.9618 10.7045 21.8818C10.5848 21.8018 10.4928 21.687 10.4408 21.5527L9.6742 19.5254C9.41309 19.4359 9.15458 19.3391 8.89881 19.2354C8.68955 19.1448 8.48322 19.0471 8.28064 18.9424L6.38025 19.7549C6.25532 19.8084 6.11761 19.8243 5.98377 19.8008C5.84967 19.7772 5.72464 19.7153 5.62537 19.6221L4.22009 18.3027C4.11503 18.2052 4.04285 18.0771 4.01502 17.9365C3.98719 17.7959 4.00483 17.6497 4.06482 17.5195L4.8822 15.7402C4.77394 15.5292 4.67246 15.3139 4.57947 15.0957C4.47121 14.8287 4.37124 14.5584 4.27966 14.2852L2.48963 13.7402C2.34421 13.6963 2.21785 13.6049 2.12927 13.4814C2.04067 13.3579 1.99541 13.2086 2.00037 13.0566L2.0697 11.1357C2.07493 11.0105 2.11395 10.8888 2.18299 10.7842C2.25205 10.6795 2.34915 10.596 2.46228 10.542L4.34021 9.63965C4.42683 9.32116 4.50306 9.07153 4.5697 8.8916C4.66403 8.64994 4.76932 8.4123 4.88416 8.17969L4.0697 6.45996C4.00748 6.32945 3.98902 6.18209 4.01599 6.04004C4.04303 5.89815 4.11446 5.76844 4.22009 5.66992L5.62341 4.34375C5.7216 4.2508 5.84496 4.18861 5.97791 4.16406C6.11093 4.13959 6.24851 4.15417 6.37341 4.20605L8.27185 4.99023C8.48118 4.8509 8.67213 4.73551 8.84412 4.64551C9.04868 4.53761 9.3231 4.42233 9.66736 4.2998L10.3275 2.45898C10.3768 2.32458 10.4661 2.20805 10.5834 2.12598C10.7006 2.04397 10.8407 2.00025 10.9838 2H13.0775ZM12.0238 9.01855C10.3569 9.01855 9.00525 10.354 9.00525 12.002C9.00528 13.6499 10.3569 14.9863 12.0238 14.9863C13.6906 14.9862 15.0404 13.6498 15.0404 12.002C15.0404 10.3541 13.6906 9.0187 12.0238 9.01855Z'
            fill='white'
          />
          <path
            d='M18.3704 16.145C18.2722 16.0521 18.139 16 18.0002 16C17.8613 16 17.7282 16.0521 17.63 16.145L15.6535 18.0141C15.5552 18.1071 15.5 18.2333 15.5 18.3648C15.5 18.4963 15.5552 18.6224 15.6535 18.7154C15.7518 18.8084 15.8851 18.8606 16.0241 18.8606C16.163 18.8606 16.2963 18.8084 16.3946 18.7154L17.4763 17.6923V21.5043C17.4763 21.6358 17.5315 21.7618 17.6297 21.8548C17.728 21.9478 17.8612 22 18.0002 22C18.1391 22 18.2724 21.9478 18.3706 21.8548C18.4689 21.7618 18.5241 21.6358 18.5241 21.5043V17.6923L19.6054 18.7154C19.654 18.7615 19.7118 18.798 19.7754 18.8229C19.839 18.8478 19.9071 18.8606 19.9759 18.8606C20.0448 18.8606 20.1129 18.8478 20.1765 18.8229C20.2401 18.798 20.2978 18.7615 20.3465 18.7154C20.3952 18.6694 20.4338 18.6147 20.4601 18.5545C20.4864 18.4944 20.5 18.4299 20.5 18.3648C20.5 18.2997 20.4864 18.2352 20.4601 18.175C20.4338 18.1149 20.3952 18.0602 20.3465 18.0141L18.3704 16.145Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'
        >
          <path
            d='M6.46038 13.2858V21H4.74609V13.2858H6.46038ZM21.889 13.2858V15H20.1747V21H18.4604V15H16.7461V13.2858H21.889ZM12.4604 11.5715C13.6613 11.578 14.8367 11.919 15.8547 12.5563L17.0949 11.3135C15.7343 10.3678 14.1174 9.85974 12.4604 9.85718C10.8255 9.86023 9.23064 10.3627 7.88924 11.2972L9.13124 12.5426C10.1284 11.9142 11.2817 11.5778 12.4604 11.5715Z'
            fill='white'
          />
          <path
            d='M12.458 8.143C14.5685 8.14774 16.6218 8.82977 18.3157 10.0887L19.5388 8.863C17.5142 7.28809 15.023 6.43167 12.458 6.42871C9.91512 6.43174 7.44593 7.28296 5.44141 8.84757L6.66369 10.0733C8.33756 8.82402 10.3693 8.14717 12.458 8.143Z'
            fill='white'
          />
          <path
            d='M12.4629 4.71427C15.4826 4.71467 18.4119 5.7458 20.766 7.63713L21.9891 6.41227C19.3164 4.20887 15.9611 3.00266 12.4972 3C9.0333 2.99735 5.67612 4.1984 3 6.3977L4.22314 7.62256C6.55753 5.74156 9.46494 4.71536 12.4629 4.71427ZM12.4629 13.2857C11.7 13.2857 10.9542 13.5119 10.3199 13.9358C9.68564 14.3596 9.19126 14.962 8.89932 15.6668C8.60738 16.3716 8.531 17.1471 8.67983 17.8953C8.82866 18.6436 9.19601 19.3308 9.73545 19.8703C10.2749 20.4097 10.9622 20.7771 11.7104 20.9259C12.4586 21.0747 13.2341 20.9983 13.9389 20.7064C14.6437 20.4145 15.2461 19.9201 15.67 19.2858C16.0938 18.6515 16.32 17.9057 16.32 17.1429C16.32 16.1199 15.9136 15.1388 15.1903 14.4154C14.4669 13.6921 13.4858 13.2857 12.4629 13.2857ZM12.4629 19.2857C12.039 19.2857 11.6247 19.16 11.2723 18.9246C10.92 18.6891 10.6453 18.3544 10.4831 17.9629C10.3209 17.5713 10.2785 17.1405 10.3612 16.7248C10.4439 16.3091 10.6479 15.9273 10.9476 15.6276C11.2473 15.3279 11.6291 15.1238 12.0448 15.0412C12.4605 14.9585 12.8913 15.0009 13.2829 15.1631C13.6744 15.3253 14.0091 15.6 14.2446 15.9523C14.48 16.3047 14.6057 16.719 14.6057 17.1429C14.6057 17.7112 14.3799 18.2562 13.9781 18.6581C13.5762 19.0599 13.0312 19.2857 12.4629 19.2857Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'
        >
          <path
            d='M19.2301 12.45C19.3422 12.7669 19.5628 13.034 19.8529 13.2041C20.143 13.3742 20.4839 13.4363 20.8154 13.3795C21.1468 13.3226 21.4475 13.1505 21.6643 12.8935C21.8811 12.6365 22 12.3112 22 11.975C22 11.6388 21.8811 11.3135 21.6643 11.0565C21.4475 10.7995 21.1468 10.6274 20.8154 10.5705C20.4839 10.5137 20.143 10.5758 19.8529 10.7459C19.5628 10.916 19.3422 11.1831 19.2301 11.5H16.5687C15.9912 10.8025 15.2407 10.2686 14.3921 9.9515V5.7145C14.7092 5.60245 14.9765 5.38197 15.1466 5.09202C15.3168 4.80208 15.379 4.46134 15.3221 4.13003C15.2652 3.79872 15.093 3.49817 14.8359 3.2815C14.5787 3.06483 14.2532 2.946 13.9169 2.946C13.5806 2.946 13.2551 3.06483 12.9979 3.2815C12.7408 3.49817 12.5686 3.79872 12.5117 4.13003C12.4548 4.46134 12.517 4.80208 12.6871 5.09202C12.8573 5.38197 13.1246 5.60245 13.4417 5.7145V9.6855C13.128 9.62861 12.8099 9.6 12.4912 9.6C11.7448 9.59846 11.0065 9.75382 10.3241 10.056L8.68924 8.422V5.8H9.63973V2H6.78828V5.8H7.73876V8.8115L9.46864 10.5405C8.7431 11.0337 8.15186 11.6998 7.74827 12.4785C7.41395 12.5149 7.08524 12.5914 6.76927 12.7065L4.74474 10.683C4.8926 10.3759 4.92696 10.0265 4.84174 9.69651C4.75652 9.36654 4.55721 9.07737 4.27912 8.88021C4.00102 8.68304 3.66209 8.5906 3.32235 8.61927C2.9826 8.64793 2.66397 8.79584 2.42288 9.03681C2.18179 9.27778 2.0338 9.59625 2.00512 9.93583C1.97644 10.2754 2.06892 10.6142 2.26619 10.8921C2.46346 11.1701 2.75277 11.3693 3.0829 11.4544C3.41304 11.5396 3.76267 11.5053 4.0699 11.3575L5.86631 13.153C5.09684 13.6579 4.5109 14.3974 4.19557 15.2618C3.88024 16.1261 3.85236 17.069 4.11607 17.9504C4.37978 18.8319 4.921 19.6047 5.65929 20.1541C6.39759 20.7036 7.29354 21.0002 8.214 21H17.4812C18.4004 20.9968 19.2827 20.6378 19.9428 19.9984C20.6029 19.359 20.9895 18.4888 21.0215 17.5706C21.0535 16.6524 20.7283 15.7575 20.1143 15.0737C19.5003 14.39 18.6451 13.9706 17.7283 13.9035C17.6315 13.3959 17.4584 12.9059 17.2151 12.45H19.2301Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='17'
          viewBox='0 0 20 17'
          fill='none'
        >
          <path
            d='M15 6C16.87 6 17.804 6.00038 18.5 6.44238C18.9651 6.74228 19.3434 7.15884 19.5977 7.65039C19.9996 8.41637 20 9.44415 20 11.5C20 13.5559 19.9996 14.5836 19.5977 15.3496C19.3347 15.8515 18.9559 16.2676 18.5 16.5576C17.804 16.9996 16.87 17 15 17C14.662 17 14.494 17.0003 14.332 16.9854C13.4981 16.9114 12.7063 16.55 12.0684 15.9541C11.9434 15.8381 11.8238 15.7072 11.5859 15.4443L10.624 14.3857C10.4051 14.1458 10.2949 14.0253 10.1699 13.9863C10.0666 13.9541 9.95532 13.9592 9.85547 14.001C9.73347 14.053 9.63257 14.1843 9.43457 14.4473L9.10059 14.8867C8.70762 15.4057 8.51195 15.6649 8.29297 15.8799C7.72698 16.4453 6.99891 16.8206 6.20996 16.9531C5.91998 17.0001 5.61296 17 5 17C3.13 17 2.196 16.9996 1.5 16.5576C1.04408 16.2676 0.66531 15.8515 0.402344 15.3496C0.000430399 14.5836 0 13.5559 0 11.5C0 9.44415 0.0004304 8.41637 0.402344 7.65039C0.66531 7.14845 1.04408 6.73238 1.5 6.44238C2.196 6.00038 3.13 6 5 6H15ZM13.8574 8C13.384 8 13 8.44771 13 9C13 9.55229 13.384 10 13.8574 10H16.1426C16.616 10 17 9.55229 17 9C17 8.44771 16.616 8 16.1426 8H13.8574ZM13.2061 0C14.8774 -2.08064e-08 15.7123 -5.59984e-05 16.4473 0.34082C16.8167 0.51367 17.1572 0.739643 17.4561 1.01074C18.0828 1.57666 18.5116 2.41916 19.3711 4.10352C19.6697 4.68903 19.8627 5.07184 20 5.40332C19.7785 5.17983 19.5307 4.98015 19.2588 4.81152L19.2549 4.80859L19.0615 4.70117C18.6074 4.46886 18.1095 4.36207 17.5518 4.30859C16.928 4.24882 16.1533 4.25 15.1973 4.25H4.80078C3.84494 4.25 3.07093 4.24883 2.44727 4.30859C1.80953 4.36974 1.2496 4.50082 0.744141 4.80957C0.471645 4.97628 0.222677 5.17577 0 5.40137C0.137244 5.07028 0.330842 4.68805 0.628906 4.10352C1.48837 2.41915 1.91827 1.57666 2.54395 1.01074C2.84272 0.73929 3.18327 0.513 3.55273 0.339844C4.28767 -3.51039e-05 5.1226 -1.5765e-08 6.79297 0H13.2061Z'
            fill='white'
          />
        </svg>,
      ],
    },
    {
      title: 'cyber_security',
      content: 'cyber_security_content',
      keyTitle: 'key_capabilities',
      innovations: [
        'zero_trust_architecture',
        'identity_access_intelligence',
        'automated_compliance_systems',
        'ai_powered_threat_detection',
        'cloud_security_operations',
        'advanced_endpoint_protection',
      ],
      icons: [
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='20'
          viewBox='0 0 20 20'
          fill='none'
        >
          <path
            d='M8.25 8.5C8.25 8.03587 8.43437 7.59075 8.76256 7.26256C9.09075 6.93437 9.53587 6.75 10 6.75C10.4641 6.75 10.9092 6.93437 11.2374 7.26256C11.5656 7.59075 11.75 8.03587 11.75 8.5V11.5C11.75 11.9641 11.5656 12.4092 11.2374 12.7374C10.9092 13.0656 10.4641 13.25 10 13.25C9.53587 13.25 9.09075 13.0656 8.76256 12.7374C8.43437 12.4092 8.25 11.9641 8.25 11.5V8.5Z'
            fill='white'
          />
          <path
            d='M7.367 0.25H12.633C13.725 0.25 14.591 0.25 15.288 0.307C16.002 0.365 16.605 0.487 17.157 0.767C18.0506 1.22284 18.777 1.94992 19.232 2.844C19.513 3.394 19.635 3.998 19.693 4.712C19.75 5.409 19.75 6.275 19.75 7.367V12.633C19.75 13.725 19.75 14.591 19.693 15.288C19.635 16.002 19.513 16.605 19.233 17.157C18.7774 18.0505 18.0507 18.7768 17.157 19.232C16.605 19.513 16.002 19.635 15.288 19.693C14.591 19.75 13.725 19.75 12.633 19.75H7.367C6.275 19.75 5.409 19.75 4.712 19.693C3.998 19.635 3.395 19.513 2.844 19.233C1.95014 18.7776 1.22341 18.0509 0.768 17.157C0.487 16.605 0.365 16.002 0.307 15.288C0.25 14.591 0.25 13.725 0.25 12.633V7.367C0.25 6.275 0.25 5.409 0.307 4.712C0.365 3.998 0.487 3.395 0.767 2.844C1.22267 1.94999 1.94977 1.22324 2.844 0.768C3.394 0.487 3.998 0.365 4.712 0.307C5.409 0.25 6.275 0.25 7.367 0.25ZM10 5.25C9.13805 5.25 8.3114 5.59241 7.7019 6.2019C7.09241 6.8114 6.75 7.63805 6.75 8.5V11.5C6.75 12.362 7.09241 13.1886 7.7019 13.7981C8.3114 14.4076 9.13805 14.75 10 14.75C10.862 14.75 11.6886 14.4076 12.2981 13.7981C12.9076 13.1886 13.25 12.362 13.25 11.5V8.5C13.25 8.0732 13.1659 7.65059 13.0026 7.25628C12.8393 6.86197 12.5999 6.50369 12.2981 6.2019C11.9963 5.90011 11.638 5.66072 11.2437 5.49739C10.8494 5.33406 10.4268 5.25 10 5.25Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='16'
          height='20'
          viewBox='0 0 16 20'
          fill='none'
        >
          <path
            d='M14 6.66667H13V4.76191C13 2.13333 10.76 0 8 0C5.24 0 3 2.13333 3 4.76191V6.66667H2C0.9 6.66667 0 7.52381 0 8.57143V18.0952C0 19.1429 0.9 20 2 20H14C15.1 20 16 19.1429 16 18.0952V8.57143C16 7.52381 15.1 6.66667 14 6.66667ZM8 15.2381C6.9 15.2381 6 14.381 6 13.3333C6 12.2857 6.9 11.4286 8 11.4286C9.1 11.4286 10 12.2857 10 13.3333C10 14.381 9.1 15.2381 8 15.2381ZM5 6.66667V4.76191C5 3.18095 6.34 1.90476 8 1.90476C9.66 1.90476 11 3.18095 11 4.76191V6.66667H5Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='24'
          height='24'
          viewBox='0 0 24 24'
          fill='none'
        >
          <path
            d='M13.0775 2C13.3715 2 13.6348 2.18303 13.7338 2.45703L14.4398 4.41406C14.6925 4.47673 14.9108 4.54111 15.0941 4.60645C15.294 4.67775 15.5539 4.7871 15.8734 4.93555L17.5179 4.06641C17.652 3.99502 17.8054 3.96861 17.9554 3.99219C18.1055 4.01582 18.2443 4.08809 18.35 4.19727L19.7953 5.69238C19.9871 5.89126 20.0417 6.18167 19.934 6.43555L19.1625 8.24316C19.2898 8.47776 19.3931 8.67871 19.4711 8.84668C19.555 9.0286 19.6587 9.28199 19.7826 9.60645L21.5795 10.377C21.8494 10.4919 22.0164 10.7618 21.9984 11.0508L21.8666 13.126C21.8575 13.2607 21.8087 13.3895 21.7269 13.4971C21.6452 13.6046 21.534 13.6857 21.4066 13.7305L19.7045 14.3359C19.6558 14.5704 19.6041 14.7722 19.5502 14.9414C19.5437 14.9609 19.5363 14.9805 19.5297 15H16.0004C15.4481 15 15.0004 15.4477 15.0004 16V19.4375L14.3002 19.7002L13.6498 21.5C13.6011 21.6314 13.5138 21.7457 13.3998 21.8271C13.2858 21.9084 13.15 21.9537 13.0101 21.957L11.1097 22C10.9659 22.0033 10.8241 21.9618 10.7045 21.8818C10.5849 21.8019 10.4927 21.6869 10.4408 21.5527L9.67517 19.5254C9.41389 19.4359 9.15474 19.3391 8.89881 19.2354C8.68966 19.1448 8.48312 19.047 8.28064 18.9424L6.38123 19.7549C6.25609 19.8085 6.11786 19.8244 5.98377 19.8008C5.84979 19.7772 5.72556 19.7151 5.62634 19.6221L4.22009 18.3027C4.11511 18.2053 4.04383 18.0771 4.01599 17.9365C3.98816 17.7959 4.00483 17.6497 4.06482 17.5195L4.8822 15.7402C4.77394 15.5292 4.67344 15.3139 4.58045 15.0957C4.47218 14.8287 4.37222 14.5584 4.28064 14.2852L2.4906 13.7402C2.34511 13.6963 2.21788 13.6049 2.12927 13.4814C2.04072 13.358 1.99541 13.2085 2.00037 13.0566L2.07068 11.1357C2.07591 11.0105 2.11493 10.8888 2.18396 10.7842C2.25301 10.6797 2.34925 10.596 2.46228 10.542L4.34021 9.63965C4.42685 9.32111 4.50306 9.07155 4.5697 8.8916C4.66404 8.6499 4.7693 8.41234 4.88416 8.17969L4.0697 6.45996C4.00753 6.32949 3.98903 6.18203 4.01599 6.04004C4.04302 5.89814 4.11445 5.76845 4.22009 5.66992L5.62439 4.34375C5.72264 4.25083 5.84589 4.18853 5.97888 4.16406C6.11194 4.13961 6.24947 4.15411 6.37439 4.20605L8.27185 4.99023C8.48118 4.8509 8.67212 4.73551 8.84412 4.64551C9.04875 4.53755 9.32383 4.42242 9.66834 4.2998L10.3275 2.45898C10.3768 2.32462 10.4661 2.20804 10.5834 2.12598C10.7006 2.04394 10.8407 2.00028 10.9838 2H13.0775ZM12.0238 9.01855C10.3569 9.01861 9.00623 10.354 9.00623 12.002C9.00625 13.6499 10.3569 14.9863 12.0238 14.9863C13.6907 14.9863 15.0404 13.6499 15.0404 12.002C15.0404 10.354 13.6907 9.01855 12.0238 9.01855Z'
            fill='white'
          />
          <path
            d='M17.6667 20.6667C17.7611 20.6667 17.8403 20.6347 17.9043 20.5707C17.9683 20.5067 18.0002 20.4276 18 20.3333C17.9998 20.2391 17.9678 20.16 17.904 20.096C17.8402 20.032 17.7611 20 17.6667 20C17.5722 20 17.4931 20.032 17.4293 20.096C17.3656 20.16 17.3336 20.2391 17.3333 20.3333C17.3331 20.4276 17.3651 20.5068 17.4293 20.571C17.4936 20.6352 17.5727 20.6671 17.6667 20.6667ZM17.6667 19.3333C17.7611 19.3333 17.8403 19.3013 17.9043 19.2373C17.9683 19.1733 18.0002 19.0942 18 19C17.9998 18.9058 17.9678 18.8267 17.904 18.7627C17.8402 18.6987 17.7611 18.6667 17.6667 18.6667C17.5722 18.6667 17.4931 18.6987 17.4293 18.7627C17.3656 18.8267 17.3336 18.9058 17.3333 19C17.3331 19.0942 17.3651 19.1734 17.4293 19.2377C17.4936 19.3019 17.5727 19.3338 17.6667 19.3333ZM17.6667 18C17.7611 18 17.8403 17.968 17.9043 17.904C17.9683 17.84 18.0002 17.7609 18 17.6667C17.9998 17.5724 17.9678 17.4933 17.904 17.4293C17.8402 17.3653 17.7611 17.3333 17.6667 17.3333C17.5722 17.3333 17.4931 17.3653 17.4293 17.4293C17.3656 17.4933 17.3336 17.5724 17.3333 17.6667C17.3331 17.7609 17.3651 17.8401 17.4293 17.9043C17.4936 17.9686 17.5727 18.0004 17.6667 18ZM19 20.6667H20.3333C20.4278 20.6667 20.507 20.6347 20.571 20.5707C20.635 20.5067 20.6669 20.4276 20.6667 20.3333C20.6664 20.2391 20.6344 20.16 20.5707 20.096C20.5069 20.032 20.4278 20 20.3333 20H19C18.9056 20 18.8264 20.032 18.7627 20.096C18.6989 20.16 18.6669 20.2391 18.6667 20.3333C18.6664 20.4276 18.6984 20.5068 18.7627 20.571C18.8269 20.6352 18.906 20.6671 19 20.6667ZM19 19.3333H20.3333C20.4278 19.3333 20.507 19.3013 20.571 19.2373C20.635 19.1733 20.6669 19.0942 20.6667 19C20.6664 18.9058 20.6344 18.8267 20.5707 18.7627C20.5069 18.6987 20.4278 18.6667 20.3333 18.6667H19C18.9056 18.6667 18.8264 18.6987 18.7627 18.7627C18.6989 18.8267 18.6669 18.9058 18.6667 19C18.6664 19.0942 18.6984 19.1734 18.7627 19.2377C18.8269 19.3019 18.906 19.3338 19 19.3333ZM19 18H20.3333C20.4278 18 20.507 17.968 20.571 17.904C20.635 17.84 20.6669 17.7609 20.6667 17.6667C20.6664 17.5724 20.6344 17.4933 20.5707 17.4293C20.5069 17.3653 20.4278 17.3333 20.3333 17.3333H19C18.9056 17.3333 18.8264 17.3653 18.7627 17.4293C18.6989 17.4933 18.6669 17.5724 18.6667 17.6667C18.6664 17.7609 18.6984 17.8401 18.7627 17.9043C18.8269 17.9686 18.906 18.0004 19 18ZM16.6667 22C16.4833 22 16.3264 21.9348 16.196 21.8043C16.0656 21.6739 16.0002 21.5169 16 21.3333V16.6667C16 16.4833 16.0653 16.3264 16.196 16.196C16.3267 16.0656 16.4836 16.0002 16.6667 16H21.3333C21.5167 16 21.6737 16.0653 21.8043 16.196C21.935 16.3267 22.0002 16.4836 22 16.6667V21.3333C22 21.5167 21.9348 21.6737 21.8043 21.8043C21.6739 21.935 21.5169 22.0002 21.3333 22H16.6667Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='16'
          height='20'
          viewBox='0 0 16 20'
          fill='none'
        >
          <path
            d='M8 -0.000976562C8.13534 -0.000976562 8.26915 0.0267973 8.39355 0.0800781L14.7881 2.82227C15.1477 2.97646 15.4545 3.23292 15.6699 3.55957C15.8852 3.88618 16 4.26898 16 4.66016V11.5176C16.0002 12.7518 15.6741 13.9646 15.0547 15.0322C14.4353 16.0998 13.5443 16.9843 12.4727 17.5967L8.49609 19.8691C8.34504 19.9554 8.17396 20.001 8 20.001C7.82604 20.001 7.65496 19.9554 7.50391 19.8691L3.52734 17.5967C2.456 16.9845 1.5647 16.1004 0.945312 15.0332C0.325946 13.966 0.000120982 12.7534 0 11.5195V4.66016C4.32021e-05 4.26898 0.114823 3.88618 0.330078 3.55957C0.545472 3.23292 0.852314 2.97646 1.21191 2.82227L7.60645 0.0800781C7.73086 0.0267973 7.86466 -0.000976562 8 -0.000976562ZM8.83496 6.80176C8.55003 6.14104 7.57821 6.16192 7.33789 6.86523L7.01465 7.81445L6.98145 7.90137C6.89472 8.11407 6.76371 8.30618 6.59668 8.46387C6.42957 8.62158 6.23013 8.74113 6.0127 8.81543L5.06445 9.13965L5 9.16504C4.33975 9.44977 4.36104 10.4208 5.06445 10.6611L6.0127 10.9854L6.09961 11.0176C6.31251 11.1043 6.50432 11.2361 6.66211 11.4033C6.8199 11.5705 6.94037 11.7697 7.01465 11.9873L7.33789 12.9355L7.36328 13C7.64786 13.6602 8.62004 13.6388 8.86035 12.9355L9.18359 11.9863L9.2168 11.9004C9.30356 11.6875 9.43435 11.4947 9.60156 11.3369C9.76872 11.1792 9.96808 11.0596 10.1855 10.9854L11.1338 10.6611L11.1982 10.6367C11.8585 10.3518 11.8371 9.37998 11.1338 9.13965L10.1855 8.81543L10.0986 8.7832C9.88576 8.69648 9.69392 8.56462 9.53613 8.39746C9.37847 8.23038 9.25789 8.03183 9.18359 7.81445L8.85938 6.86621L8.83496 6.80176ZM11.3145 5.47949C11.2306 5.47944 11.1483 5.50589 11.0801 5.55469C11.0119 5.60348 10.9607 5.67259 10.9336 5.75195L10.793 6.16406L10.3809 6.30469L10.333 6.32422C10.2616 6.35919 10.202 6.41463 10.1621 6.4834C10.1222 6.5522 10.1033 6.63155 10.1084 6.71094C10.1135 6.79027 10.1421 6.86655 10.1904 6.92969C10.2388 6.99287 10.3055 7.03983 10.3809 7.06543L10.793 7.20703L10.9336 7.61914L10.9531 7.66602C10.9881 7.73747 11.0435 7.79695 11.1123 7.83691C11.1811 7.87689 11.2604 7.89566 11.3398 7.89062C11.4192 7.88556 11.4954 7.85691 11.5586 7.80859C11.6216 7.76032 11.6687 7.69429 11.6943 7.61914L11.835 7.20605L12.248 7.06543L12.2949 7.0459C12.3664 7.01091 12.4259 6.95555 12.4658 6.88672C12.5057 6.81792 12.5246 6.73855 12.5195 6.65918C12.5144 6.57991 12.4858 6.5035 12.4375 6.44043C12.3892 6.37747 12.3232 6.33028 12.248 6.30469L11.835 6.16406L11.6943 5.75195L11.6748 5.70508C11.6417 5.63756 11.5902 5.57982 11.5264 5.54004C11.4628 5.50051 11.3893 5.47955 11.3145 5.47949Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='22'
          height='15'
          viewBox='0 0 22 15'
          fill='none'
        >
          <path
            d='M11 1.79728e-07C12.5904 -0.000367705 14.1316 0.564039 15.3604 1.59668C16.5891 2.62935 17.4293 4.06638 17.7373 5.66211C20.1206 5.83086 22 7.8375 22 10.3125C22 12.8999 19.9468 14.9998 17.417 15H5.5C2.46583 15 3.70981e-08 12.4781 3.70981e-08 9.375C-0.000157326 7.98929 0.500316 6.65238 1.40527 5.62109C2.3105 4.58974 3.55701 3.93601 4.9043 3.78711C6.05019 1.53731 8.35098 2.86332e-07 11 1.79728e-07ZM11 4C9.96515 4 9.12525 4.85308 9.125 5.9043V6.66699H8.75C8.3375 6.66699 8 7.00966 8 7.42871V11.2383C8.0001 11.6572 8.33756 12 8.75 12H13.25C13.6624 12 13.9999 11.6572 14 11.2383V7.42871C14 7.00966 13.6625 6.66699 13.25 6.66699H12.875V5.9043C12.8748 4.85308 12.0348 4 11 4ZM11 8.57129C11.4124 8.57129 11.7498 8.91411 11.75 9.33301C11.75 9.75206 11.4125 10.0957 11 10.0957C10.5875 10.0957 10.25 9.75206 10.25 9.33301C10.2502 8.91411 10.5876 8.57129 11 8.57129ZM11 4.76172C11.6223 4.76172 12.1248 5.27213 12.125 5.9043V6.66699H9.875V5.9043C9.87525 5.27213 10.3777 4.76172 11 4.76172Z'
            fill='white'
          />
        </svg>,
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='18'
          height='16'
          viewBox='0 0 18 16'
          fill='none'
        >
          <path
            d='M18 7H10.29C9.9041 6.11743 9.02488 5.5 8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.02488 10.5 9.9041 9.88257 10.29 9H18V16H8C3.5817 16 0 12.4183 0 8C0 3.5817 3.5817 0 8 0H18V7ZM8 7.5C8.27614 7.5 8.5 7.72386 8.5 8C8.5 8.27614 8.27614 8.5 8 8.5C7.72386 8.5 7.5 8.27614 7.5 8C7.5 7.72386 7.72386 7.5 8 7.5Z'
            fill='white'
          />
        </svg>,
      ],
    },
  ];
  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);
  const [animatedNumber, setAnimatedNumber] = useState(0);
  const [canSlide, setCanSlide] = useState(true);

  const sectionRef = useRef<HTMLDivElement>(null);

  const isInView = useInView(sectionRef);
  const { width: windowWidth } = useWindowDimensions();
  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying && isInView) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, isInView]);
  useEffect(() => {
    setCanSlide(false);
    // Reset animation when slide changes
    progressRef.current = 0;
    setAnimatedNumber(0);
    setTimeout(() => setCanSlide(true), 2000);
  }, [currentIndex]);
  useEffect(() => {
    if (Math.floor(animatedNumber) === 9) {
      handleNext();
    }
  }, [Math.floor(animatedNumber)]);

  const handlePrev = useCallback(async () => {
    if (!canSlide) return;
    await setDirection('left');
    setCurrentIndex(currentIndex === 0 ? solutionsSections.length - 1 : currentIndex - 1);
  }, [currentIndex, solutionsSections, canSlide]);

  const handleNext = useCallback(async () => {
    if (!canSlide) return;
    await setDirection('right');
    setCurrentIndex(currentIndex === solutionsSections.length - 1 ? 0 : currentIndex + 1);
  }, [currentIndex, solutionsSections, canSlide]);
  return (
    <Section container center className='relative py-40'>
      <SlashDiv
        additionalHeight={600}
        className='relative flex aspect-square w-full max-w-[1000px] items-center justify-center rounded-full'
      >
        <img
          src={getCdnUrl('/solutions/circle.gif')}
          className='absolute size-full min-w-[500px] flex-none object-cover opacity-40'
          ref={divRef}
          alt='Circle'
        />
        <div className='absolute size-full rounded-full bg-blue-800 opacity-20 blur-3xl' />
        <div className='absolute flex w-full justify-between max-md:hidden'>
          <CarouselButton direction='left' onClick={handlePrev} />
          <CarouselButton direction='right' onClick={handleNext} />
        </div>
        {width > 0 && (
          <CoreSolutionRainbow
            icons={solutionsSections[currentIndex].icons}
            items={solutionsSections[currentIndex].innovations}
            width={
              width /
              match(windowWidth)
                .with(
                  P.when((w) => w && w > 1300),
                  () => 2.7
                )
                .with(
                  P.when((w) => w && w > 1024),
                  () => 3
                )
                .with(
                  P.when((w) => w && w > 688),
                  () => 3.3
                )
                .otherwise(() => 3)
            }
            activeIndex={currentIndex}
            direction={direction}
          />
        )}
        <motion.div
          ref={sectionRef}
          className='z-10 flex flex-col items-center gap-4 text-center max-md:mt-40'
        >
          <motion.div layout>
            <Tag>{t('our-core-solutions')}</Tag>
          </motion.div>

          <AnimatePresence mode='popLayout'>
            <motion.div
              layoutId={`core-solution-title-${currentIndex}`}
              key={`core-solution-title-${currentIndex}`}
              className={cn(
                'mt-1 text-2xl leading-tight font-bold text-nowrap md:text-3xl lg:text-4xl'
              )}
              initial={{ opacity: 0, x: -100 }}
              animate={{ opacity: 1, x: 0, transition: { delay: 0.5, duration: 0.5 } }}
              exit={{ opacity: 0, x: 100 }}
              transition={{
                duration: 0.5,
              }}
            >
              {t(solutionsSections[currentIndex].title)}
            </motion.div>
          </AnimatePresence>
          <AnimatePresence mode='popLayout'>
            <motion.div
              initial={{ opacity: 0, x: -100 }}
              animate={{ opacity: 0.7, x: 0, transition: { delay: 0.5, duration: 0.5 } }}
              exit={{ opacity: 0, x: 100 }}
              transition={{ duration: 0.5 }}
              layoutId={`core-solution-content-${currentIndex}`}
              key={`core-solution-content-${currentIndex}`}
              className={cn('max-w-[520px] text-sm will-change-transform lg:text-base')}
            >
              {t(solutionsSections[currentIndex].content)}
            </motion.div>
          </AnimatePresence>
          <motion.div layout className='flex w-full justify-end gap-6 md:hidden'>
            <CarouselButton direction='left' onClick={handlePrev} />
            <CarouselButton direction='right' onClick={handleNext} />
          </motion.div>
        </motion.div>
      </SlashDiv>
    </Section>
  );
}

const CoreSolutionRainbow = ({
  items,
  width,
  activeIndex,
  direction,
  icons,
}: {
  items: string[];
  width: number;
  activeIndex: number;
  direction: 'left' | 'right';
  icons: React.ReactNode[];
}) => {
  const t = useTranslations('solutions');
  return [...Array(6)].map((_, index) => (
    <AnimatePresence key={index} mode='popLayout'>
      {index <= items.length - 1 && (
        <motion.div
          key={`item-${index}-${activeIndex}`}
          initial={{
            transform:
              direction === 'right'
                ? `rotate(0deg) translateY(${width}px) rotate(0deg)`
                : `rotate(360deg) translateY(${width}px) rotate(-360deg)`,
            opacity: 0,
          }}
          animate={{
            transform: `rotate(${90 + (180 / items.length) * 0.5 + (180 / items.length) * index}deg) translateY(${width}px) rotate(-${90 + (180 / items.length) * 0.5 + (180 / items.length) * index}deg)`,
            opacity: 1,
            transition: {
              duration: 1.5,
              delay: 0.4,
              ease: 'easeOut',
              opacity: { duration: 0.4, delay: 0.4 + (items.length - index) * 0.1 },
            },
            direction: direction === 'left' ? 'reverse' : 'normal',
          }}
          exit={{
            transform:
              direction === 'right'
                ? `rotate(440deg) translateY(${width}px) rotate(-440deg)`
                : `rotate(-80deg) translateY(${width}px) rotate(80deg)`,
            transition: {
              duration: 1.5,
              ease: 'easeIn',
              opacity: {
                duration: 0.4,
                delay: 0.4 + (direction === 'right' ? items.length - index : index) * 0.1,
              },
            },
            opacity: 0,
          }}
          className={cn(
            'absolute flex transform-gpu items-center justify-center rounded-full will-change-transform [animation-delay:calc(var(--delay)*1000ms)]'
          )}
        >
          <motion.div className='relative flex items-center justify-center'>
            <div className='flex size-10 items-center justify-center rounded-lg border border-gray-50 bg-black/16 backdrop-blur-xl'>
              {icons[index] || (
                <div className='scale-25'>
                  <LogoIcon />
                </div>
              )}
            </div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{
                opacity: 1,
                transition: {
                  delay:
                    direction === 'right'
                      ? 0.7 + (items.length - index) * 0.1
                      : 0.7 + index * 0.1,
                  duration: 0.5,
                },
              }}
              exit={{
                opacity: 0,
                transition: {
                  delay:
                    direction === 'right' ? (items.length - index) * 0.1 : index * 0.1,
                },
              }}
              className='absolute bottom-full will-change-transform max-md:hidden'
            >
              <div className='relative flex flex-col-reverse items-center'>
                <div className='h-5 w-[1px] bg-white'></div>
                {match(index)
                  .with(
                    P.when((n) => n + 0.5 < items.length / 2),
                    () => (
                      <>
                        <div className='h-5 w-[1px] translate-x-[-7px] translate-y-0.75 -rotate-45 bg-white'></div>
                        <div className='absolute top-0.5 right-3 size-2 rounded-full bg-white'></div>
                        <div className='absolute -top-3 right-7 w-[140px] text-right text-sm font-medium lg:w-[160px] lg:text-base xl:w-[550px]'>
                          {t(items?.[index])}
                        </div>
                      </>
                    )
                  )
                  .with(
                    P.when((n) => n + 0.5 > items.length / 2),
                    () => (
                      <>
                        <div className='h-5 w-[1px] translate-x-[7px] translate-y-0.75 rotate-45 bg-white'></div>
                        <div className='absolute top-0.5 left-3 size-2 rounded-full bg-white'></div>
                        <div className='absolute -top-3 left-7 w-[140px] text-sm font-medium lg:w-[160px] lg:text-base xl:w-[550px]'>
                          {t(items?.[index])}
                        </div>
                      </>
                    )
                  )
                  .otherwise(() => (
                    <>
                      <div className='h-5 w-[1px] bg-white'></div>
                      <div className='size-2 rounded-full bg-white'></div>
                      <div className='mb-1 w-[140px] self-center text-center text-sm font-medium lg:w-[160px] lg:text-base xl:w-[550px]'>
                        {t(items?.[index])}
                      </div>
                    </>
                  ))}
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  ));
};
