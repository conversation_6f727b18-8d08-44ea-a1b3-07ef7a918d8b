import { getCarouselGroups } from '@/services/directus';
import { getAllDepartments, getAllOpeningJobs } from '@/services/erp';
import HeroSection from '../components/HeroSection';
import CareerCategory from './CareerCategory';
import JobNotificationSection from './JobNotificationSection';
import { OurCultureValueSection, WhyJoinWithUsSection } from './OurCultureValue';
import WhatOurTeamSay from './WhatOurTeamSay';
import WhoWeAreSection from './WhoWeAreSection';
import { JobDepartmentAvailable } from './types';

export default async function CareerMainView() {
  const [jobRes, departmentsRes, carouselGroups] = await Promise.all([
    getAllOpeningJobs(),
    getAllDepartments(),
    getCarouselGroups('career'),
  ]);

  const jobData = jobRes?.data || [];
  const departmentNames = departmentsRes?.data || [];

  const departmentAvailableJobs = departmentNames.map<JobDepartmentAvailable>(
    (department) => {
      const jobs = jobData.filter((job) => job.department === department.name);

      return {
        ...department,
        availableJobs: jobs.length,
      };
    }
  );

  return (
    <section className='bg-background relative z-10 flex h-full w-full flex-col gap-10 md:gap-20'>
      <HeroSection sectionKey='main' />
      <WhoWeAreSection />
      <WhyJoinWithUsSection />
      <WhatOurTeamSay carouselGroups={carouselGroups?.data?.[0]?.items || []} />
      <OurCultureValueSection />
      <JobNotificationSection />
      <CareerCategory data={departmentAvailableJobs} />
    </section>
  );
}
