import { Asset, Translation } from '@/types/resources';

export type JobDepartmentAvailable = {
  name: string;
  department_name: string;
  availableJobs: number;
};

export type OurTeamSayDataItem = {
  id: number;
  sort: number;
  item: OurTeamSayDataItemDetail;
};

export type OurTeamSayDataItemDetail = {
  id: string;
  click_url: string | null;
  asset: Asset;
  translations: Translation[];
};

export type OurTeamSaySlide = {
  id: number;
  name: string;
  position: string;
  image: string;
  description: string;
};
