'use client';

import BlurShine from '@/components/common/BlurShine';
import { Button } from '@/components/common/Button';
import { Card } from '@/components/common/Card';
import { Input } from '@/components/common/Input';
import { Section } from '@/components/common/Section';
import { ScrollMotionDiv } from '@/components/motion/ScrollMotionDiv';
import { createNewsletterSubscription } from '@/services/directus';
import { getCdnUrl } from '@/utils/url/asset';
import { IconArrowRight } from '@tabler/icons-react';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

const EMAIL_PATTERN = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export default function JobNotificationSection() {
  const t = useTranslations('career.create-job-notification');

  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitEmail = async () => {
    try {
      if (email === '') return toast.error('Email is required.');
      if (!EMAIL_PATTERN.test(email)) return toast.error('Invalid email address.');

      setIsSubmitting(true);
      const response = await createNewsletterSubscription({
        email,
        agreeToPrivacyPolicy: true,
      });
      if (!response.success) {
        return toast.error('Failed to subscribe. Please try again later.');
      }
      setEmail('');
      toast.success('You have successfully subscribed to job notifications!');
    } catch (err) {
      console.error(err);
      toast.error('Failed to subscribe to job notifications. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <BlurShine size={1600} blur={400} direction='left' color='var(--color-blue-800)' />
      <Card className='relative z-50 flex w-full flex-col items-center gap-12 rounded-none border-0 py-16'>
        <ScrollMotionDiv
          transformFrom={[0, 1]}
          transformTo={[0, -300]}
          scrollX
          className='absolute top-0 left-0 h-full w-[calc(100%+300px)]'
        >
          <motion.img
            src={getCdnUrl('/solutions/connect.webp')}
            className='h-full w-full object-cover'
            alt=''
          />
        </ScrollMotionDiv>
        <div
          className='absolute inset-0 z-10 h-full w-full'
          style={{
            background:
              'linear-gradient(90deg, rgba(50, 127, 239, 0.7) 0%, rgba(16, 50, 99, 0.7) 100%)',
          }}
        ></div>
        <Section className='relative z-20 flex flex-col justify-between gap-4 xl:container xl:flex-row xl:items-center'>
          <div className='flex flex-col gap-2'>
            <h3 className='text-[32px] leading-12 font-bold'>{t('title')}</h3>
            <p className='max-w-[495px] text-sm leading-[21px]'>{t('subtitle')}</p>
          </div>
          <div className='flex flex-col items-center gap-2 md:flex-row'>
            <Input
              value={email}
              placeholder={t('placeholder')}
              onChange={(e) => setEmail(e.target.value)}
              className='placeholder:text-neutrals-60 border-neutral-60 focus:border-primary h-full w-full border py-4 !text-base leading-[160%] font-normal !text-neutral-100 transition-colors placeholder:text-base focus:outline-none focus-visible:!ring-0 focus-visible:!ring-offset-0 md:w-[450px]'
            />
            <Button
              size='lg'
              disabled={isSubmitting}
              onClick={handleSubmitEmail}
              className='h-auto w-full !px-8 !py-4 leading-6 disabled:opacity-100 disabled:grayscale-25 md:w-fit'
              aria-label='Subscribe to job notifications'
            >
              {isSubmitting ? (
                <>
                  {t('submitting')}
                  <div className='size-5 animate-spin rounded-full border-[3px] border-transparent border-t-white' />
                </>
              ) : (
                <>
                  {t('button')}
                  <IconArrowRight className='size-6' />
                </>
              )}
            </Button>
          </div>
        </Section>
      </Card>
    </>
  );
}
