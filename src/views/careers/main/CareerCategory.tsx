'use client';
import { Button } from '@/components/common/Button';
import { Card, CardContent, CardHeader } from '@/components/common/Card';
import HeroTitle from '@/components/common/HeroTitle';
import NoDataIcon from '@/components/icons/NoDataIcon';
import { IconArrowRight } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import CareerCategoryItem from '../components/CareerCategoryItem';
import { JobDepartmentAvailable } from './types';

interface CareerCategoryProps {
  data: JobDepartmentAvailable[];
}

export default function CareerCategory({ data }: CareerCategoryProps) {
  const t = useTranslations('career.category');

  const sorted = data
    .filter((item) => item.availableJobs > 0)
    .sort((a, b) => b.availableJobs - a.availableJobs);

  return (
    <section className='relative z-50 mb-40 px-4 xl:container xl:mx-auto xl:px-0'>
      <HeroTitle
        title={t('title')}
        description={t('description')}
        descriptionClassName='w-full max-w-full px-4 lg:max-w-[1165px]'
        tag={t('tag')}
      />
      <div className='mt-4 flex w-full items-center justify-center'>
        <Link href='/careers/open-positions'>
          <Button className='transition-all hover:opacity-85' aria-label='Show all jobs'>
            {t('button')}
            <IconArrowRight className='size-6' />
          </Button>
        </Link>
      </div>

      <Card className='mt-10 gap-6 rounded-sm p-8 lg:mt-20 xl:px-32 xl:py-24'>
        <CardHeader className='flex w-full items-center justify-between gap-3 !px-0'>
          <h3 className='text-left text-[clamp(2rem,1.7573rem+1.0356vw,3rem)] leading-[110%] font-semibold'>
            {t.rich('explore-category', {
              span: (chunks) => <span className='text-primary uppercase'>{chunks}</span>,
            })}
          </h3>
          <Link href='/careers/open-positions'>
            <Button
              variant='link'
              aria-label='Show all jobs'
              className='text-primary text-base leading-[160%] font-semibold hover:no-underline hover:opacity-80'
            >
              <span className='hidden md:inline'>{t('show-all-jobs')}</span>
              <IconArrowRight />
            </Button>
          </Link>
        </CardHeader>
        <CardContent className='px-0'>
          {sorted.length > 0 ? (
            <div className='grid w-full grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-4'>
              {sorted.map((item) => (
                <CareerCategoryItem key={item.name} item={item} />
              ))}
            </div>
          ) : (
            <div className='flex h-full flex-col items-center justify-center gap-5 text-gray-600 xl:col-span-3'>
              <NoDataIcon className='w-48' />
              <p className='text-center text-xl'>{t('job-list.no-jobs')}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </section>
  );
}
