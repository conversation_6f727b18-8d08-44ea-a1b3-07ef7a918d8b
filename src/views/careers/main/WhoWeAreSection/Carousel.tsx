'use client';
import BlurShine from '@/components/common/BlurShine';
import { CarouselButton } from '@/components/common/CarouselButton';
import { useEmblaCarouselButtons } from '@/hooks/useEmblaCarouselButtons';
import { cn } from '@/utils/cn';
import { EmblaCarouselType, EmblaEventType, EmblaOptionsType } from 'embla-carousel';
import useEmblaCarousel from 'embla-carousel-react';
import { motion, useInView } from 'framer-motion';
import { useCallback, useEffect, useRef, useState } from 'react';

const TWEEN_FACTOR_BASE = 0.5;

const numberWithinRange = (number: number, min: number, max: number): number =>
  Math.min(Math.max(number, min), max);

type PropType = {
  slides: { title: string; description: string }[];
};

function EmblaCarousel({ slides }: PropType) {
  const [animatedNumber, setAnimatedNumber] = useState(0);
  const dotRef = useRef<HTMLDivElement>(null);

  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);
  const isInView = useInView(dotRef);

  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: 'center',
    startIndex: 1,
  } as EmblaOptionsType);

  const tweenFactor = useRef(0);
  const tweenNodes = useRef<HTMLElement[]>([]);

  const { selectedIndex, onDotButtonClick, onPrevButtonClick, onNextButtonClick } =
    useEmblaCarouselButtons(emblaApi);

  const setTweenNodes = useCallback((emblaApi: EmblaCarouselType): void => {
    tweenNodes.current = emblaApi.slideNodes().map((slideNode) => {
      return slideNode.querySelector('.slide') as HTMLElement;
    });
  }, []);

  const setTweenFactor = useCallback((emblaApi: EmblaCarouselType) => {
    tweenFactor.current = TWEEN_FACTOR_BASE * emblaApi.scrollSnapList().length;
  }, []);

  const tweenScale = useCallback(
    (emblaApi: EmblaCarouselType, eventName?: EmblaEventType) => {
      const engine = emblaApi.internalEngine();
      const scrollProgress = emblaApi.scrollProgress();
      const slidesInView = emblaApi.slidesInView();
      const isScrollEvent = eventName === 'scroll';

      emblaApi.scrollSnapList().forEach((scrollSnap, snapIndex) => {
        let diffToTarget = scrollSnap - scrollProgress;
        const slidesInSnap = engine.slideRegistry[snapIndex];

        slidesInSnap.forEach((slideIndex) => {
          if (isScrollEvent && !slidesInView.includes(slideIndex)) return;

          if (engine.options.loop) {
            engine.slideLooper.loopPoints.forEach((loopItem) => {
              const target = loopItem.target();

              if (slideIndex === loopItem.index && target !== 0) {
                const sign = Math.sign(target);

                if (sign === -1) {
                  diffToTarget = scrollSnap - (1 + scrollProgress);
                }
                if (sign === 1) {
                  diffToTarget = scrollSnap + (1 - scrollProgress);
                }
              }
            });
          }

          const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor.current);
          const scale = numberWithinRange(tweenValue, 0, 1).toString();
          const tweenNode = tweenNodes.current[slideIndex];
          tweenNode.style.transform = `scale(${scale})`;
        });
      });
    },
    []
  );

  useEffect(() => {
    if (!emblaApi) return;

    setTweenNodes(emblaApi);
    setTweenFactor(emblaApi);
    tweenScale(emblaApi);

    emblaApi
      .on('reInit', setTweenNodes)
      .on('reInit', setTweenFactor)
      .on('reInit', tweenScale)
      .on('scroll', tweenScale)
      .on('slideFocus', tweenScale);
  }, [emblaApi, tweenScale]);

  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying && isInView) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, isInView]);

  useEffect(() => {
    if (Math.floor(animatedNumber) === 9) {
      onNextButtonClick();
    }
  }, [Math.floor(animatedNumber)]);

  useEffect(() => {
    progressRef.current = 0;
    setAnimatedNumber(0);
  }, [selectedIndex]);

  return (
    <section className='relative z-50 my-20 h-full w-full lg:my-40'>
      <BlurShine size={1600} blur={300} direction='right' color='var(--color-blue-800)' />
      <div className='relative' ref={emblaRef}>
        <div className='flex w-full items-center'>
          {slides.map((slide, index) => (
            <div
              key={index}
              className={cn(
                'max-w-[1140px] min-w-[0] shrink-0 basis-full [transform:translate3d(0,_0,_0)] xl:basis-4/6'
              )}
              onMouseEnter={() => setIsPlaying(false)}
              onMouseLeave={() => setIsPlaying(true)}
              onFocus={() => setIsPlaying(false)}
            >
              <div className='slide h-full'>
                <div
                  className={cn('h-full rounded-xl bg-transparent', {
                    'backdrop-blur-sm': index === selectedIndex,
                  })}
                >
                  <div
                    className={cn(
                      'h-full rounded-xl opacity-20 transition-[opacity,background-color,border-color]',
                      {
                        'border border-white/20 bg-white/2 py-20 opacity-100 lg:py-32':
                          index === selectedIndex,
                      }
                    )}
                  >
                    <div className={cn('flex flex-col items-center gap-4 select-none')}>
                      <h2
                        className={cn(
                          'w-full text-center text-6xl leading-none font-medium lg:text-[120px]',
                          {
                            'font-bold': index === selectedIndex,
                          }
                        )}
                      >
                        {slide.title}
                      </h2>
                      <p
                        className={cn(
                          'w-full text-center text-2xl leading-none lg:text-5xl'
                        )}
                      >
                        {slide.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className='absolute top-1/2 -left-5 z-30 hidden h-full -translate-y-1/2 items-center md:left-4 md:flex xl:left-1/5'>
          <CarouselButton direction='left' onClick={onPrevButtonClick} />
        </div>
        <div className='absolute top-1/2 -right-5 z-30 hidden h-full -translate-y-1/2 items-center md:right-4 md:flex xl:right-1/5'>
          <CarouselButton direction='right' onClick={onNextButtonClick} />
        </div>
      </div>
      <div className='z-10 mt-4 flex items-center justify-center gap-2' ref={dotRef}>
        {slides.map((_, index: number) => (
          <motion.div
            key={`dot-${index}`}
            onClick={() => onDotButtonClick(index)}
            className={cn('h-0.5 w-12 cursor-pointer rounded-2xl bg-white')}
          >
            <motion.div
              animate={{
                width: index === selectedIndex ? `${animatedNumber * 12}%` : 0,
              }}
              className='bg-primary h-full'
            />
          </motion.div>
        ))}
      </div>
    </section>
  );
}

export default EmblaCarousel;
