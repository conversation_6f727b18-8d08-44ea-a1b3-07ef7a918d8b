'use client';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { SlashDiv } from '@/components/common/SlashBackground';
import { useTranslations } from 'next-intl';
import EmblaCarousel from './Carousel';

function WhoWeAreSection() {
  const t = useTranslations('career.who-we-are');

  const slides = [
    {
      title: t('slides.first.title'),
      description: t('slides.first.description'),
    },
    {
      title: t('slides.second.title'),
      description: t('slides.second.description'),
    },
    {
      title: t('slides.third.title'),
      description: t('slides.third.description'),
    },
  ];

  return (
    <SlashDiv h={2000}>
      <Section container className='flex flex-col items-center text-center'>
        <HeroTitle
          tag={t('tag')}
          title={t('title')}
          description={t('description')}
          titleClassName='md:whitespace-pre-line'
          className='w-full items-center text-center'
          descriptionClassName='w-full max-w-full md:max-w-[655px] text-base leading-7'
        />
        <EmblaCarousel slides={slides} />
      </Section>
    </SlashDiv>
  );
}

export default WhoWeAreSection;
