'use client';
import CircleRounding from '@/components/common/CircleRounding';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { SlashDiv } from '@/components/common/SlashBackground';
import AiFillIcon from '@/components/icons/AiFillIcon';
import MergeBranchIcon from '@/components/icons/MergeBranchIcon';
import RankingDuetoneIcon from '@/components/icons/RankingDuetoneIcon';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { cn } from '@/utils/cn';
import { IconUserFilled } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import BlueShineLight from './BlueShineLight';
import ValueCardItem from './ValueCardItem';

export default function InfiniteCircleSection() {
  const t = useTranslations('career.our-culture-value');
  const { width } = useWindowDimensions();

  const isTablet = width ? width < 1023 : false;

  const values = [
    {
      title: t('values.first.title'),
      description: t('values.first.description'),
      icon: <MergeBranchIcon className='size-6' />,
    },
    {
      title: t('values.second.title'),
      description: t('values.second.description'),
      icon: <IconUserFilled className='size-6' />,
    },
    {
      title: t('values.third.title'),
      description: t('values.third.description'),
      icon: <AiFillIcon className='size-6' />,
    },
    {
      title: t('values.four.title'),
      description: t('values.four.description'),
      icon: <RankingDuetoneIcon className='size-6' />,
    },
  ];

  const cardValuePosition: Record<number, string> = {
    [0]: '-top-35 xl:top-0 left-5 xl:left-1/9',
    [1]: '-top-35 xl:top-0 right-5 xl:right-1/9',
    [2]: '-bottom-35 xl:bottom-0 right-5 xl:right-1/9',
    [3]: '-bottom-35  xl:bottom-0 left-5 xl:left-1/9',
  };

  return (
    <SlashDiv
      h={1000}
      additionalHeight={500}
      className={cn('relative flex flex-col gap-5 md:mt-40 xl:container xl:mx-auto')}
    >
      <Section center className='relative'>
        <CircleRounding
          size={isTablet ? '100%' : '55%'}
          color={['#38DCF5', '#63BDFF59']}
          clockRotate={false}
          length={3}
          speed={10}
        />
        <div className='absolute inset-0 flex h-full w-full flex-col items-center justify-center'>
          <Section center className={cn('relative')}>
            <CircleRounding
              size={isTablet ? '60%' : '30%'}
              color={['#FF8300', '#FFF5EB']}
              clockRotate
              length={3}
              speed={30}
            />

            <div className='absolute inset-0 z-50 flex w-full flex-col items-center justify-center gap-15'>
              <HeroTitle
                className='w-full items-center text-center'
                title={t('title')}
                titleClassName='md:whitespace-pre-line'
                description={t('description')}
                descriptionClassName='w-full max-w-full md:max-w-[655px] text-base leading-7'
                tag={t('tag')}
              />
            </div>
          </Section>
        </div>
        <div className='absolute inset-0 z-10 flex h-full w-full items-center justify-center'>
          <BlueShineLight className='h-[500%] w-[500%]' />
        </div>
      </Section>
      {!isTablet &&
        values.map((value, index) => {
          return (
            <ValueCardItem
              value={value}
              key={value.title}
              variant='transparent'
              className={cn('absolute z-50 mb-0 w-[365px]', cardValuePosition[index])}
            />
          );
        })}
    </SlashDiv>
  );
}
