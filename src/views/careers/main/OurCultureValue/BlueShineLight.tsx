export default function BlueShineLight(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={1568}
      height={1600}
      viewBox='0 0 1568 1600'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g opacity={0.8} filter='url(#filter0_f_285_3534)'>
        <rect
          x={400}
          y={400}
          width={768}
          height={800}
          fill='url(#paint0_radial_285_3534)'
          fillOpacity={0.8}
        />
      </g>
      <defs>
        <filter
          id='filter0_f_285_3534'
          x={0}
          y={0}
          width={1568}
          height={1600}
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur stdDeviation={200} result='effect1_foregroundBlur_285_3534' />
        </filter>
        <radialGradient
          id='paint0_radial_285_3534'
          cx={0}
          cy={0}
          r={1}
          gradientUnits='userSpaceOnUse'
          gradientTransform='translate(784 800) rotate(90) scale(400 384)'
        >
          <stop stopColor='#EAF3FF' />
          <stop offset={1} stopColor='#2271E1' />
        </radialGradient>
      </defs>
    </svg>
  );
}
