'use client';

import { Glasscard } from '@/components/common/Card';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { cn } from '@/utils/cn';
import { HTMLAttributes } from 'react';

export type ValueItemType = {
  title: string;
  description: string;
  icon: React.ReactNode;
};

interface ValueItemProps {
  value: ValueItemType;
  className?: HTMLAttributes<HTMLDivElement>['className'];
  variant?: 'default' | 'transparent';
}

export default function ValueCardItem({
  value,
  className,
  variant = 'default',
}: ValueItemProps) {
  const variantStyles = {
    default: {
      card: 'bg-white/5',
      icon: 'bg-primary',
      borderColor: '#FFFFFF63',
    },
    transparent: {
      card: 'border border-white/10',
      icon: 'bg-white/5 border border-white/10',
      borderColor: undefined,
    },
  };

  return (
    <div className={cn('shrink-0 rounded-2xl', className)}>
      <Glasscard
        borderColor={variantStyles[variant].borderColor}
        className={cn('h-full p-4', variantStyles[variant].card)}
      >
        <div className='flex flex-col gap-5'>
          <div
            className={cn(
              'flex size-11 items-center justify-center rounded-xl',
              variantStyles[variant].icon
            )}
          >
            {value.icon}
          </div>
          <div className='flex w-full flex-col'>
            <h3 className='mb-1 text-4xl font-medium whitespace-pre-line'>
              {value.title}
            </h3>
            <p className='text-sm text-white/60'>{value.description}</p>
          </div>
        </div>
      </Glasscard>
    </div>
  );
}
