'use client';
import BlurShine from '@/components/common/BlurShine';
import { Glasscard } from '@/components/common/Card';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import AiFillIcon from '@/components/icons/AiFillIcon';
import MergeBranchIcon from '@/components/icons/MergeBranchIcon';
import RankingDuetoneIcon from '@/components/icons/RankingDuetoneIcon';
import { getCdnUrl } from '@/utils/url/asset';
import { IconUserFilled } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import ValueCardItem from './ValueCardItem';

export default function WhyJoinWithUs() {
  const t = useTranslations('career.why-join-with-us');

  const values = [
    {
      title: t('values.first.title'),
      description: t('values.first.description'),
      icon: <MergeBranchIcon className='size-6' />,
    },
    {
      title: t('values.second.title'),
      description: t('values.second.description'),
      icon: <IconUserFilled className='size-6' />,
    },
    {
      title: t('values.third.title'),
      description: t('values.third.description'),
      icon: <AiFillIcon className='size-6' />,
    },
    {
      title: t('values.four.title'),
      description: t('values.four.description'),
      icon: <RankingDuetoneIcon className='size-6' />,
    },
  ];

  return (
    <Section container className='relative'>
      <div className='absolute top-1/2 right-0 -translate-y-1/2'>
        <BlurShine
          size={800}
          blur={800}
          direction='right'
          color='var(--color-blue-800)'
        />
      </div>
      <HeroTitle
        className='w-full items-center text-center'
        title={t('title')}
        titleClassName='md:whitespace-pre-line'
        description={t('description')}
        descriptionClassName='w-full max-w-full md:max-w-[655px] text-base leading-7'
        tag={t('tag')}
      />

      <div className='relative z-10 mt-6 flex w-full justify-center'>
        <Glasscard
          isShadow={false}
          borderWidth={2}
          borderColor='#FFFFFF63'
          autoRotate={{ duration: 3 }}
          className='relative z-50 w-full max-w-[1140px] rounded-2xl p-2 backdrop-blur-sm lg:p-10'
        >
          <div className='relative aspect-video rounded-2xl'>
            <video className='absolute inset-0 h-full w-full rounded-2xl' controls>
              <source
                src={getCdnUrl('/careers/vns_brandbcampaign.mp4')}
                type='video/mp4'
              />
            </video>
          </div>
        </Glasscard>
        <div className='absolute top-2/3 right-1/3 -z-50 w-full translate-x-1/5 -translate-y-1/2 lg:right-0'>
          <svg
            viewBox='0 0 1232 969'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
            className='size-[180%] lg:size-full'
          >
            <g filter='url(#filter0_f_262_5066)'>
              <rect
                width={884}
                height={621}
                transform='matrix(-1 0 0 1 1058 174)'
                fill='url(#paint0_linear_262_5066)'
              />
              <rect
                width={884}
                height={621}
                transform='matrix(-1 0 0 1 1058 174)'
                fill='black'
                fillOpacity={0.3}
              />
            </g>
            <defs>
              <filter
                id='filter0_f_262_5066'
                x={0}
                y={0}
                width={1232}
                height={969}
                filterUnits='userSpaceOnUse'
                colorInterpolationFilters='sRGB'
              >
                <feFlood floodOpacity={0} result='BackgroundImageFix' />
                <feBlend
                  mode='normal'
                  in='SourceGraphic'
                  in2='BackgroundImageFix'
                  result='shape'
                />
                <feGaussianBlur
                  stdDeviation={87}
                  result='effect1_foregroundBlur_262_5066'
                />
              </filter>
              <linearGradient
                id='paint0_linear_262_5066'
                x1={21}
                y1={37.5}
                x2={834}
                y2={559.5}
                gradientUnits='userSpaceOnUse'
              >
                <stop stopColor='#327FEF' />
                <stop offset={1} stopColor='white' />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </div>

      <div className='relative z-50 mt-10 grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6'>
        {values.map((value) => (
          <ValueCardItem key={value.title} value={value} />
        ))}
      </div>
    </Section>
  );
}
