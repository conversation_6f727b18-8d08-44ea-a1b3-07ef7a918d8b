export default function BackgroundLightEffect(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox='0 0 1568 1600'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g opacity={0.8} filter='url(#filter0_f_12_327)'>
        <rect
          x={400}
          y={400}
          width={768}
          height={800}
          fill='url(#paint0_linear_12_327)'
        />
      </g>
      <defs>
        <filter
          id='filter0_f_12_327'
          x={0}
          y={0}
          width={1568}
          height={1600}
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity={0} result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur stdDeviation={200} result='effect1_foregroundBlur_12_327' />
        </filter>
        <linearGradient
          id='paint0_linear_12_327'
          x1={784}
          y1={1200}
          x2={784}
          y2={400}
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#327FEF' />
          <stop offset={0.5} stopColor='#FF8300' />
          <stop offset={1} stopColor='#327FEF' />
        </linearGradient>
      </defs>
    </svg>
  );
}
