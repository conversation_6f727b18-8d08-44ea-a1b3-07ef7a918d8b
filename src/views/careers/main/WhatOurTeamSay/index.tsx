'use client';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { useLocale, useTranslations } from 'next-intl';
import { Carousel } from './Carousel';
import { OurTeamSayDataItem, OurTeamSaySlide } from '../types';
import { getTranslatedData } from '@/hooks/useTranslatedData';
import { getAssetUrl } from '@/utils/url/asset';
import { Translation } from '@/types/resources';

interface IWhatOurTeamSayProps {
  carouselGroups: OurTeamSayDataItem[];
}

export default function WhatOurTeamSay({ carouselGroups }: IWhatOurTeamSayProps) {
  const t = useTranslations('career.what-our-team-say');
  const locale = useLocale();

  const slides = carouselGroups.map<OurTeamSaySlide>((item) => {
    const translated = getTranslatedData(item.item.translations, locale) as
      | Translation
      | undefined;

    return {
      id: item.id,
      name: translated?.title || '',
      position: translated?.subtitle || '',
      image: getAssetUrl(item.item.asset.filename_disk) || '',
      description: translated?.description || '',
    };
  });

  return (
    <Section container className='flex flex-col gap-10 md:gap-20'>
      <HeroTitle
        className='w-full items-center text-center'
        title={t('title')}
        titleClassName='md:whitespace-pre-line'
        description={t('description')}
        descriptionClassName='w-full max-w-full md:max-w-[765px] text-base leading-7'
        tag={t('tag')}
      />
      <Carousel slides={slides} />
    </Section>
  );
}
