'use client';
import { Glasscard } from '@/components/common/Card';
import { CarouselButton } from '@/components/common/CarouselButton';
import QuoteIcon from '@/components/icons/QuoteIcon';
import { useEmblaCarouselButtons } from '@/hooks/useEmblaCarouselButtons';
import { cn } from '@/utils/cn';
import { getCdnUrl } from '@/utils/url/asset';
import useEmblaCarousel from 'embla-carousel-react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';
import { OurTeamSaySlide } from '../types';
import BackgroundLightEffect from './BackgroundLightEffect';

interface SlideProps {
  index: number;
  slide: OurTeamSaySlide;
  onHover: React.Dispatch<React.SetStateAction<boolean>>;
}

const Slide = ({ slide, onHover }: SlideProps) => {
  const slideRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={slideRef}
      className='h-full min-w-[0] flex-[0_0_100%]'
      onMouseLeave={() => onHover(true)}
      onMouseEnter={() => onHover(false)}
    >
      <div className='relative z-20 flex h-full flex-col items-center'>
        <Image
          src={slide.image}
          alt={slide.name}
          width={600}
          height={800}
          className='overflow-hidden object-cover'
        />
        <div className={cn('relative flex h-60 lg:gap-4 xl:gap-10')}>
          <QuoteIcon className='hidden opacity-20 lg:block' />
          <Glasscard className='group w-fit gap-3 rounded-lg bg-black/16'>
            <article className='h-full w-full max-w-[800px] rounded-xl border border-white/10 p-4'>
              <div className='flex flex-col gap-1 py-2.5 lg:gap-2'>
                <h2 className='text-2xl font-bold md:text-4xl'>{slide.name}</h2>
                <p className='text-primary text-sm font-medium'>{slide.position}</p>
              </div>
              <p className='text-sm leading-[160%] font-normal text-gray-50 md:text-base lg:text-lg'>
                {slide.description}
              </p>
            </article>
          </Glasscard>
          <div className='flex flex-col justify-end'>
            <QuoteIcon className='hidden rotate-180 opacity-20 lg:block' />
          </div>
        </div>
      </div>
    </div>
  );
};

interface CarouselProps {
  slides: OurTeamSaySlide[];
}

export function Carousel({ slides }: CarouselProps) {
  const [animatedNumber, setAnimatedNumber] = useState(0);

  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);

  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: 'center' });

  const { selectedIndex, onDotButtonClick, onPrevButtonClick, onNextButtonClick } =
    useEmblaCarouselButtons(emblaApi);

  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying]);

  useEffect(() => {
    if (Math.floor(animatedNumber) === 9) {
      onNextButtonClick();
    }
  }, [Math.floor(animatedNumber)]);

  useEffect(() => {
    progressRef.current = 0;
    setAnimatedNumber(0);
  }, [selectedIndex]);

  return (
    <div className='relative w-full'>
      <div ref={emblaRef} className='overflow-hidden'>
        <div className='flex w-full'>
          {slides.map((slide, index) => (
            <Slide key={index} slide={slide} index={index} onHover={setIsPlaying} />
          ))}
        </div>
      </div>
      <Image
        src={getCdnUrl('/careers/vns-logo-bg.webp')}
        alt='vns-logo-holder'
        width={1440}
        height={840}
        priority
        loading='eager'
        className='absolute top-10 left-1/2 -z-20 h-[840px] w-fit -translate-x-1/2 object-contain'
      />
      <BackgroundLightEffect className='absolute top-1/3 left-1/2 -z-20 size-[150%] -translate-x-1/2 -translate-y-1/2' />

      <motion.div className='absolute top-1/2 left-1/12 z-50 hidden h-full -translate-y-1/2 items-center md:flex'>
        <CarouselButton direction='left' onClick={onPrevButtonClick} />
      </motion.div>
      <motion.div className='absolute top-1/2 right-1/12 z-50 hidden h-full -translate-y-1/2 items-center md:flex'>
        <CarouselButton direction='right' onClick={onNextButtonClick} />
      </motion.div>
      <div className='mt-5 flex items-center justify-center gap-2'>
        {slides.map((_, index: number) => (
          <motion.div
            key={`dot-${index}`}
            onClick={() => onDotButtonClick(index)}
            className={cn('h-0.5 w-12 cursor-pointer rounded-2xl bg-white')}
          >
            <motion.div
              animate={{
                width: index === selectedIndex ? `${animatedNumber * 12}%` : 0,
              }}
              className='bg-primary h-full'
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
}
