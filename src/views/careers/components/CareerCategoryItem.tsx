import {
  CardContent,
  CardDescription,
  CardTitle,
  Glasscard,
} from '@/components/common/Card';
import CaseIcon from '@/components/icons/CaseIcon';
import CodeIcon from '@/components/icons/CodeIcon';
import FinanceIcon from '@/components/icons/FinanceIcon';
import MonitorIcon from '@/components/icons/MonitorIcon';
import SpeakerIcon from '@/components/icons/SpeakerIcon';
import UsersIcon from '@/components/icons/UsersIcon';
import { IconArrowRight } from '@tabler/icons-react';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import React from 'react';
import { JobDepartmentAvailable } from '../main/types';
import SaleIcon from '@/components/icons/SaleIcon';

interface CareerCategoryItemProps {
  item: JobDepartmentAvailable;
}

const CareerCategoryIcons: Record<string, React.ReactNode> = {
  '[AIS] Data Insights - Vsilicon': <CodeIcon className='size-12' />,
  '[BA] Business Analysis - Vsilicon': <CaseIcon className='size-12' />,
  '[BDD] Business Development - Vsilicon': <CaseIcon className='size-12' />,
  '[BE] Back-end - Vsilicon': <CodeIcon className='size-12' />,
  '[CORP] Corporate Services - Vsilicon': <SaleIcon className='size-12' />,
  '[CPL] Compliance - Vsilicon': <MonitorIcon className='size-12' />,
  '[DELI] Delivery Group - Vsilicon': <MonitorIcon className='size-12' />,
  '[DvO] DevOps - Vsilicon': <CodeIcon className='size-12' />,
  '[FE] Front-end - Vsilicon': <CodeIcon className='size-12' />,
  '[FIN] Accounting & Finance - Vsilicon': <FinanceIcon className='size-12' />,
  '[GRC] Governance, Risk and Compliance - Vsilicon': <FinanceIcon className='size-12' />,
  '[HRS] Human Resources - Vsilicon': <UsersIcon className='size-12' />,
  '[ITS] IT Department - Vsilicon': <MonitorIcon className='size-12' />,
  '[LEA] Managing Director Office - Vsilicon': <CaseIcon className='size-12' />,
  '[MKT] Marketing and Communication - Vsilicon': <SpeakerIcon className='size-12' />,
  '[MOB] Mobile Dev - Vsilicon': <CodeIcon className='size-12' />,
  '[OPE] Operations - Vsilicon': <MonitorIcon className='size-12' />,
  '[PO] Product Owner - Vsilicon': <CaseIcon className='size-12' />,
  '[PQA] Process Quality Assurance  - Vsilicon': <MonitorIcon className='size-12' />,
  '[QC] Quality Control - Vsilicon': <CodeIcon className='size-12' />,
};

export default function CareerCategoryItem({ item }: CareerCategoryItemProps) {
  const t = useTranslations('career.category');

  return (
    <Link
      href={`/careers/open-positions?departments=${encodeURIComponent(item.name)}`}
      prefetch={false}
      className='min-h-[244px] w-full cursor-pointer rounded-2xl bg-black/5'
    >
      <Glasscard
        isShadow={false}
        className='group h-full bg-black/25 from-blue-900 from-0% to-blue-950 to-100% transition-all duration-300 ease-in-out'
      >
        <motion.div
          initial={{
            background:
              'linear-gradient(180deg, rgba(50, 127, 239, 0) 0%, rgba(29, 73, 137, 0) 100%)',
          }}
          whileHover={{
            background: [
              'linear-gradient(360deg, rgba(50, 127, 239, 0) 0%, rgba(29, 73, 137, 0) 100%)',
              'linear-gradient(111.67deg, rgba(50, 127, 239, 0.32) 0%, rgba(29, 73, 137, 0.32) 100%)',
            ],
            transition: {
              duration: 0.3,
              ease: 'easeInOut',
              repeatType: 'reverse',
            },
          }}
          className='h-full w-full rounded-2xl border border-white/10 p-4 group-hover:border-transparent hover:border-transparent'
        >
          <CardContent className='flex h-full flex-col gap-6 px-0'>
            <CardTitle className='shrink-0 text-gray-50 group-hover:text-gray-50'>
              {CareerCategoryIcons[item.name]}
            </CardTitle>
            <CardDescription className='flex flex-1 flex-col gap-2 text-base leading-[160%] group-hover:text-gray-50'>
              <h3 className='w-full overflow-hidden text-2xl leading-[120%] font-semibold text-wrap text-ellipsis'>
                {item.department_name}
              </h3>
              <div className='flex items-center gap-4'>
                <p className='text-gray-700 group-hover:text-gray-50'>{`${item.availableJobs} ${item.availableJobs > 1 ? t('available-jobs') : t('available-job')} `}</p>
                <IconArrowRight className='text-primary size-6 group-hover:text-gray-50' />
              </div>
            </CardDescription>
          </CardContent>
        </motion.div>
      </Glasscard>
    </Link>
  );
}
