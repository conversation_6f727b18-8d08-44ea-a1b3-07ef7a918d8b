export default function RectangleBackground(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width={1083}
      height={1484}
      viewBox='0 0 1083 1484'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g opacity={0.5}>
        <path
          d='M741.985 211.497L453.225 351.185L452.969 143.073L739.804 3.22545L741.985 211.497Z'
          stroke='currentColor'
          strokeWidth={4}
        />
        <path
          d='M940.762 861.518L385.225 1143.68L384.999 805.469L940.761 521.514L940.762 861.518Z'
          stroke='currentColor'
          strokeWidth={4}
        />
        <path
          d='M641.762 1228.5L126.224 1480.73L126.001 1172.49L641.761 918.46L641.762 1228.5Z'
          stroke='currentColor'
          strokeWidth={4}
        />
      </g>
    </svg>
  );
}
