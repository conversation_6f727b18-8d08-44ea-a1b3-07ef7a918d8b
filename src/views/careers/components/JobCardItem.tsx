'use client';
import { Button } from '@/components/common/Button';
import {
  <PERSON>A<PERSON>,
  CardContent,
  CardDescription,
  CardTitle,
  Glasscard,
} from '@/components/common/Card';
import { Job } from '@/types/job';
import { IconPointFilled } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { motion } from 'framer-motion';

interface JobCardItemProps {
  job: Job;
}

function JobCardItem({ job }: JobCardItemProps) {
  const t = useTranslations('career.job-list');

  return (
    <Link
      href={`/careers/${job.route?.split('/').pop()}`}
      className='min-h-[244px] w-full cursor-pointer rounded-2xl bg-black/10'
    >
      <Glasscard
        borderWidth={2}
        className='group h-full bg-black/25 from-blue-900 from-0% to-blue-950 to-100% transition-all duration-300 ease-in-out'
      >
        <motion.div
          initial={{
            background:
              'linear-gradient(180deg, rgba(50, 127, 239, 0) 0%, rgba(29, 73, 137, 0) 100%)',
          }}
          whileHover={{
            background: [
              'linear-gradient(360deg, rgba(50, 127, 239, 0) 0%, rgba(29, 73, 137, 0) 100%)',
              'linear-gradient(111.67deg, rgba(50, 127, 239, 0.32) 0%, rgba(29, 73, 137, 0.32) 100%)',
            ],
            transition: {
              duration: 0.3,
              ease: 'easeInOut',
              repeatType: 'reverse',
            },
          }}
          className='flex h-full w-full flex-col justify-between gap-4 rounded-2xl border border-white/10 p-4 group-hover:border-transparent hover:border-transparent'
        >
          <CardContent className='flex flex-col gap-0.5 px-0'>
            <CardTitle className='flex w-full items-center gap-2 text-gray-700 group-hover:text-gray-50'>
              <p className='max-w-full truncate text-base leading-[160%] font-normal'>
                {job.department_name || job.department}
              </p>
              <IconPointFilled className='size-2 shrink-0' />
            </CardTitle>
            <h4 className='text-2xl leading-9 font-semibold text-gray-50'>
              {job.job_title}
            </h4>
            <CardDescription className='flex items-center gap-2 text-base leading-[160%] text-gray-700 group-hover:text-gray-50'>
              <p>{'VNS'}</p>
              <IconPointFilled className='size-2' />
              <p>{'HCMC'}</p>
              <IconPointFilled className='size-2' />
              <p>{job.employment_type}</p>
            </CardDescription>
          </CardContent>
          <CardAction className='w-full'>
            <Button
              variant='outline'
              aria-label='View job'
              className='group-hover:bg-primary group-hover:border-primary w-full border-blue-700 bg-transparent text-gray-50 transition-colors duration-300 ease-in-out'
            >
              {t('view-job')}
            </Button>
          </CardAction>
        </motion.div>
      </Glasscard>
    </Link>
  );
}

export default JobCardItem;
