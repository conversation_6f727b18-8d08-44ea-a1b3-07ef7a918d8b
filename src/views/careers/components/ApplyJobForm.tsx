'use client';
import { But<PERSON> } from '@/components/common/Button';
import { Card, CardData } from '@/components/common/Card';
import { Checkbox } from '@/components/common/Checkbox';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/common/Form';
import { Input } from '@/components/common/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/Select';
import { SlashDiv } from '@/components/common/SlashBackground';
import UploadIcon from '@/components/icons/UploadIcon';
import { ScrollMotionDiv } from '@/components/motion/ScrollMotionDiv';
import { TextShimmer } from '@/components/motion/TextShimmer';
import { Job } from '@/types/job';
import { cn } from '@/utils/cn';
import { zodResolver } from '@hookform/resolvers/zod';
import { IconArrowRight, IconFile } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

interface JobFormProps {
  job: Job;
}

const formSchema = z.object({
  email: z.email({ message: 'Invalid email address.' }),
  phone: z
    .string()
    .min(1, { message: 'Phone is required.' })
    .regex(/^[0-9+\-\s()]*$/, { message: 'Invalid phone number format.' })
    .min(10, { message: 'Phone number must be at least 10 digits.' })
    .max(15, { message: 'Phone number must not exceed 15 digits.' }),
  fullName: z.string().min(1, {
    message: 'Full name is required.',
  }),
  discoverySource: z.string(),
  agreeToTerms: z.boolean().refine(
    (data) => {
      return data;
    },
    {
      message: 'You have to agree to the terms and conditions.',
    }
  ),
  resume: z.instanceof(File, { message: 'Resume is required.' }),
  location: z.string(),
});

function ApplyJobForm({ job }: JobFormProps) {
  const t = useTranslations('career.apply-form');
  const router = useRouter();
  const pathName = usePathname();

  const [loading, setLoading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles[0]?.size > 10 * 1024 * 1024) {
      form.setError('resume', {
        type: 'manual',
        message: 'File size should not exceed 10MB.',
      });
    } else {
      form.setValue('resume', acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, open, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
    },
    multiple: false,
    noClick: true,
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
      phone: '',
      fullName: '',
      discoverySource: 'Company website',
      agreeToTerms: false,
      location: job.location,
      resume: undefined,
    },
  });

  const handleSubmitResume = async (data: z.infer<typeof formSchema>) => {
    setLoading(true);
    try {
      // Upload the resume file
      // const uploadedFile = await uploadFile(data.resume);

      // Prepare the job application data
      // const jobApplicationData = {
      //   ...data,
      //   job_title: job?.name,
      //   designation: job?.designation,
      //   applicant_name: data.fullName,
      //   email_id: data.email,
      //   phone_number: data.phone,
      //   country: 'Vietnam',
      //   resume_attachment: uploadedFile.message.file_url,
      //   source: 'Website Listing',
      //   custom_data_policy: 1,
      //   web_form_name: 'job-application-submit',
      //   discovery_source: data.discoverySource,
      // };
      // Submit the job application
      // await submitJobApplication(jobApplicationData);

      router.push(`${pathName}/submitted`);
      form.reset();
    } catch (error) {
      console.error('Error submitting job application:', error);
      toast.error(t('submit-error'));
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  return (
    <div id='apply-form'>
      <SlashDiv h={2000} className='relative w-full items-start px-4'>
        <Card className='relative z-50 w-full gap-12 overflow-hidden rounded-sm px-0 py-10 lg:px-16 xl:container xl:mx-auto 2xl:px-80 2xl:py-[100px]'>
          <div className='absolute inset-0 -z-10 h-full w-full backdrop-blur-sm' />
          <ScrollMotionDiv
            transformTo={[-100, 200]}
            className='absolute top-0 left-0 hidden lg:block'
          >
            <svg
              width='658'
              height='886'
              viewBox='0 0 658 886'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
              className='text-blue-800'
            >
              <g opacity={0.4}>
                <path
                  d='M-282.816 263.518L272.72 545.676L272.946 207.469L-282.816 -76.4858L-282.816 263.518Z'
                  stroke='currentColor'
                  strokeWidth={4}
                />
                <path
                  d='M16.1828 630.497L531.721 882.729L531.944 574.489L16.1842 320.46L16.1828 630.497Z'
                  stroke='currentColor'
                  strokeWidth={4}
                />
              </g>
            </svg>
          </ScrollMotionDiv>
          <ScrollMotionDiv
            transformTo={[-100, 150]}
            className='absolute right-0 bottom-0 hidden lg:block'
          >
            <svg
              width='824'
              height='1295'
              viewBox='0 0 824 1295'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
              className='text-orange-800'
            >
              <g opacity={0.6}>
                <path
                  d='M940.762 861.518L385.225 1143.68L384.999 805.469L940.761 521.514L940.762 861.518Z'
                  stroke='currentColor'
                  strokeWidth={4}
                />
                <path
                  d='M641.762 1228.5L126.224 1480.73L126.001 1172.49L641.761 918.46L641.762 1228.5Z'
                  stroke='currentColor'
                  strokeWidth={4}
                />
              </g>
            </svg>
          </ScrollMotionDiv>
          <h3 className='w-full text-center text-[3rem] leading-[110%] font-bold'>
            {t.rich('title', {
              span: (chunks) => <span className='text-primary'>{chunks}</span>,
            })}
          </h3>
          <div className='z-50 w-full rounded-xl md:p-6 lg:bg-black/10 lg:backdrop-blur-sm xl:p-20'>
            <h4 className='px-4 text-center text-3xl leading-10 font-semibold tracking-[3.84%] sm:text-[32px] sm:leading-12 md:px-0 md:text-left'>
              {t('subtitle')}
            </h4>
            <p className='mb-5 px-4 text-center text-base leading-7 md:px-0 md:text-left'>
              {t('description')}
            </p>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmitResume)}
                className='px-4 md:px-0'
              >
                <CardData className='mb-5 p-6 backdrop-blur-sm lg:backdrop-blur-none'>
                  <div className='mb-4'>
                    <h3 className='mb-1 text-lg leading-7 font-bold text-gray-50'>
                      {t('media-upload.title')}
                    </h3>
                    <p className='text-sm leading-5 text-gray-50'>
                      {t('media-upload.description')}
                    </p>
                  </div>
                  <FormField
                    name='resume'
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div
                            className={cn(
                              'relative w-full cursor-pointer rounded-lg border-2 border-dashed border-gray-50 p-4 transition-all duration-200 hover:border-blue-800 hover:bg-blue-800/5 lg:p-14',
                              isDragActive ? 'border-blue-800 bg-blue-800/5' : ''
                            )}
                            onClick={open}
                          >
                            <div
                              {...getRootProps({ className: 'dropzone' })}
                              className='flex flex-col items-center justify-center gap-2'
                            >
                              <UploadIcon className='size-[42px]' />
                              <div className='flex w-fit flex-col items-center justify-center gap-2'>
                                <p className='text-center text-lg font-bold'>
                                  {t('media-upload.action')}
                                </p>
                                <div className='flex w-full items-center space-x-4'>
                                  <div className='h-px flex-1 bg-blue-400/50'></div>
                                  <span className='px-0.5 text-sm text-blue-300'>
                                    {t('media-upload.or')}
                                  </span>
                                  <div className='h-px flex-1 bg-blue-400/50'></div>
                                </div>
                                <Button
                                  type='button'
                                  variant='outline'
                                  className='rounded-lg bg-white font-semibold text-blue-800'
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    open();
                                  }}
                                  aria-label='Upload resume'
                                >
                                  {t('media-upload.button')}
                                </Button>
                              </div>
                              <input {...getInputProps()} />
                              {field.value && (
                                <CardData className='mt-2 flex w-fit flex-row items-center gap-2 rounded-md px-5 py-3'>
                                  <IconFile size={16} />
                                  {field.value?.name}
                                </CardData>
                              )}
                            </div>
                          </div>
                        </FormControl>
                        <FormDescription>{t('media-upload.note')}</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardData>
                <div className='w-full'>
                  <h5 className='text-2xl leading-9 font-bold text-gray-50'>
                    {t('field.job-title')}
                  </h5>
                  <Input
                    value={job.job_title}
                    readOnly
                    className='mt-4 rounded-[10px] border-2 border-gray-400 !text-base leading-[160%] font-normal !text-neutral-100 !shadow-none transition-colors placeholder:text-base focus:outline-none focus-visible:!ring-0 focus-visible:!ring-offset-0'
                  />
                </div>
                <div className='mt-10'>
                  <h5 className='mb-4 text-2xl leading-9 font-bold text-gray-50'>
                    {t('contact-details')}
                  </h5>
                  <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                    <FormInput
                      control={form.control}
                      name='fullName'
                      required
                      label={t('field.full-name')}
                      placeholder={t('field.full-name-placeholder')}
                    />
                    <FormInput
                      control={form.control}
                      name='phone'
                      required
                      label={t('field.phone')}
                      placeholder={t('field.phone-placeholder')}
                    />
                    <FormInput
                      control={form.control}
                      name='email'
                      required
                      label={t('field.email')}
                      placeholder={t('field.email-placeholder')}
                    />
                    <FormInput
                      control={form.control}
                      name='location'
                      required
                      readOnly
                      label={t('field.location')}
                    />
                  </div>
                </div>
                <FormField
                  name='discoverySource'
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className='mt-6'>
                      <FormLabel className='!text-2xl leading-9 font-bold text-gray-50'>
                        {t('field.known-about-us')}
                      </FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger className='focus:border-primary mt-2 rounded-[10px] border-2 border-gray-400 bg-white !text-base leading-[160%] font-normal !text-neutral-100 !shadow-none transition-colors placeholder:text-base focus:outline-none focus-visible:!ring-0 focus-visible:!ring-offset-0'>
                            <SelectValue />
                          </SelectTrigger>

                          <SelectContent className='text-neutral-100'>
                            {[
                              'LinkedIn',
                              'Facebook',
                              'ITViec',
                              'Company website',
                              'From VNS staffs',
                              'Other channels',
                            ].map((item) => (
                              <SelectItem
                                key={item}
                                value={item}
                                className='hover:bg-primary/5 hover:!text-primary !text-base leading-[160%] font-normal !text-neutral-100 transition-colors'
                              >
                                {item}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className='mt-6 h-[1px] w-full bg-[#6A6A6A]' />

                <FormField
                  name='agreeToTerms'
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className='mt-6'>
                      <div className='flex flex-row items-start space-y-0 space-x-3'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className='data-[state=checked]:bg-primary data-[state=checked]:border-primary mt-1'
                            aria-label='Agree to terms and conditions'
                          />
                        </FormControl>{' '}
                        <FormLabel className='text-base leading-6 tracking-[1.92%] text-gray-50'>
                          {t.rich('terms', {
                            a: (chunks) => (
                              <Link
                                href='/privacy'
                                target='_blank'
                                className='hover:text-primary underline underline-offset-2'
                              >
                                {chunks}
                              </Link>
                            ),
                          })}
                        </FormLabel>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type='submit'
                  disabled={loading}
                  className={cn('bg-primary mt-6 w-full rounded-[10px] font-semibold')}
                  aria-label='Submit job application'
                >
                  <TextShimmer
                    className='text-base [--base-color:var(--color-gray-50)] [--base-gradient-color:var(--color-primary)]'
                    duration={loading ? 1 : 0}
                  >
                    {t(loading ? 'field.submitting' : 'field.submit')}
                  </TextShimmer>

                  {!loading && <IconArrowRight />}
                </Button>

                {/* <p className='mt-4 text-center text-base leading-6 tracking-[1.92%] text-gray-50'>
                  {t.rich('google-policy', {
                    a: (chunks) => (
                      <Link
                        href='https://policies.google.com/privacy'
                        target='_blank'
                        className='hover:text-primary underline'
                      >
                        {chunks}
                      </Link>
                    ),
                    link: (chunks) => (
                      <Link
                        href='https://policies.google.com/terms'
                        target='_blank'
                        className='hover:text-primary underline'
                      >
                        {chunks}
                      </Link>
                    ),
                  })}
                </p> */}
              </form>
            </Form>
          </div>
        </Card>
      </SlashDiv>
    </div>
  );
}

const FormInput = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<'input'> & {
    control: any;
    name?: keyof z.infer<typeof formSchema>;
    className?: string;
    label?: string;
    required?: boolean;
  }
>(({ control, name, label, className, type, required, ...props }, ref) => {
  return (
    <FormField
      control={control}
      name={name as string}
      render={({ field }) => (
        <FormItem>
          <FormLabel className='flex gap-1 text-base leading-6 text-gray-50'>
            {label}
            {required && <span className='text-[#FF3B30]'>*</span>}
          </FormLabel>
          <FormControl>
            <Input
              {...props}
              {...field}
              ref={ref}
              className='focus:border-primary mt-2 rounded-[10px] border-2 border-gray-400 !text-base leading-[160%] font-normal !text-neutral-100 !shadow-none transition-colors placeholder:text-base focus:outline-none focus-visible:!ring-0 focus-visible:!ring-offset-0'
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
});

export default ApplyJobForm;
