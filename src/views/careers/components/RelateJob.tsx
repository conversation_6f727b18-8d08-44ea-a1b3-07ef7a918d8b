import { Button } from '@/components/common/Button';
import { <PERSON>, CardContent, CardHeader } from '@/components/common/Card';
import NoDataIcon from '@/components/icons/NoDataIcon';
import { Job } from '@/types/job';
import { IconArrowRight } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import JobCardItem from './JobCardItem';

interface IRelateJobProps {
  jobs: Job[];
}

export default function RelateJob({ jobs }: IRelateJobProps) {
  const t = useTranslations('career.related-jobs');

  return (
    <div className='relative z-20 mt-10 px-4 pb-20 lg:mt-20 xl:container xl:mx-auto xl:px-0'>
      <Card className='gap-6 rounded-sm p-4 xl:px-32 xl:py-24'>
        <CardHeader className='flex w-full items-center justify-between gap-3 !px-0'>
          <h3 className='text-left text-[clamp(2rem,1.7573rem+1.0356vw,3rem)] leading-[110%] font-semibold'>
            {t.rich('title', {
              span: (chunks) => <span className='text-primary'>{chunks}</span>,
            })}
          </h3>
          <Link href='/careers/open-positions'>
            <Button
              variant='link'
              aria-label='Show all jobs'
              className='text-primary text-base leading-[160%] font-semibold hover:no-underline hover:opacity-80'
            >
              <span className='hidden md:inline'>{t('show-all')}</span>
              <IconArrowRight />
            </Button>
          </Link>
        </CardHeader>
        <CardContent className='px-0'>
          {jobs.length > 0 ? (
            <div className='grid w-full grid-cols-1 gap-4 md:grid-cols-2 xl:col-span-3 xl:grid-cols-3'>
              {jobs.map((job) => (
                <JobCardItem key={job.name} job={job} />
              ))}
            </div>
          ) : (
            <div className='flex h-full flex-col items-center justify-center gap-5 text-gray-600 xl:col-span-3'>
              <NoDataIcon className='w-48' />
              <p className='text-center text-xl'>{t('no-jobs')}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
