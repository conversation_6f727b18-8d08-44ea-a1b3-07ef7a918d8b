import { Card } from '@/components/common/Card';
import Tag from '@/components/common/Tag';
import { getAssetUrl, getCdnUrl } from '@/utils/url/asset';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { InfiniteSlider } from '@/components/motion/InfiniteSlider';

interface CompanyPartnerProps {
  partners: any;
}

export default function CompanyPartner({ partners }: CompanyPartnerProps) {
  const t = useTranslations('career');

  const images = (partners || []).map((partner: any) => ({
    src: getAssetUrl(partner.asset.filename_disk),
    alt: partner.name as string,
    width: 300,
    height: 60,
    // click_url: partner.click_url,
  })) as Array<{
    src: string;
    alt: string;
    width: number;
    height: number;
  }>;

  return (
    <>
      <Card className='flex w-full flex-col items-center gap-12 rounded-none py-8'>
        <div className='flex items-center justify-center'>
          <Tag className='w-fit text-sm leading-4 !tracking-[18%]'>
            {t('partner-tag')}
          </Tag>
        </div>
        <InfiniteSlider className='w-full'>
          {images.map((partner, index) => (
            <div key={index} className='h-16 flex-shrink-0'>
              <Image
                {...partner}
                unoptimized
                className='aspect-[3/1] max-h-full max-w-full object-contain'
              />
            </div>
          ))}
        </InfiniteSlider>
      </Card>
    </>
  );
}
