'use client';
import { Button } from '@/components/common/Button';
import { Input } from '@/components/common/Input';
import { AuroraBackground } from '@/components/layout/AuroraBackground';
import { getCdnUrl } from '@/utils/url/asset';
import { IconSearch } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import OrangeUnderline from './OrangeUnderline';

interface HeroSectionProps {
  sectionKey?: 'main' | 'jobs' | 'detail';
}

export default function HeroSection({ sectionKey = 'main' }: HeroSectionProps) {
  const t = useTranslations('career.hero-section');
  const router = useRouter();
  const [keywords, setKeywords] = useState<string>('');

  const popularKeywords = ['Backend', 'Frontend', 'Marketing'];

  const handleSearch = () => {
    router.replace(
      `/careers/open-positions${keywords ? `?keyword=${encodeURIComponent(keywords)}` : ''}`,
      { scroll: false }
    );
  };

  const handleKeywordClick = (keyword: string) => {
    setKeywords(keyword);
    router.replace(`/careers/open-positions?keyword=${encodeURIComponent(keyword)}`, {
      scroll: false,
    });
  };

  return (
    <AuroraBackground className='h-full'>
      <div className='h-full w-full py-16 lg:h-[913px] lg:pt-[100px]'>
        <div className='relative container mx-auto flex h-full flex-col items-start justify-center gap-5 px-8'>
          <div
            className='absolute top-1/2 right-1/2 -z-30 h-full w-fit translate-x-1/2 -translate-y-1/2 opacity-30 md:right-0 md:translate-x-0'
            style={{
              maskImage:
                'linear-gradient(330deg, transparent 0%, transparent 30%,  black 70%)',
            }}
          >
            <Image
              width={624}
              height={624}
              priority
              src='/logo-3d.webp'
              alt='logo'
              className='size-full object-contain'
            />
          </div>
          <div
            className='absolute top-0 right-1/2 hidden h-full max-h-[624px] w-fit translate-x-1/2 md:right-0 md:block md:translate-x-0'
            style={{
              maskImage: `linear-gradient(0deg,rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 1) 25%, rgba(0, 0, 0, 1) 70%)`,
            }}
          >
            <Image
              src={getCdnUrl('/careers/hero-default-avatar.webp')}
              alt='Hero Default Avatar'
              width={442}
              height={624}
              priority
              className='h-full w-full object-contain'
            />
          </div>
          <div className='relative w-fit'>
            <h3 className='text-foreground text-left text-3xl leading-12 font-bold whitespace-pre-line md:text-5xl md:leading-16 lg:leading-[94px]'>
              {t.rich(`${sectionKey}.title`, {
                span: (children: React.ReactNode) => (
                  <p className='text-blue-800'>{children}</p>
                ),
              })}
            </h3>
            <OrangeUnderline className='w-full' />
          </div>
          <div>
            <p className='w-full text-left text-sm leading-[160%] md:text-base md:whitespace-pre-line lg:text-xl'>
              {t(`${sectionKey}.subtitle`)}
            </p>
          </div>

          <div className='z-[999]'>
            <div className='card-shadow flex flex-col items-center gap-4 rounded-lg bg-blue-50 p-4 md:flex-row'>
              <div className='flex items-center gap-4 rounded-md'>
                <IconSearch className='text-neutral-100' />
                <Input
                  size={52}
                  value={keywords}
                  onChange={(e) => setKeywords(e.target.value)}
                  placeholder={t('search-placeholder')}
                  className='placeholder:text-neutrals-60 border-b-neutral-60 border-neutral-20 focus:border-primary h-full rounded-none border-0 border-b-[1px] !bg-transparent px-0 !text-base leading-[160%] font-normal !text-neutral-100 !shadow-none transition-colors placeholder:text-base focus:outline-none focus-visible:!ring-0 focus-visible:!ring-offset-0'
                />
              </div>
              <Button
                size='lg'
                className='w-full px-[49px] !py-3.5 leading-6 md:w-fit'
                onClick={handleSearch}
                aria-label='Search jobs'
              >
                {t('search-button')}
              </Button>
            </div>
            <div className='mt-4 flex w-full items-start'>
              <span className='text-base leading-[160%]'>{t('popular-keyword')}</span>
              <div className='ml-2 flex flex-wrap items-center gap-0.5'>
                {popularKeywords.map((keyword, index) => (
                  <span
                    key={keyword}
                    className='mr-0.5 cursor-pointer text-base leading-[160%] underline-offset-4 hover:underline'
                    onClick={() => handleKeywordClick(keyword)}
                  >
                    {index < popularKeywords.length - 1 ? keyword + ',' : keyword}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AuroraBackground>
  );
}
