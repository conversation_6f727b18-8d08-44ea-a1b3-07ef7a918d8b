import { cn } from '@/utils/cn';

const ProcessFlow = ({ steps = [] }: { steps: string[] }) => {
  return (
    <>
      <div className={cn('relative z-10 grid w-full grid-cols-2 gap-y-3 xl:grid-cols-5')}>
        {steps.map((step, index) => {
          const isFirst = index === 0;
          const isLast = index === steps.length - 1;

          return (
            <div
              key={step}
              className={cn('process-flow', {
                start: isFirst,
                end: isLast,
                middle: !isFirst && !isLast,
              })}
            >
              <p className='absolute inset-0 flex items-center px-6 py-4 text-base leading-5 font-medium lg:text-lg lg:leading-7'>
                {step}
              </p>
            </div>
          );
        })}
      </div>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        version='1.1'
        className='invisible absolute size-0'
      >
        <defs>
          <filter id='round'>
            <feGaussianBlur in='SourceGraphic' stdDeviation='1' result='blur' />
            <feColorMatrix
              in='blur'
              mode='matrix'
              values='1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -1'
              result='goo'
            />
            <feComposite in='SourceGraphic' in2='goo' operator='atop' />
          </filter>
        </defs>
      </svg>
    </>
  );
};

export default ProcessFlow;
