import { Button, buttonVariants } from '@/components/common/Button';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/common/pagination';
import { cn } from '@/utils/cn';
import { IconArrowLeft, IconArrowRight } from '@tabler/icons-react';
import { useMemo } from 'react';

interface IPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export default function JobListPagination({
  currentPage,
  totalPages,
  onPageChange,
}: IPaginationProps) {
  const pages = useMemo(() => {
    const pages = [];
    const maxVisiblePages = 3; // Adjust as needed

    if (totalPages <= maxVisiblePages + 2) {
      // Show all pages if few pages exist
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first, last, and middle pages with ellipsis
      pages.push(1);
      if (currentPage > 2) pages.push('...');
      for (
        let i = Math.max(2, currentPage - 1);
        i <= Math.min(totalPages - 1, currentPage + 1);
        i++
      ) {
        pages.push(i);
      }
      if (currentPage < totalPages - 1) pages.push('...');
      pages.push(totalPages);
    }

    return pages;
  }, [currentPage, totalPages]);

  return (
    <div className='mt-12 flex items-center justify-center'>
      <Pagination>
        <PaginationContent>
          <PaginationItem onClick={() => onPageChange(currentPage ? currentPage - 1 : 1)}>
            <Button
              disabled={currentPage <= 1}
              className={cn(
                buttonVariants({ variant: 'ghost' }),
                'hover:text-primary h-12 w-12 rounded-full border-none bg-transparent hover:bg-gray-50'
              )}
            >
              <IconArrowLeft className='size-6' />
            </Button>
          </PaginationItem>
          {pages.map((page) => {
            if (typeof page === 'string') {
              return (
                <PaginationItem key={page}>
                  <PaginationEllipsis />
                </PaginationItem>
              );
            }

            const isActive = page === currentPage;

            return (
              <PaginationItem key={page} onClick={() => onPageChange(page)}>
                <Button
                  className={cn(
                    'h-12 w-12 rounded-full border-none bg-transparent text-sm leading-5 font-medium text-gray-50 shadow-none hover:bg-blue-500 hover:text-gray-950',
                    { 'bg-primary text-gray-50': isActive }
                  )}
                >
                  {page.toString().padStart(2, '0')}
                </Button>
              </PaginationItem>
            );
          })}
          <PaginationItem onClick={() => onPageChange(currentPage ? currentPage + 1 : 2)}>
            <Button
              disabled={currentPage >= totalPages}
              className={cn(
                buttonVariants({ variant: 'ghost' }),
                'hover:text-primary h-12 w-12 rounded-full border-none bg-transparent hover:bg-gray-50'
              )}
            >
              <IconArrowRight className='size-6' />
            </Button>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
