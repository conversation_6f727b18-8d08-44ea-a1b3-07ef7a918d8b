'use client';
import { Card } from '@/components/common/Card';
import { useTranslations } from 'next-intl';
import ProcessFlow from '../components/ProcessFlow';

function RecruitmentProcess() {
  const t = useTranslations('career.recruitment-process');

  const processSteps = [t('step-1'), t('step-2'), t('step-3'), t('step-4'), t('step-5')];

  return (
    <div className='relative z-20 my-10 px-4 lg:my-20 xl:container xl:mx-auto xl:px-0'>
      <Card className='gap-4 rounded-sm px-6 py-8'>
        <div className='absolute inset-0 -z-10 backdrop-blur-sm' />
        <h3 className='text-2xl leading-6 font-medium text-gray-50'>
          {t.rich('title', {
            span: (chunk) => <span className='text-primary'>{chunk}</span>,
          })}
        </h3>
        <div className='bg-gray-20 h-[1px] w-full' />

        <ProcessFlow steps={processSteps} />
      </Card>
    </div>
  );
}

export default RecruitmentProcess;
