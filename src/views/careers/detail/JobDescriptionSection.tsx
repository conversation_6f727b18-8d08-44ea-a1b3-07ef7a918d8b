'use client';
import { Badge } from '@/components/common/Badge';
import { Button } from '@/components/common/Button';
import { Card, CardContent, CardHeader } from '@/components/common/Card';
import { SlashDiv } from '@/components/common/SlashBackground';
import { ContentView } from '@/components/tiptap';
import { Job } from '@/types/job';
import { IconArrowRight } from '@tabler/icons-react';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import JobOverview from './JobOverview';

interface IJobDescriptionSectionProps {
  job: Job;
  url: string;
}

export default function JobDescriptionSection({ job, url }: IJobDescriptionSectionProps) {
  const t = useTranslations('career.job-detail');

  const handleScroll = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 120, // Adjust the offset as needed
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (typeof window !== 'undefined') {
      timer = setTimeout(() => {
        handleScroll('job-description');
      }, 300);
    }
    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div id='job-description'>
      <SlashDiv h={2000} className='relative mt-20 px-4 xl:container xl:mx-auto xl:px-0'>
        <div className='absolute inset-0 z-0 backdrop-blur-sm' />
        <Card className='relative z-50 h-full gap-6 rounded-sm px-4 py-8 xl:px-32 xl:py-24'>
          {/*  */}
          <CardHeader className='flex w-full flex-col justify-between gap-3 !px-0 lg:flex-row'>
            <div className='flex w-full flex-auto flex-col gap-3'>
              <h3 className='w-full text-left text-[clamp(2rem,1.7573rem+1.0356vw,3rem)] leading-[110%] font-semibold'>
                {t('detail-of')} <span className='text-primary'>{job.job_title}</span>
              </h3>
              <div className='flex items-center gap-3'>
                <Badge className='text-foreground rounded-sm bg-blue-800 px-3 py-1 text-sm leading-5 font-semibold shadow-none'>
                  {'at VNSilicon'}
                </Badge>
                {job.employment_type && (
                  <Badge className='text-foreground rounded-sm bg-blue-800 px-3 py-1 text-sm leading-5 font-semibold uppercase shadow-none'>
                    {job.employment_type}
                  </Badge>
                )}
              </div>
            </div>
            <div className='flex w-fit shrink-0 items-center gap-3'>
              <Button
                size='lg'
                onClick={() => handleScroll('apply-form')}
                aria-label='Apply now'
                className='text-foreground flex h-auto items-center gap-3 bg-[#0A65CC] px-8 py-4 transition-colors duration-300 ease-in-out hover:bg-[#0A65CC]/70'
              >
                <p className='text-base leading-6 font-semibold'>{t('apply-now')}</p>
                <IconArrowRight />
              </Button>
            </div>
          </CardHeader>
          {/*  */}
          <CardContent className='flex flex-col-reverse gap-6 px-0 lg:flex-row'>
            <div className='flex w-full flex-col gap-4 lg:w-[55%]'>
              <ContentView content={job.description || ''} />
            </div>
            <div className='w-full lg:w-[45%]'>
              <JobOverview url={url} job={job} />
            </div>
          </CardContent>
        </Card>
      </SlashDiv>
    </div>
  );
}
