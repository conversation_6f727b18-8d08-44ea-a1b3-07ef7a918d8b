import { Job } from '@/types/job';
import HeroSection from '../components/HeroSection';
import CompanyPartner from '../components/CompanyPartner';
import JobDescriptionSection from './JobDescriptionSection';
import RecruitmentProcess from './RecruitmentProcess';
import ApplyJobForm from '../components/ApplyJobForm';
import RelateJob from '../components/RelateJob';
import RectangleBackground from '../components/RectangleBackground';
import { getPartners } from '@/services/directus';

type Props = {
  job: Job;
  relatedJobs: Job[];
  url: string;
};
export default async function CareerDetailView({ job, relatedJobs, url }: Props) {
  const partners = await getPartners();

  return (
    <section className='bg-background relative z-10 h-full w-full'>
      <RectangleBackground className='absolute top-1/2 right-0 -z-10 -translate-y-1/2 text-orange-800 lg:-right-48' />
      <HeroSection sectionKey='detail' />
      <CompanyPartner partners={partners?.data} />
      <JobDescriptionSection job={job} url={url} />
      <RecruitmentProcess />
      <ApplyJobForm job={job} />
      <RelateJob jobs={relatedJobs} />
    </section>
  );
}
