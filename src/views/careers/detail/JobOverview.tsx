'use client';
import { But<PERSON> } from '@/components/common/Button';
import { CardA<PERSON>, CardContent, Glasscard } from '@/components/common/Card';
import CalenderIcon from '@/components/icons/CalenderIcon';
import ClockCountDownFillIcon from '@/components/icons/ClockCountDownFillIcon';
import FaceBookIcon from '@/components/icons/FaceBookIcon';
import LinkedInIcon from '@/components/icons/LinkedInIcon';
import MDLocaltionIcon from '@/components/icons/MDLocaltionIcon';
import RankingDuetoneIcon from '@/components/icons/RankingDuetoneIcon';
import TwitterXIcon from '@/components/icons/TwitterXIcon';
import { SocialPlatform, useSocialShare } from '@/hooks/useSocialShare';
import { Job } from '@/types/job';
import { DATE_FORMAT } from '@/utils/constants/formatter';
import { extractExperienceYears, getRoleLevel } from '@/utils/func';
import {
  IconBriefcase,
  IconLink,
  IconMail,
  IconStack2Filled,
  IconUnlink,
} from '@tabler/icons-react';
import dayjs from 'dayjs';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface IJobOverviewProps {
  job: Job;
  url: string;
}

function JobOverview({ job, url }: IJobOverviewProps) {
  const t = useTranslations('career.job-detail');
  const tCommon = useTranslations('common');

  const { share } = useSocialShare();

  const [isCopied, setIsCopied] = useState(false);

  const requiredExperienceYears = extractExperienceYears(job.description || '');

  const overviewData = [
    {
      label: t('job-posted'),
      icon: <CalenderIcon className='size-8' />,
      value: dayjs(job.posted_on, DATE_FORMAT.DEFAULT).format(DATE_FORMAT.SHORT_DATE),
    },
    {
      label: t('expired-in'),
      value: dayjs(job.custom_expected_offer_date, DATE_FORMAT.DEFAULT).format(
        DATE_FORMAT.SHORT_DATE
      ),
      icon: <ClockCountDownFillIcon className='size-8' />,
    },
    {
      label: t('job-level'),
      icon: <IconStack2Filled className='size-8' />,
      value: getRoleLevel(job.designation || '') || tCommon('not-specified'),
    },
    {
      label: t('job-experience'),
      icon: <RankingDuetoneIcon className='size-8' />,
      value: requiredExperienceYears
        ? `${requiredExperienceYears}+ ${tCommon('years')}`
        : tCommon('not-required'),
    },
    {
      label: t('education'),
      icon: <IconBriefcase className='size-8' />,
      value: t('bachelor-graduated'),
    },
    {
      label: t('job-location'),
      icon: <MDLocaltionIcon className='size-8' />,
      value: 'HCMC, Vietnam',
    },
  ];

  const handleShare = async (platform: SocialPlatform) => {
    const success = await share(platform, { url, title: job.job_title });
    if (platform === 'copy' && success) {
      toast.success(tCommon('copied'));
      setIsCopied(true);
    }
  };

  useEffect(() => {
    if (isCopied) {
      const timer = setTimeout(() => {
        setIsCopied(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isCopied]);

  return (
    <Glasscard
      borderColor='var(--color-foreground)'
      borderWidth={2}
      borderRadius={16}
      className='flex h-fit flex-col gap-4 bg-black/10 px-0.5'
    >
      <CardContent className='p-5 lg:px-8 lg:pt-9'>
        <h3 className='mb-5 text-lg leading-7 font-medium'>{t('job-overview')}</h3>
        <div className='grid w-full grid-cols-2 gap-4 md:grid-cols-[repeat(auto-fill,minmax(168px,1fr))]'>
          {overviewData.map((item, index) => {
            return (
              <div
                key={index}
                className='flex h-full w-full flex-col gap-3 overflow-hidden'
              >
                {item.icon}
                <div>
                  <p className='text-xs leading-[18px] uppercase'>{item.label}:</p>
                  <p className='truncate text-sm leading-5 font-medium'>{item.value}</p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
      <CardAction className='border-foreground w-full border-t-2 p-5 lg:px-8 lg:pb-9'>
        <p className='mb-2 text-lg leading-7 font-medium text-gray-50'>
          {t('share-job')}:
        </p>
        <div className='flex flex-wrap items-center gap-2'>
          <Button
            className='bg-blue-20 hover:bg-blue-20/80 h-auto p-2 text-blue-800 md:px-4 md:py-2'
            aria-label='Copy link'
            onClick={() => handleShare('copy')}
          >
            {isCopied ? (
              <IconUnlink className='size-6' />
            ) : (
              <IconLink className='size-6' />
            )}

            <span className='hidden text-base leading-6 font-medium md:inline'>
              {isCopied ? tCommon('copied') : tCommon('copy-link')}
            </span>
          </Button>
          <Button
            aria-label='Share on LinkedIn'
            className='bg-blue-20 hover:bg-blue-20/80 h-auto p-[10px] text-blue-800'
            onClick={() => handleShare('linkedin')}
          >
            <LinkedInIcon className='size-5' />
          </Button>
          <Button
            aria-label='Share on Facebook'
            className='h-auto bg-blue-800 p-[10px] hover:bg-blue-800'
            onClick={() => handleShare('facebook')}
          >
            <FaceBookIcon className='size-5' />
          </Button>
          <Button
            aria-label='Share on Twitter'
            className='h-auto bg-black p-[10px] text-white hover:bg-black/80'
            onClick={() => handleShare('twitter')}
          >
            <TwitterXIcon className='size-5' />
          </Button>
          <Button
            aria-label='Share by email'
            className='bg-blue-20 hover:bg-blue-20/80 h-auto p-[10px] text-blue-800'
            onClick={() => handleShare('email')}
          >
            <IconMail className='size-5' />
          </Button>
        </div>
      </CardAction>
    </Glasscard>
  );
}

export default JobOverview;
