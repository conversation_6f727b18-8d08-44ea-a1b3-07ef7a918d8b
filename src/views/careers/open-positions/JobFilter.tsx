'use client';
import { Button } from '@/components/common/Button';
import { CardData } from '@/components/common/Card';
import { Checkbox } from '@/components/common/Checkbox';
import { Input } from '@/components/common/Input';
import { Department } from '@/types/department';
import { IconCircleX, IconSearch } from '@tabler/icons-react';
import { debounce } from 'lodash-es';
import { useTranslations } from 'next-intl';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { JobBoardFiltered } from './JobBoard';

interface IJobFilterProps {
  departments: Department[];
  setFiltered: React.Dispatch<React.SetStateAction<JobBoardFiltered>>;
}

export default function JobFilter({ departments, setFiltered }: IJobFilterProps) {
  const t = useTranslations('career.job-list');
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [keyword, setKeyword] = useState('');
  const [_departments, setDepartments] = useState<string[]>([]);

  const debouncedSetKeyword = debounce((value: string) => {
    setFiltered((prev) => ({ ...prev, search: value }));
  }, 300);

  const onSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value);
    debouncedSetKeyword(e.target.value);
  };

  const onClearAll = () => {
    setFiltered({ departments: [], search: '' });
    setKeyword('');
    setDepartments([]);
    const nextSearchParams = new URLSearchParams(searchParams.toString());
    nextSearchParams.delete('keyword');
    router.replace(`${pathname}?${nextSearchParams}`, { scroll: false });
  };

  const onCheckDepartment = (checked: boolean, id: string) => {
    const updatedDepartments = checked
      ? [..._departments, id] // Add department if checked
      : _departments.filter((dept) => dept !== id); // Remove if unchecked
    setDepartments(updatedDepartments);
    setFiltered((prev) => ({ ...prev, departments: updatedDepartments }));
  };

  const isFiltering = (_departments?.length ?? 0) > 0 || !!keyword;

  useEffect(() => {
    const filtered = {
      search: searchParams.get('keyword') || '',
      departments: searchParams.getAll('departments') || [],
    };

    setKeyword(filtered.search);
    setDepartments(filtered.departments || []);
    setFiltered(filtered);
  }, [searchParams]);

  return (
    <CardData className='h-fit gap-[10px] text-gray-50'>
      <div className='flex min-h-12 items-center justify-between'>
        <h5 className='text-2xl leading-9 font-bold'>{t('filter')}</h5>
        {isFiltering && (
          <Button
            variant='ghost'
            className='hover:text-primary px-0 text-sm leading-[21px] font-semibold'
            onClick={onClearAll}
            aria-label='Clear all filters'
          >
            <IconCircleX />
            {t('filter-clear')}
          </Button>
        )}
      </div>
      <div className='border-background-highlight flex items-center rounded-lg border-2 pr-2'>
        <Input
          placeholder={t('search-placeholder')}
          className='h-full border-0 bg-transparent !text-gray-50 focus-visible:border-0 focus-visible:!ring-0 focus-visible:!ring-offset-0'
          value={keyword}
          onChange={onSearch}
        />
        <IconSearch className='text-gray-50' />
      </div>
      <div className='flex flex-col gap-2'>
        <h6 className='text-base leading-6 font-semibold'>{t('filter-department')}</h6>
        <div className='flex flex-col gap-2'>
          {departments.map((department) => (
            <div key={department.name} className='flex gap-2'>
              <Checkbox
                id={`${department.name}`}
                checked={_departments.includes(department.name)}
                className='data-[state=checked]:bg-primary data-[state=checked]:border-primary mt-1 bg-gray-50'
                onCheckedChange={(checked: boolean) =>
                  onCheckDepartment(checked, department.name)
                }
                aria-label={`${department.department_name}`}
              />
              <label htmlFor={`${department.name}`} className='text-base leading-6'>
                {department.department_name}
              </label>
            </div>
          ))}
        </div>
      </div>
    </CardData>
  );
}
