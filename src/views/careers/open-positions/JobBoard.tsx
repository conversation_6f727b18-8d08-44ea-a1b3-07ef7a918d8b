'use client';
import { Card, CardContent, CardHeader } from '@/components/common/Card';
import { SlashDiv } from '@/components/common/SlashBackground';
import NoDataIcon from '@/components/icons/NoDataIcon';
import { Department } from '@/types/department';
import { Job } from '@/types/job';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import JobCardItem from '../components/JobCardItem';
import JobListPagination from '../components/JobListPagination';
import JobFilter from './JobFilter';

export type JobBoardFiltered = {
  search: string;
  departments: string[];
};

interface IJobBoardViewProps {
  jobs: Job[];
  departments: Department[];
}

const ITEMS_PER_PAGE = 15;

export default function JobBoardView({ jobs, departments }: IJobBoardViewProps) {
  const t = useTranslations('career');

  const searchParams = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [jobList, setJobList] = useState<Job[]>(jobs);
  const [filtered, setFiltered] = useState<JobBoardFiltered>({
    search: '',
    departments: [] as string[],
  });

  const totalPages = Math.ceil(jobList.length / ITEMS_PER_PAGE);

  const paginatedJobs = jobList.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handleScrollToJobBoard = () => {
    const element = document.getElementById('job-board');
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 120, // Adjust the offset as needed
        behavior: 'smooth',
      });
    }
  };

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
      handleScrollToJobBoard();
    }
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const filtered = {
        departments: searchParams.getAll('departments') || [],
        search: searchParams.get('keyword') || '',
      };

      setFiltered(filtered);
    }
  }, [searchParams]);

  useEffect(() => {
    let result = [...jobs];
    setCurrentPage(1);

    if (filtered.departments && filtered.departments.length > 0) {
      result = result.filter((job) => filtered.departments.includes(job.department));
    }

    if (filtered.search) {
      result = result.filter(
        (job) =>
          filtered.search &&
          job.job_title.toLowerCase().includes(filtered.search.toLowerCase())
      );
    }

    setJobList(result);
  }, [filtered, jobs]);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (typeof window !== 'undefined') {
      timer = setTimeout(() => {
        handleScrollToJobBoard();
      }, 300);
    }
    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div id='job-board'>
      <SlashDiv h={2000} className='container mx-auto mt-20 lg:pb-20'>
        <Card className='relative z-50 gap-6 rounded-sm px-4 py-8 lg:gap-12 lg:px-16 lg:py-12 xl:px-32 xl:py-24'>
          <div className='absolute inset-0 -z-10 backdrop-blur-sm' />
          <CardHeader className='!px-0'>
            <h3 className='w-full text-center text-[clamp(2rem,1.7573rem+1.0356vw,3rem)] leading-[110%] font-semibold md:text-left'>
              {t.rich('job-list.title', {
                span: (children: React.ReactNode) => (
                  <span className='text-primary'>{children}</span>
                ),
              })}
            </h3>
          </CardHeader>
          <CardContent className='px-0'>
            <div className='grid grid-cols-1 items-start gap-6 xl:grid-cols-4'>
              <JobFilter departments={departments} setFiltered={setFiltered} />

              {paginatedJobs.length > 0 ? (
                <div className='grid w-full grid-cols-1 gap-4 md:grid-cols-2 xl:col-span-3 xl:grid-cols-3'>
                  {paginatedJobs.map((job) => (
                    <JobCardItem key={job.name} job={job} />
                  ))}
                </div>
              ) : (
                <div className='flex h-full flex-col items-center justify-center gap-5 text-gray-600 xl:col-span-3'>
                  <NoDataIcon className='w-48' />
                  <p className='text-center text-xl'>{t('job-list.no-jobs')}</p>
                </div>
              )}
            </div>
            {paginatedJobs.length > 0 && (
              <JobListPagination
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={handlePageChange}
              />
            )}
          </CardContent>
        </Card>
      </SlashDiv>
    </div>
  );
}
