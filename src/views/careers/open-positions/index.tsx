import { getAllDepartments, getAllOpeningJobs } from '@/services/erp';
import { Department } from '@/types/department';
import { Job } from '@/types/job';
import CompanyPartner from '../components/CompanyPartner';
import HeroSection from '../components/HeroSection';
import JobBoardView from './JobBoard';
import { getPartners } from '@/services/directus';

export default async function OpenPositionsView() {
  const [partners, res, departmentsRes] = await Promise.all([
    getPartners(),
    getAllOpeningJobs(),
    getAllDepartments(),
  ]);

  const jobRes: Job[] = res?.data || [];
  const departmentNames = departmentsRes?.data || [];

  const departments: Department[] = Array.from(
    new Set(jobRes.map((job) => job.department))
  ).map((department) => ({
    name: department,
    department_name:
      departmentNames.find((d) => d.name === department)?.department_name || department,
  }));

  const jobs = jobRes.map<Job>((job) => {
    const department_name =
      departments.find((d) => d.name === job.department)?.department_name ||
      job.department;

    return { ...job, department_name };
  });

  return (
    <section className='bg-background relative z-10 h-full w-full'>
      <HeroSection sectionKey='jobs' />
      <CompanyPartner partners={partners?.data} />
      <JobBoardView jobs={jobs} departments={departments} />
    </section>
  );
}
