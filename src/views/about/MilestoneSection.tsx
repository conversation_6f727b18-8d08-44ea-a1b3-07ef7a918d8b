'use client';
import { CarouselButton } from '@/components/common/CarouselButton';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { ScrollMotionDiv } from '@/components/motion/ScrollMotionDiv';
import ScrollReveal from '@/components/motion/ScrollReveal';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { cn } from '@/utils/cn';
import {
  animate,
  AnimatePresence,
  AnimationPlaybackControlsWithThen,
  motion,
  useMotionValue,
  useMotionValueEvent,
  useTransform,
} from 'framer-motion';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Autoplay, Navigation, Pagination } from 'swiper/modules';
import { Swiper, SwiperRef, SwiperSlide } from 'swiper/react';
import DotIcon from '../home/<USER>/DotIcon';
import GalleryDialog from '@/components/layout/GalleryDialog';
export default function MilestoneSection() {
  const t = useTranslations('about');
  const [currentMilestone, setCurrentMilestone] = useState(1);
  const [isChangeMilestone, setisChangeMilestone] = useState(true);
  const features = [
    {
      tag: '2024',
      title: t('every_journey_starts_somewhere'),
      description: t('every_journey_starts_somewhere_description'),
      assets: [
        '/home/<USER>/1.webp',
        '/home/<USER>/2.webp',
        '/home/<USER>/3.webp',
        '/home/<USER>/4.webp',
        '/home/<USER>/5.webp',
        '/home/<USER>/6.webp',
        '/home/<USER>/7.webp',
      ],
    },
    {
      tag: t('early_2025'),
      title: t('growing_faster_than_we_imagined'),
      description: t('growing_faster_than_we_imagined_description'),
      assets: [
        '/home/<USER>/8.webp',
        '/home/<USER>/9.webp',
        '/home/<USER>/10.webp',
        '/home/<USER>/11.webp',
        '/home/<USER>/12.webp',
        '/home/<USER>/13.webp',
        '/home/<USER>/14.webp',
      ],
    },
    {
      tag: t('mid_2025'),
      title: t('expanding_our_reach'),
      description: t('expanding_our_reach_description'),
      assets: [
        '/home/<USER>/15.webp',
        '/home/<USER>/16.webp',
        '/home/<USER>/17.webp',
        '/home/<USER>/18.webp',
        '/home/<USER>/19.webp',
        '/home/<USER>/20.webp',
        '/home/<USER>/21.webp',
      ],
    },
    {
      tag: t('2025_and_beyond'),
      title: t('ready_to_scale'),
      description: t('ready_to_scale_description'),
      assets: [
        '/home/<USER>/22.webp',
        '/home/<USER>/23.webp',
        '/home/<USER>/24.webp',
        '/home/<USER>/25.webp',
        '/home/<USER>/26.webp',
        '/home/<USER>/27.webp',
      ],
    },
  ];
  useEffect(() => {
    setisChangeMilestone(true);
    const timer = setTimeout(() => {
      setisChangeMilestone(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [currentMilestone]);

  const count = useMotionValue(0);

  const rounded = useTransform(
    count,
    (latest: number) =>
      `${latest + 100 / features.length / 2 < 100 ? latest + 100 / features.length / 2 : 100}%`
  );

  useMotionValueEvent(count, 'change', (latest) => {
    setCurrentMilestone(
      Math.min(Math.floor(latest / (100 / features.length)), features.length - 1)
    );
  });

  const controls = useRef<AnimationPlaybackControlsWithThen | null>(null);

  const startLoop = (from = 0) => {
    controls.current?.stop();
    const duration = 50 - (from / 100) * 50;
    controls.current = animate(from, 100, {
      duration: duration,
      ease: 'linear',
      onUpdate: (value) => count.set(value),
      onComplete: () => {
        jumpToNumber(0, 0.5);
      },
    });
  };

  const jumpToNumber = (targetNumber: number, duration?: number) => {
    controls.current?.stop();

    const fromValue = count.get();

    controls.current = animate(fromValue, targetNumber, {
      duration: duration || 1,
      ease: 'easeOut',
      onUpdate: (value) => count.set(value),
      onComplete: () => {
        startLoop(targetNumber);
      },
    });
  };
  const handleChangeMilestone = (index: number) => {
    jumpToNumber(index * (100 / features.length), 0.2);
  };
  useEffect(() => {
    startLoop();
    return () => controls.current?.stop();
  }, []);
  const { width } = useWindowDimensions();

  return (
    <Section center className='max-md:-mb-20 md:pb-40 lg:pb-60'>
      <HeroTitle
        tag={t('when_we_were_born')}
        title={t('our_company_milestone')}
        description={t('wow_what_a_journey_so_far')}
      />

      <motion.div
        animate={{
          x: width && width < 1024 ? (-currentMilestone * width) / 2 - 16 + width / 4 : 0,
        }}
        transition={{
          duration: 0.5,
          ease: 'easeInOut',
        }}
        style={{
          width: `${((width ? width : 1500) * features.length) / 2}px`,
        }}
        className='relative z-10 mt-5 grid grid-cols-4 max-lg:self-start lg:container lg:w-full'
      >
        {features.map((_, index) => (
          <MilestoneItem
            content={_.tag}
            key={index}
            index={index}
            currentMilestone={currentMilestone}
            handleChangeMilestone={handleChangeMilestone}
          />
        ))}
        <motion.div
          className='bg-primary absolute right-0 bottom-2 left-0 z-10 h-2 rounded-full'
          style={{
            width: rounded,
          }}
        ></motion.div>
        <motion.div
          className='absolute right-0 bottom-2 left-0 z-10 h-2 rounded-full bg-[#D9D9D9]/10'
          animate={{
            width: '100%',
          }}
        ></motion.div>
      </motion.div>
      <motion.div className='z-20 mt-10 flex w-full items-end justify-end gap-2 lg:hidden'>
        <CarouselButton
          direction='left'
          onClick={() => {
            handleChangeMilestone(
              currentMilestone === 0 ? features.length - 1 : currentMilestone - 1
            );
          }}
        />
        <CarouselButton
          direction='right'
          onClick={() => {
            handleChangeMilestone(
              currentMilestone === features.length - 1 ? 0 : currentMilestone + 1
            );
          }}
        />
      </motion.div>
      <div className='relative mt-16 flex w-full flex-col items-center'>
        <AnimatePresence mode='wait'>
          <motion.div
            key={`milestone-${currentMilestone}`}
            initial={{ opacity: 0, filter: 'blur(10px)' }}
            animate={{ opacity: 1, filter: 'blur(0px)' }}
            exit={{ opacity: 0, filter: 'blur(10px)' }}
            className='absolute inset-0 -top-60 flex w-full items-center justify-center will-change-transform md:-top-32'
          >
            <ScrollMotionDiv
              transformFrom={[0, 0.5]}
              transformTo={[-80, 0]}
              className='container flex'
              style={{
                justifyContent: !isChangeMilestone ? 'flex-end' : 'flex-start',
              }}
            >
              <motion.div
                key={`shadow-${currentMilestone}`}
                transition={{
                  duration: 10,
                  ease: 'easeInOut',
                }}
                layout
                className='text-[300px] font-bold text-nowrap opacity-10 lg:text-[480px]'
                style={{
                  background: 'linear-gradient(180deg, #FFFFFF 0%, transparent 70%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                }}
              >
                {features[currentMilestone].tag}
              </motion.div>
            </ScrollMotionDiv>
          </motion.div>
        </AnimatePresence>
        <MilestoneSlider
          assets={features[currentMilestone].assets}
          currentMilestone={currentMilestone}
          tag={features[currentMilestone].tag}
          title={features[currentMilestone].title}
          description={features[currentMilestone].description}
        />
      </div>
    </Section>
  );
}
const MilestoneItem = ({
  index,
  currentMilestone,
  handleChangeMilestone,
  content,
}: {
  index: number;
  currentMilestone: number;
  handleChangeMilestone: (index: number) => void;
  content: string;
}) => {
  const [isHover, setIsHover] = useState(false);
  return (
    <div
      onClick={() => handleChangeMilestone(index)}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      className='relative z-20 flex h-[200px] w-full cursor-pointer flex-col-reverse items-center justify-start'
    >
      <div className='absolute -bottom-7 z-0 flex w-full items-center justify-center'>
        <motion.div
          className='size-20 bg-blue-800'
          animate={{
            opacity: currentMilestone === index || isHover ? 1 : 0,
          }}
          style={{
            background: `radial-gradient(circle, var(--color-blue-900) 0%, transparent 40%, transparent 100%)`,
          }}
        />
      </div>
      <motion.div
        className='border-primary z-10 size-6 rounded-full border-1 bg-white'
        animate={{
          borderColor:
            currentMilestone !== index || !isHover
              ? 'var(--color-primary)'
              : 'var(--color-blue-850)',
        }}
      ></motion.div>
      <motion.div className='mt-2 h-10 w-[1px] bg-white'></motion.div>
      <motion.div
        animate={{
          fontSize: currentMilestone === index ? 24 : 14,
          color:
            currentMilestone === index ? 'var(--color-white)' : 'var(--color-gray-700)',
          borderRadius: currentMilestone === index ? 8 : 4,
          borderColor:
            currentMilestone === index || isHover
              ? 'var(--color-primary)'
              : 'var(--color-gray-700)',
        }}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 10,
        }}
        className={cn(
          'flex items-center justify-center border text-center font-bold transition-[padding] duration-300 will-change-transform',
          currentMilestone === index ? 'p-2 md:p-4' : 'p-2 md:p-2'
        )}
      >
        <div>{content}</div>
      </motion.div>
    </div>
  );
};

export function MilestoneSlider({
  currentMilestone,
  tag,
  title,
  description,
  assets,
}: {
  currentMilestone: number;
  tag: string;
  title: string;
  description: string;
  assets: string[];
}) {
  const [openIndex, setOpenIndex] = useState('');

  const sliderRef = useRef<SwiperRef>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const handlePrev = useCallback(() => {
    if (!sliderRef.current?.swiper) return;
    sliderRef.current.swiper.slidePrev();
  }, []);

  const handleNext = useCallback(() => {
    if (!sliderRef.current?.swiper) return;
    sliderRef.current.swiper.slideNext();
  }, []);
  return (
    <>
      <GalleryDialog
        type='image'
        src={openIndex}
        isOpen={openIndex !== ''}
        onOpenChange={(isOpen: boolean) => {
          if (!isOpen) setOpenIndex('');
        }}
      />
      <ScrollReveal className='relative container flex items-center sm:mt-6 md:mt-20 md:h-[330px] lg:mt-40'>
        <div className='mt-40 hidden scale-50 sm:scale-60 md:block md:scale-80 lg:scale-100'>
          <DotIcon indexNumber={currentIndex} />
        </div>
        <AnimatePresence mode='wait'>
          <motion.div
            key={`milestone-${currentMilestone}`}
            initial={{ opacity: 0, filter: 'blur(10px)' }}
            animate={{ opacity: 1, filter: 'blur(0px)' }}
            exit={{ opacity: 0, filter: 'blur(10px)' }}
            className='z-[1] flex w-screen flex-col gap-6 max-md:mt-10 md:absolute md:top-1/2 md:left-28 md:-translate-y-1/2 md:gap-10 lg:left-40 lg:gap-16'
          >
            <HeroTitle
              tag={tag}
              title={title}
              description={description}
              className='items-start justify-start pr-4 text-start'
            />
            <div className='relative flex h-[330px] w-screen items-center'>
              <Swiper
                modules={[Pagination, Navigation, Autoplay]}
                ref={sliderRef}
                speed={1000}
                slidesPerView={'auto'}
                spaceBetween={25}
                slidesPerGroup={1}
                loop={true}
                autoplay={{ delay: 3000 }}
                className='px-20'
                onRealIndexChange={(swiper) => setCurrentIndex(swiper.realIndex)}
                onInit={(swiper) => setCurrentIndex(swiper.realIndex)}
                onReachEnd={(swiper) => {
                  setTimeout(() => {
                    swiper.slideNext(300);
                  }, 500);
                }}
                breakpoints={{
                  1024: {
                    slidesPerGroup: 2,
                  },
                }}
              >
                {[...assets, ...assets].map((asset, index) => (
                  <SwiperSlide key={index} className='!w-fit'>
                    <div
                      onClick={() => setOpenIndex(asset)}
                      className='aspect-[588/330] w-[340px] cursor-pointer bg-blue-400 md:w-[500px] lg:w-[588px]'
                    >
                      <motion.img src={asset} alt='' className='size-full object-cover' />
                    </div>
                  </SwiperSlide>
                ))}
                <div className='flex gap-2 py-6 pt-2'>
                  <CarouselButton direction='left' onClick={handlePrev} />
                  <CarouselButton direction='right' onClick={handleNext} />
                </div>
              </Swiper>
            </div>
          </motion.div>
        </AnimatePresence>
      </ScrollReveal>
    </>
  );
}
