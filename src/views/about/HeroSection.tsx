'use client';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { AuroraBackground } from '@/components/layout/AuroraBackground';
import { ScrollMotionDiv } from '@/components/motion/ScrollMotionDiv';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useTranslations } from 'next-intl';

export default function HeroSection() {
  const t = useTranslations('about');
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0.5, 1], [35, 100]);
  const maskImage = useTransform(
    y,
    (latest) =>
      `linear-gradient(to top, transparent 0%, transparent ${latest}%,  black 60%)`
  );
  return (
    <Section className='flex min-h-[620px]'>
      <AuroraBackground>
        <HeroTitle
          className='z-10 mt-32 md:mt-52'
          tag={t('who_we_are')}
          title={t('innovation_at_our_core')}
          description={t('about_description')}
          descriptionClassName=' max-w-[900px]'
        />
        <ScrollMotionDiv
          transformFrom={[0.5, 1]}
          transformTo={[0, 150]}
          className='absolute top-20 h-full w-full bg-[#00000000] opacity-30'
          style={{
            maskImage: maskImage as any,
          }}
          transition={{
            duration: 0.5,
          }}
        >
          <motion.img src='/logo-3d.webp' alt='' className='size-full object-contain' />
        </ScrollMotionDiv>
      </AuroraBackground>
    </Section>
  );
}
