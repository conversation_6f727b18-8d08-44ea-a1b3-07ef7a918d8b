'use client';
import BlurShine from '@/components/common/BlurShine';
import { Glasscard } from '@/components/common/Card';
import CircleRounding from '@/components/common/CircleRounding';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { SlashBackground2 } from '@/components/common/SlashBackground';
import Tag from '@/components/common/Tag';
import { cn } from '@/utils/cn';
import { AnimatePresence, motion, useInView } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';
import ClientFocusIcon from './icons/ClientFocusIcon';
import ExcellenceIcon from './icons/ExcellenceIcon';
import InnovationIcon from './icons/InnovationIcon';
import IntegrityIcon from './icons/IntegrityIcon';
export default function OurCultureValueSection() {
  const t = useTranslations('about');
  const [isHovered, setIsHovered] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const features = [
    {
      title: t('excellence'),
      subtitle: t('excellence_subtitle'),
      description: t('excellence_description'),
      icon: <ExcellenceIcon animate={true} />,
      subicon: '/about/core-values/excellence-icon.png',
    },
    {
      title: t('integrity'),
      subtitle: t('integrity_subtitle'),
      description: t('integrity_description'),
      icon: <IntegrityIcon animate={true} />,
      subicon: '/about/core-values/integrity-icon.png',
    },
    {
      title: t('innovation'),
      subtitle: t('innovation_subtitle'),
      description: t('innovation_description'),
      icon: <InnovationIcon animate={true} />,
      subicon: '/about/core-values/innovation-icon.png',
    },
    {
      title: t('client_focus'),
      subtitle: t('client_focus_subtitle'),
      description: t('client_focus_description'),
      icon: <ClientFocusIcon animate={true} />,
      subicon: '/about/core-values/client-focus-icon.png',
    },
  ];
  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);
  const [animatedNumber, setAnimatedNumber] = useState(0);

  const sectionRef = useRef<HTMLDivElement>(null);

  const isInView = useInView(sectionRef);

  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying && isInView) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, isInView]);
  useEffect(() => {
    // Reset animation when slide changes
    progressRef.current = 0;
    setAnimatedNumber(0);
  }, [activeIndex]);
  // useEffect(() => {
  //   if (Math.floor(animatedNumber) === 9) {
  //     setActiveIndex(activeIndex === features.length - 1 ? 0 : activeIndex + 1);
  //   }
  // }, [Math.floor(animatedNumber)]);

  return (
    <>
      <BlurShine
        size={1600}
        blur={300}
        direction='left'
        color='var(--color-orange-800)'
      />
      <Section
        ref={sectionRef}
        container
        center
        className='relative mt-40 flex items-center justify-center'
      >
        <SlashBackground2 h={1700} />
        <CircleRounding
          className='max-lg:absolute max-lg:inset-0 max-lg:!w-full'
          size={'70%'}
          color={['#38DCF5', '#63BDFF59']}
          clockRotate={false}
          length={2}
        ></CircleRounding>
        <div
          className='absolute inset-0 z-0 opacity-30'
          style={{
            background:
              'radial-gradient(circle, var(--color-blue-900) 0%, transparent 50%, transparent 700%)',
          }}
        ></div>
        <AnimatePresence mode='popLayout'>
          <motion.div
            key={activeIndex}
            className='absolute inset-0 hidden w-full flex-col items-center justify-center gap-15 lg:flex'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0, transition: { duration: 0.1 } }}
            transition={{
              duration: 0.3,
              ease: 'easeIn',
            }}
          >
            <div>{features[activeIndex].icon}</div>
            <HeroTitle
              title={features[activeIndex].title}
              description={features[activeIndex].description}
              tag={t('our_culture_value')}
            />
          </motion.div>
        </AnimatePresence>
        <div className='mt-10 flex w-full flex-col items-center gap-4 lg:hidden'>
          <Tag className='mb-10'>{t('our_culture_value')}</Tag>
          {features.map((feature: any, index: number) => (
            <Glasscard key={index}>
              <div className='flex flex-col gap-4 p-4'>
                <div className='size-11 rounded-xl border p-2'>
                  <img
                    src={feature.subicon}
                    alt={feature.title}
                    className='size-full object-contain'
                  />
                </div>
                <div className='text-2xl font-medium'>{feature.title}</div>
                <div className='text-sm'>{feature.subtitle}</div>
                <div className='text-sm text-white/70'>{feature.description}</div>
              </div>
            </Glasscard>
          ))}
        </div>
      </Section>
      <Section
        container
        center
        className='relative hidden items-center justify-center lg:-mt-20 lg:flex xl:-mt-40'
      >
        <div className='grid h-[300px] w-full grid-cols-4 place-items-end gap-2 lg:gap-6'>
          {features.map((feature, index) => (
            <div key={index} onClick={() => setActiveIndex(index)}>
              <Glasscard
                isForCardSection={activeIndex === index}
                className='flex w-full cursor-pointer flex-col items-start justify-end font-medium'
              >
                <div className='relative flex flex-col gap-4 p-2 lg:p-4'>
                  <motion.div
                    animate={{
                      borderColor: activeIndex === index ? '#ffffff' : '#FFFFFF1C',
                      filter: activeIndex === index ? 'grayscale(0)' : 'grayscale(1)',
                    }}
                    className='z-10 size-11 rounded-xl border p-2'
                  >
                    <img
                      src={feature.subicon}
                      alt={feature.title}
                      className='size-full object-contain'
                    />
                  </motion.div>
                  <motion.div
                    animate={{
                      opacity: activeIndex === index ? 1 : 0.4,
                    }}
                    transition={{
                      duration: 0.3,
                      ease: 'easeIn',
                    }}
                    className={cn(
                      'z-10 whitespace-pre-wrap transition-[font-size] duration-300',
                      activeIndex === index
                        ? 'lg:text-3xl xl:text-[40px]'
                        : 'lg:text-2xl xl:text-[32px]'
                    )}
                  >
                    {feature.title}
                  </motion.div>
                  <motion.div
                    className='z-10 text-sm lg:text-base xl:text-xl 2xl:text-2xl'
                    animate={{
                      opacity: activeIndex === index ? 1 : 0.4,
                    }}
                    transition={{
                      duration: 0.3,
                      ease: 'easeIn',
                    }}
                  >
                    {feature.subtitle}
                  </motion.div>
                  <motion.div
                    animate={{
                      opacity: activeIndex === index ? 1 : 0,
                    }}
                    transition={{
                      duration: 0.3,
                      ease: 'easeIn',
                    }}
                    className='absolute inset-0 z-0 rounded-b-2xl'
                    style={{
                      background:
                        'radial-gradient(100% 100% at 50% 0%, rgba(0, 64, 128, 0) 0%, #0068FF 100%)',
                    }}
                  ></motion.div>
                </div>
              </Glasscard>
            </div>
          ))}
        </div>
      </Section>
    </>
  );
}

// epxort const
