'use client';
import { CarouselButton } from '@/components/common/CarouselButton';
import { useEmblaCarouselButtons } from '@/hooks/useEmblaCarouselButtons';
import { cn } from '@/utils/cn';
import useEmblaCarousel from 'embla-carousel-react';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';

interface CarouselPaneProps {
  slides: Array<{
    title: string;
    description: string;
  }>;
}

export default function CarouselPane({ slides }: CarouselPaneProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    axis: 'y',
    align: 'center',
    startIndex: 1,
  });
  const [isAutoplaying, setIsAutoplaying] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [_, setProgress] = useState(0);
  const { selectedIndex, onPrevButtonClick, onNextButtonClick } =
    useEmblaCarouselButtons(emblaApi);

  useEffect(() => {
    if (isAutoplaying) {
      intervalRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 2;
          if (newProgress >= 100) {
            setTimeout(() => onNextButtonClick(), 0);
            return 0;
          }
          return newProgress;
        });
      }, 100);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAutoplaying, onNextButtonClick]);

  useEffect(() => {
    setProgress(0);
  }, [selectedIndex]);

  return (
    <section className='relative z-50 flex h-full w-full flex-col lg:flex-row lg:items-center'>
      <div className='relative'>
        <motion.div className='absolute top-1/2 left-0 z-30 flex h-full -translate-y-1/2 flex-col items-center justify-center gap-4'>
          <CarouselButton direction='top' onClick={onNextButtonClick} />
          <CarouselButton direction='bottom' onClick={onPrevButtonClick} />
        </motion.div>
        <div
          className='h-80 shrink-0 overflow-hidden pl-14 md:h-[512px] xl:pl-[104px]'
          ref={emblaRef}
          style={{
            maskImage: `linear-gradient(0deg,rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 1) 40%, rgba(0, 0, 0, 1) 60%, rgba(255, 255, 255, 0) 100%)`,
          }}
        >
          <div className='flex h-full w-full flex-col xl:w-[590px]'>
            {slides.map((item) => {
              return (
                <motion.div
                  key={item.title}
                  className={cn(
                    'flex h-full w-full items-center justify-start pt-5 md:pt-10'
                  )}
                  onMouseEnter={() => setIsAutoplaying(false)}
                  onMouseLeave={() => setIsAutoplaying(true)}
                  onFocus={() => setIsAutoplaying(false)}
                >
                  <h6
                    className={cn(
                      'text-7xl leading-none font-medium tracking-tight whitespace-pre-line text-gray-50 md:text-9xl',
                      'transition-opacity duration-500'
                    )}
                  >
                    {item.title}
                  </h6>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
      <div className='flex h-64 w-full items-center justify-center border-l-4 border-l-orange-800 bg-[linear-gradient(90deg,_rgba(13,_62,_112,_0)_0%,_rgba(13,_62,_112,_0.05)_100%),linear-gradient(270deg,_rgba(255,_131,_0,_0)_63.76%,_rgba(255,_131,_0,_0.2)_100%)] px-8 backdrop-blur-sm xl:h-[300px] xl:px-14'>
        <AnimatePresence mode='popLayout'>
          <motion.p
            layout
            key={selectedIndex}
            className='text-xl leading-8 text-wrap text-gray-50 opacity-70 xl:text-2xl xl:leading-9'
            initial={{ x: 56, filter: 'blur(10px)', opacity: 0 }}
            animate={{ x: 0, filter: 'blur(0px)', opacity: 1 }}
            exit={{ x: -56, filter: 'blur(10px)', opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            {slides[selectedIndex].description}
          </motion.p>
        </AnimatePresence>
      </div>
    </section>
  );
}
