'use client';
import { useEmblaCarouselButtons } from '@/hooks/useEmblaCarouselButtons';
import { cn } from '@/utils/cn';
import useEmblaCarousel from 'embla-carousel-react';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';

interface CarouselPaneProps {
  slides: Array<{
    title: string;
    description: string;
  }>;
}

export default function CarouselMobilePane({ slides }: CarouselPaneProps) {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: 'start',
    startIndex: 1,
  });
  const [isAutoplaying, setIsAutoplaying] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [_, setProgress] = useState(0);
  const { selectedIndex, onNextButtonClick } = useEmblaCarouselButtons(emblaApi);

  useEffect(() => {
    if (isAutoplaying) {
      intervalRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + 2;
          if (newProgress >= 100) {
            setTimeout(() => onNextButtonClick(), 0);
            return 0;
          }
          return newProgress;
        });
      }, 100);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAutoplaying, onNextButtonClick]);

  useEffect(() => {
    setProgress(0);
  }, [selectedIndex]);

  return (
    <div className='relative flex w-full flex-col overflow-hidden'>
      <div
        className='overflow-hidden'
        ref={emblaRef}
        style={{
          maskImage: `linear-gradient(to right, rgba(255, 255, 255, 0) -25%, rgba(0, 0, 0, 1) 20%, rgba(0, 0, 0, 1) 80%, rgba(255, 255, 255, 0) 100%)`,
        }}
      >
        <div className='-ml-10 flex'>
          {slides.map((item) => {
            return (
              <motion.div
                key={item.title}
                className={cn('flex flex-[0_0_90%] pl-10')}
                onFocus={() => setIsAutoplaying(false)}
              >
                <h6
                  className={cn(
                    'text-6xl leading-none font-medium tracking-tight text-gray-50',
                    'transition-opacity duration-500'
                  )}
                >
                  {item.title}
                </h6>
              </motion.div>
            );
          })}
        </div>
      </div>

      <div className='mt-4 flex h-64 items-center justify-center border-l-4 border-l-orange-800 bg-[linear-gradient(90deg,_rgba(13,_62,_112,_0)_0%,_rgba(13,_62,_112,_0.05)_100%),linear-gradient(270deg,_rgba(255,_131,_0,_0)_63.76%,_rgba(255,_131,_0,_0.2)_100%)] px-4 backdrop-blur-sm'>
        <AnimatePresence mode='popLayout'>
          <motion.p
            key={selectedIndex}
            layout
            className='w-full text-lg leading-8 text-wrap text-gray-50 opacity-70'
            initial={{ x: 56, filter: 'blur(8px)', opacity: 0 }}
            animate={{ x: 0, filter: 'blur(0px)', opacity: 1 }}
            exit={{ x: -56, filter: 'blur(8px)', opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            {slides[selectedIndex].description}
          </motion.p>
        </AnimatePresence>
      </div>
    </div>
  );
}
