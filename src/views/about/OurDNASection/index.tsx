'use client';
import { Section } from '@/components/common/Section';
import { SlashDiv } from '@/components/common/SlashBackground';
import CarouselPane from './CarouselPane';
import CarouselMobilePane from './CarouselMobilePane';
import useWindowDimensions from '@/hooks/useWindowDimensions';

export default function OurDNASection() {
  const { width } = useWindowDimensions();

  const slides = [
    {
      title: 'Our\nVision',
      description:
        'To be the leading enabler of digital transformation in Vietnam and ASEAN by empowering businesses and governments with AI and emerging technologies, driving sustainable growth and competitive advantage.',
    },
    {
      title: 'Our\nMission',
      description:
        'Our mission is to become the premier innovation and technology solutions center driving digital transformation, empowering businesses and talent through cutting-edge solutions.',
    },
    {
      title: 'Our\nReach',
      description:
        'From our Vietnam innovation center to our Bangkok Silicon partnership, we deliver transformative impact across Southeast Asia through digital transformation, innovation initiatives, technical excellence, and talent development.',
    },
  ];

  const isMobile = width ? width <= 767 : false;

  return (
    <Section className='container my-40 lg:my-80'>
      <SlashDiv h={500} additionalHeight={500} className='relative z-10'>
        {isMobile ? (
          <CarouselMobilePane slides={slides} />
        ) : (
          <CarouselPane slides={slides} />
        )}
      </SlashDiv>
    </Section>
  );
}
