import { motion } from 'framer-motion';

export default function IntegrityIcon({ animate }: { animate?: boolean }) {
  return (
    <div className='relative'>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='349'
        height='334'
        viewBox='0 0 349 334'
        fill='none'
      >
        <path
          d='M174.865 216.013C173.788 216.635 172.569 217.011 171.309 217.071L130.685 219.033C119.154 219.585 109.236 218.459 100.351 215.607C80.9116 209.374 64.5982 196.384 54.4276 179.011L20.9309 121.842C17.0636 115.244 17.9305 106.818 23.0644 100.87L69.0591 47.7339C71.7736 44.5499 75.5723 42.3341 79.7237 41.4975L148.565 27.4281C156.265 25.8429 164.041 29.2223 167.895 35.8161L201.416 93.0274C211.581 110.37 214.941 130.929 210.886 150.919C209.024 160.089 205.145 169.282 199.038 179.026L177.518 213.43C176.854 214.503 175.937 215.383 174.865 216.013'
          fill='url(#paint0_linear_182_3)'
        />
        <g filter='url(#filter0_f_182_3)'>
          <path
            d='M156.904 181.951C156.173 182.373 155.359 182.649 154.529 182.733L127.779 185.45C120.186 186.218 113.715 185.897 107.987 184.477C95.4532 181.376 85.2243 174.012 79.1926 163.73L59.3252 129.894C57.0314 125.99 57.9145 120.787 61.5075 116.945L93.6906 82.6238C95.5918 80.5688 98.1689 79.0675 100.926 78.3994L146.657 67.2012C151.773 65.9416 156.753 67.7258 159.038 71.6287L178.92 105.489C184.949 115.753 186.39 128.245 182.982 140.663C181.418 146.36 178.528 152.146 174.155 158.353L158.742 180.267C158.266 180.951 157.631 181.524 156.904 181.951'
            fill='#1EA51F'
            fill-opacity='0.5'
          />
        </g>
        <foreignObject x='76.8391' y='26.5955' width='288.346' height='314.615'>
          <div
            style={{
              backdropFilter: 'blur(12px)',
              clipPath: 'url(#bgblur_0_182_3_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g data-figma-bg-blur-radius='24'>
          <mask
            id='path-3-outside-1_182_3'
            maskUnits='userSpaceOnUse'
            x='82.4094'
            y='33.5693'
            width='268.369'
            height='301.824'
            fill='black'
          >
            <rect fill='white' x='82.4094' y='33.5693' width='268.369' height='301.824' />
            <path d='M197.018 316.102C195.383 315.85 193.807 315.224 192.449 314.233L148.705 282.321C136.294 273.258 126.95 263.793 120.117 253.389C105.16 230.634 99.0729 203.44 103.006 176.807L115.917 89.1425C117.405 79.0246 125.324 70.9556 135.602 69.0388L227.551 52.0338C233.018 50.98 238.801 51.843 243.802 54.4403L326.883 97.3041C336.186 102.087 341.43 112.114 339.931 122.217L327.01 209.946C323.097 236.542 309.449 260.807 288.591 278.279C279.021 286.293 267.34 292.644 252.892 297.709L201.92 315.624C200.337 316.188 198.654 316.34 197.018 316.102' />
          </mask>
          <path
            d='M197.018 316.102C195.383 315.85 193.807 315.224 192.449 314.233L148.705 282.321C136.294 273.258 126.95 263.793 120.117 253.389C105.16 230.634 99.0729 203.44 103.006 176.807L115.917 89.1425C117.405 79.0246 125.324 70.9556 135.602 69.0388L227.551 52.0338C233.018 50.98 238.801 51.843 243.802 54.4403L326.883 97.3041C336.186 102.087 341.43 112.114 339.931 122.217L327.01 209.946C323.097 236.542 309.449 260.807 288.591 278.279C279.021 286.293 267.34 292.644 252.892 297.709L201.92 315.624C200.337 316.188 198.654 316.34 197.018 316.102'
            fill='#72DC60'
            fill-opacity='0.35'
          />
          <path
            d='M196.866 317.09C197.411 317.174 197.922 316.8 198.006 316.254C198.09 315.708 197.715 315.197 197.17 315.113L197.018 316.102L196.866 317.09ZM192.449 314.233L193.039 313.426L193.039 313.425L192.449 314.233ZM148.705 282.321L148.115 283.129L148.116 283.129L148.705 282.321ZM120.117 253.389L120.953 252.84L120.953 252.84L120.117 253.389ZM103.006 176.807L103.995 176.953L103.995 176.952L103.006 176.807ZM115.917 89.1425L116.906 89.2882L116.906 89.288L115.917 89.1425ZM135.602 69.0388L135.42 68.0555L135.419 68.0558L135.602 69.0388ZM227.551 52.0338L227.733 53.0171L227.74 53.0157L227.551 52.0338ZM243.802 54.4403L243.341 55.3277L243.343 55.329L243.802 54.4403ZM326.883 97.3041L326.425 98.1928L326.426 98.1935L326.883 97.3041ZM339.931 122.217L338.942 122.07L338.942 122.071L339.931 122.217ZM327.01 209.946L326.021 209.8L326.021 209.8L327.01 209.946ZM288.591 278.279L289.233 279.046L289.233 279.046L288.591 278.279ZM252.892 297.709L252.561 296.765L252.56 296.765L252.892 297.709ZM201.92 315.624L201.588 314.68L201.584 314.682L201.92 315.624ZM197.162 315.112C196.615 315.033 196.108 315.411 196.028 315.958C195.948 316.504 196.327 317.012 196.873 317.091L197.018 316.102L197.162 315.112ZM197.018 316.102L197.17 315.113C195.685 314.885 194.261 314.317 193.039 313.426L192.449 314.233L191.86 315.041C193.353 316.131 195.081 316.816 196.866 317.09L197.018 316.102ZM192.449 314.233L193.039 313.425L149.294 281.513L148.705 282.321L148.116 283.129L191.86 315.041L192.449 314.233ZM148.705 282.321L149.295 281.514C136.959 272.506 127.707 263.124 120.953 252.84L120.117 253.389L119.281 253.938C126.193 264.462 135.628 274.011 148.115 283.129L148.705 282.321ZM120.117 253.389L120.953 252.84C106.128 230.286 100.099 203.34 103.995 176.953L103.006 176.807L102.016 176.661C98.0473 203.54 104.191 230.981 119.281 253.938L120.117 253.389ZM103.006 176.807L103.995 176.952L116.906 89.2882L115.917 89.1425L114.927 88.9968L102.016 176.661L103.006 176.807ZM115.917 89.1425L116.906 89.288C118.33 79.6073 125.913 71.863 135.785 70.0219L135.602 69.0388L135.419 68.0558C124.735 70.0481 116.48 78.4419 114.927 88.997L115.917 89.1425ZM135.602 69.0388L135.784 70.0221L227.733 53.0171L227.551 52.0338L227.369 51.0504L135.42 68.0555L135.602 69.0388ZM227.551 52.0338L227.74 53.0157C232.982 52.0054 238.537 52.8323 243.341 55.3277L243.802 54.4403L244.263 53.5529C239.066 50.8537 233.054 49.9546 227.362 51.0518L227.551 52.0338ZM243.802 54.4403L243.343 55.329L326.425 98.1928L326.883 97.3041L327.342 96.4154L244.26 53.5516L243.802 54.4403ZM326.883 97.3041L326.426 98.1935C335.359 102.786 340.376 112.404 338.942 122.07L339.931 122.217L340.92 122.364C342.484 111.824 337.012 101.387 327.34 96.4148L326.883 97.3041ZM339.931 122.217L338.942 122.071L326.021 209.8L327.01 209.946L327.999 210.091L340.92 122.362L339.931 122.217ZM327.01 209.946L326.021 209.8C322.143 236.15 308.622 260.196 287.949 277.513L288.591 278.279L289.233 279.046C310.277 261.418 324.05 236.934 328 210.091L327.01 209.946ZM288.591 278.279L287.949 277.513C278.49 285.434 266.921 291.731 252.561 296.765L252.892 297.709L253.222 298.652C267.759 293.557 279.552 287.152 289.233 279.046L288.591 278.279ZM252.892 297.709L252.56 296.765L201.588 314.68L201.92 315.624L202.251 316.567L253.223 298.652L252.892 297.709ZM201.92 315.624L201.584 314.682C200.159 315.19 198.641 315.328 197.162 315.112L197.018 316.102L196.873 317.091C198.666 317.353 200.514 317.187 202.256 316.565L201.92 315.624Z'
            fill='url(#paint1_linear_182_3)'
            mask='url(#path-3-outside-1_182_3)'
          />
        </g>
        <g
          filter='url(#filter2_d_182_3)'
          z-index='100'
          data-figma-bg-blur-radius='49.0531'
        >
          <path
            d='M202.473 213.194C199.92 212.814 197.51 211.503 195.834 209.311L174.277 181.09C170.952 176.71 171.856 170.569 176.308 167.359C180.761 164.136 187.079 165.067 190.419 169.436L205.931 189.733L257.116 152.747C261.583 149.526 267.9 150.458 271.228 154.825C274.566 159.207 273.661 165.36 269.211 168.558L209.946 211.389C207.711 213.006 205.026 213.573 202.473 213.194Z'
            fill='url(#paint2_linear_182_3)'
          />
          <path
            d='M257.308 153.012C261.632 149.895 267.748 150.798 270.967 155.023C274.094 159.127 273.37 164.833 269.413 167.994L269.02 168.292L209.754 211.124C207.592 212.688 204.994 213.238 202.521 212.87C200.046 212.502 197.715 211.232 196.094 209.112L174.538 180.891C171.323 176.657 172.197 170.726 176.499 167.624L176.499 167.623C180.809 164.504 186.926 165.407 190.159 169.635L205.672 189.932L205.865 190.185L206.123 189.999L257.308 153.012Z'
            stroke='url(#paint3_linear_182_3)'
            stroke-opacity='0.5'
            stroke-width='0.654041'
          />
        </g>
        <defs>
          <filter
            id='filter0_f_182_3'
            x='5.73692'
            y='14.4784'
            width='231.376'
            height='223.701'
            filterUnits='userSpaceOnUse'
            color-interpolation-filters='sRGB'
          >
            <feFlood flood-opacity='0' result='BackgroundImageFix' />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='BackgroundImageFix'
              result='shape'
            />
            <feGaussianBlur
              stdDeviation='26.1616'
              result='effect1_foregroundBlur_182_3'
            />
          </filter>
          <clipPath
            id='bgblur_0_182_3_clip_path'
            transform='translate(-76.8391 -26.5955)'
          >
            <path d='M197.018 316.102C195.383 315.85 193.807 315.224 192.449 314.233L148.705 282.321C136.294 273.258 126.95 263.793 120.117 253.389C105.16 230.634 99.0729 203.44 103.006 176.807L115.917 89.1425C117.405 79.0246 125.324 70.9556 135.602 69.0388L227.551 52.0338C233.018 50.98 238.801 51.843 243.802 54.4403L326.883 97.3041C336.186 102.087 341.43 112.114 339.931 122.217L327.01 209.946C323.097 236.542 309.449 260.807 288.591 278.279C279.021 286.293 267.34 292.644 252.892 297.709L201.92 315.624C200.337 316.188 198.654 316.34 197.018 316.102' />
          </clipPath>
          <filter
            id='filter2_d_182_3'
            x='123.22'
            y='101.776'
            width='199.075'
            height='160.584'
            filterUnits='userSpaceOnUse'
            color-interpolation-filters='sRGB'
          >
            <feFlood flood-opacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='16.351' dy='16.351' />
            <feGaussianBlur stdDeviation='16.351' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 0.454902 0 0 0 0 0.870588 0 0 0 0 0.376471 0 0 0 0.5 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_182_3'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_182_3'
              result='shape'
            />
          </filter>
          <clipPath id='bgblur_1_182_3_clip_path' transform='translate(-123.22 -101.776)'>
            <path d='M202.473 213.194C199.92 212.814 197.51 211.503 195.834 209.311L174.277 181.09C170.952 176.71 171.856 170.569 176.308 167.359C180.761 164.136 187.079 165.067 190.419 169.436L205.931 189.733L257.116 152.747C261.583 149.526 267.9 150.458 271.228 154.825C274.566 159.207 273.661 165.36 269.211 168.558L209.946 211.389C207.711 213.006 205.026 213.573 202.473 213.194Z' />
          </clipPath>
          <linearGradient
            id='paint0_linear_182_3'
            x1='148.183'
            y1='0.820694'
            x2='95.9935'
            y2='195.506'
            gradientUnits='userSpaceOnUse'
          >
            <stop stop-color='#9BF763' />
            <stop offset='1' stop-color='#26AB5B' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_182_3'
            x1='155.274'
            y1='70.9546'
            x2='299.443'
            y2='278.504'
            gradientUnits='userSpaceOnUse'
          >
            <stop stop-color='white' stopOpacity='0.25' />
            <stop offset='1' stop-color='white' stopOpacity='0' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_182_3'
            x1='265.822'
            y1='164.095'
            x2='157.482'
            y2='152.383'
            gradientUnits='userSpaceOnUse'
          >
            <stop stop-color='white' />
            <stop offset='1' stop-color='white' stopOpacity='0.2' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_182_3'
            x1='184.735'
            y1='162.746'
            x2='266.736'
            y2='175.997'
            gradientUnits='userSpaceOnUse'
          >
            <stop stop-color='white' />
            <stop offset='1' stop-color='white' stopOpacity='0' />
          </linearGradient>
        </defs>
      </svg>

      <div className='absolute top-[134px] right-[26px] z-10'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='168'
          height='129'
          viewBox='0 0 168 129'
          fill='none'
        >
          <g filter='url(#filter0_d_182_9)' data-figma-bg-blur-radius='49.0531'>
            <path
              d='M47.4729 79.1938C44.9198 78.8144 42.51 77.5032 40.8341 75.3112L19.2775 47.0895C15.9523 42.7097 16.8559 36.5691 21.3076 33.3588C25.7613 30.1356 32.0786 31.0674 35.4191 35.4363L50.9314 55.7334L102.116 18.7473C106.583 15.5261 112.9 16.458 116.228 20.8249C119.566 25.2067 118.661 31.3603 114.211 34.5576L54.9456 77.3887C52.7111 79.0058 50.026 79.5732 47.4729 79.1938Z'
              fill='url(#paint0_linear_182_9)'
            />
            <path
              d='M102.308 19.0123C106.632 15.8947 112.748 16.798 115.967 21.0232C119.094 25.1273 118.37 30.8333 114.413 33.9943L114.02 34.2924L54.7538 77.1241C52.5923 78.6882 49.9938 79.2376 47.5205 78.8701C45.0462 78.5023 42.7151 77.2322 41.0942 75.1123L19.5378 46.8914C16.3233 42.6571 17.1975 36.7263 21.4992 33.6242L21.4993 33.6233C25.8091 30.5043 31.9259 31.4069 35.1589 35.6351L50.6718 55.9322L50.8651 56.1852L51.123 55.9986L102.308 19.0123Z'
              stroke='url(#paint1_linear_182_9)'
              stroke-opacity='0.5'
              stroke-width='0.654041'
            />
          </g>
          <defs>
            <filter
              id='filter0_d_182_9'
              x='-31.7801'
              y='-32.2237'
              width='199.075'
              height='160.584'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood flood-opacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='16.351' dy='16.351' />
              <feGaussianBlur stdDeviation='16.351' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 0.454902 0 0 0 0 0.870588 0 0 0 0 0.376471 0 0 0 0.5 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_182_9'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_182_9'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_0_182_9_clip_path'
              transform='translate(31.7801 32.2237)'
            >
              <path d='M47.4729 79.1938C44.9198 78.8144 42.51 77.5032 40.8341 75.3112L19.2775 47.0895C15.9523 42.7097 16.8559 36.5691 21.3076 33.3588C25.7613 30.1356 32.0786 31.0674 35.4191 35.4363L50.9314 55.7334L102.116 18.7473C106.583 15.5261 112.9 16.458 116.228 20.8249C119.566 25.2067 118.661 31.3603 114.211 34.5576L54.9456 77.3887C52.7111 79.0058 50.026 79.5732 47.4729 79.1938Z' />
            </clipPath>
            <linearGradient
              id='paint0_linear_182_9'
              x1='110.822'
              y1='30.0954'
              x2='2.48231'
              y2='18.3831'
              gradientUnits='userSpaceOnUse'
            >
              <stop stop-color='white' />
              <stop offset='1' stop-color='white' stopOpacity='0.2' />
            </linearGradient>
            <linearGradient
              id='paint1_linear_182_9'
              x1='29.7353'
              y1='28.7459'
              x2='111.736'
              y2='41.9966'
              gradientUnits='userSpaceOnUse'
            >
              <stop stop-color='white' />
              <stop offset='1' stop-color='white' stopOpacity='0' />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
}
