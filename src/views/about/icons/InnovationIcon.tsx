import { motion } from 'framer-motion';

export default function InnovationIcon({ animate }: { animate?: boolean }) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='400'
      height='400'
      viewBox='0 0 400 400'
      fill='none'
    >
      <path
        d='M120.874 193.561C120.874 193.561 85.0069 213.513 85.007 239.641L85.0072 278.121C84.9897 279.842 85.3156 281.548 85.9659 283.141C86.6162 284.734 87.5779 286.181 88.7946 287.398C90.0112 288.615 91.4582 289.576 93.0511 290.227C94.644 290.877 96.3508 291.203 98.0713 291.185L121.824 291.185L120.874 193.561Z'
        fill='url(#paint0_linear_1762_9062)'
      />
      <path
        d='M208.057 93.3956C205.813 91.41 202.915 90.3225 199.915 90.3408C196.915 90.3591 194.023 91.4817 191.793 93.4947C177.498 105.96 149.207 135.166 149.158 170.899L149.344 257.023L199.965 257.029L250.663 256.959L250.783 170.528C250.527 135.101 222.393 105.898 208.057 93.3956Z'
        fill='#0DFFFF'
        fill-opacity='0.5'
      />
      <foreignObject x='95.8789' y='6.5293' width='208.078' height='309.283'>
        <motion.div
          style={{
            backdropFilter: 'blur(12px)',
            clipPath: 'url(#bgblur_0_1762_9062_clip_path)',
            height: '100%',
            width: '100%',
          }}
          initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          exit={{
            opacity: 0,
          }}
          transition={{
            duration: 0.3,
            ease: 'easeInOut',
            delay: 0.3,
          }}
        ></motion.div>
      </foreignObject>
      <path
        data-figma-bg-blur-radius='24'
        d='M145.211 82.7118C159.003 61.9569 175.572 45.7441 186.696 36.0469L186.696 36.0463C190.252 32.8377 194.866 31.052 199.656 31.0295C204.444 31.0071 209.072 32.7484 212.658 35.922L213.721 36.8556C224.874 46.6994 240.846 62.5996 254.253 82.6953C268.091 103.437 279.218 128.681 279.456 156.366V290.813C279.456 290.945 279.404 291.072 279.31 291.166C279.216 291.26 279.089 291.312 278.956 291.312L200.097 291.312H200.095L121.354 291.194C121.079 291.193 120.856 290.97 120.855 290.694L120.38 156.728V156.727C120.38 128.804 131.417 103.47 145.211 82.7118Z'
        fill='#96B3FF'
        fill-opacity='0.35'
        stroke='url(#paint1_linear_1762_9062)'
        stroke-linecap='round'
        stroke-linejoin='round'
      />
      <path
        d='M278.475 193.65C278.475 193.65 314.342 213.365 314.342 239.731L314.342 277.973C314.353 279.707 314.019 281.426 313.361 283.031C312.702 284.635 311.732 286.093 310.505 287.319C309.279 288.545 307.821 289.516 306.217 290.175C304.612 290.833 302.893 291.167 301.159 291.156L278 291.75L278.475 193.65Z'
        fill='url(#paint2_linear_1762_9062)'
      />

      <motion.path
        d='M199.732 311.122C196.092 311.16 192.497 311.933 189.163 313.395C185.829 314.856 182.824 316.975 180.329 319.626C177.834 322.276 175.899 325.404 174.642 328.82C173.384 332.236 172.829 335.871 173.01 339.507C173.01 352.571 199.614 373.236 199.614 373.236C199.614 373.236 226.336 352.452 226.217 339.507C226.445 335.88 225.931 332.244 224.706 328.822C223.48 325.401 221.57 322.265 219.09 319.608C216.611 316.951 213.615 314.828 210.287 313.368C206.958 311.909 203.367 311.145 199.732 311.122Z'
        fill='url(#paint4_linear_1762_9062)'
        animate={animate && { y: [0, -20] }}
        transition={{
          duration: 3,
          ease: 'easeInOut',
          repeat: Infinity,
          repeatType: 'reverse',
        }}
      />
      <foreignObject x='112.966' y='107.028' width='173.275' height='173.275'>
        <div
          style={{
            backdropFilter: 'blur(23px)',
            clipPath: 'url(#bgblur_1_1762_9062_clip_path)',
            height: '100%',
            width: '100%',
          }}
        ></div>
      </foreignObject>
      <g filter='url(#filter1_i_1762_9062)' data-figma-bg-blur-radius='45.9911'>
        <path
          d='M228.344 222.407C244.218 206.534 244.218 180.798 228.344 164.925C212.471 149.052 186.735 149.052 170.862 164.925C154.989 180.798 154.989 206.534 170.862 222.407C186.736 238.28 212.471 238.281 228.344 222.407Z'
          fill='#F1F1F1'
          fill-opacity='0.2'
        />
        <path
          d='M228.344 222.407C244.218 206.534 244.218 180.798 228.344 164.925C212.471 149.052 186.735 149.052 170.862 164.925C154.989 180.798 154.989 206.534 170.862 222.407C186.736 238.28 212.471 238.281 228.344 222.407Z'
          fill='#00D4DB'
          fill-opacity='0.1'
        />
        <path
          d='M171.863 165.925C187.184 150.605 212.023 150.605 227.344 165.926C242.664 181.246 242.664 206.086 227.344 221.406C212.023 236.727 187.184 236.727 171.863 221.407C156.542 206.086 156.542 181.246 171.863 165.925Z'
          stroke='url(#paint5_linear_1762_9062)'
          strokeOpacity='0.1'
          strokeWidth='2.83022'
        />
      </g>
      <foreignObject x='161.094' y='155.156' width='77.0312' height='77.0273'>
        <div
          style={{
            backdropFilter: 'blur(7.5px)',
            clipPath: 'url(#bgblur_2_1762_9062_clip_path)',
            height: '100%',
            width: '100%',
          }}
        ></div>
      </foreignObject>
      <g filter='url(#filter2_d_1762_9062)' data-figma-bg-blur-radius='15'>
        <path
          d='M216.237 210.297C225.42 201.114 225.42 186.226 216.237 177.043C207.054 167.86 192.165 167.86 182.983 177.043C173.8 186.226 173.8 201.114 182.983 210.297C192.165 219.48 207.054 219.48 216.237 210.297Z'
          fill='url(#paint6_linear_1762_9062)'
        />
        <path
          d='M183.124 177.184C192.229 168.08 206.991 168.08 216.095 177.185C225.2 186.29 225.2 201.05 216.095 210.155C206.991 219.26 192.229 219.26 183.124 210.156C174.02 201.051 174.02 186.289 183.124 177.184Z'
          stroke='url(#paint7_linear_1762_9062)'
          strokeWidth='0.4'
        />
      </g>
      <defs>
        <clipPath
          id='bgblur_0_1762_9062_clip_path'
          transform='translate(-95.8789 -6.5293)'
        >
          <path d='M145.211 82.7118C159.003 61.9569 175.572 45.7441 186.696 36.0469L186.696 36.0463C190.252 32.8377 194.866 31.052 199.656 31.0295C204.444 31.0071 209.072 32.7484 212.658 35.922L213.721 36.8556C224.874 46.6994 240.846 62.5996 254.253 82.6953C268.091 103.437 279.218 128.681 279.456 156.366V290.813C279.456 290.945 279.404 291.072 279.31 291.166C279.216 291.26 279.089 291.312 278.956 291.312L200.097 291.312H200.095L121.354 291.194C121.079 291.193 120.856 290.97 120.855 290.694L120.38 156.728V156.727C120.38 128.804 131.417 103.47 145.211 82.7118Z' />
        </clipPath>
        <filter
          id='filter1_i_1762_9062'
          x='112.966'
          y='107.028'
          width='173.275'
          height='173.275'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dy='7.66519' />
          <feGaussianBlur stdDeviation='15.3304' />
          <feComposite in2='hardAlpha' operator='arithmetic' k2='-1' k3='1' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0'
          />
          <feBlend mode='normal' in2='shape' result='effect1_innerShadow_1762_9062' />
        </filter>
        <clipPath
          id='bgblur_1_1762_9062_clip_path'
          transform='translate(-112.966 -107.028)'
        >
          <path d='M228.344 222.407C244.218 206.534 244.218 180.798 228.344 164.925C212.471 149.052 186.735 149.052 170.862 164.925C154.989 180.798 154.989 206.534 170.862 222.407C186.736 238.28 212.471 238.281 228.344 222.407Z' />
        </clipPath>
        <filter
          id='filter2_d_1762_9062'
          x='161.094'
          y='155.156'
          width='77.0312'
          height='77.0273'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feColorMatrix
            in='SourceAlpha'
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
            result='hardAlpha'
          />
          <feOffset dx='5' dy='5' />
          <feGaussianBlur stdDeviation='5' />
          <feColorMatrix
            type='matrix'
            values='0 0 0 0 0 0 0 0 0 0.831373 0 0 0 0 0.858824 0 0 0 0.5 0'
          />
          <feBlend
            mode='normal'
            in2='BackgroundImageFix'
            result='effect1_dropShadow_1762_9062'
          />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='effect1_dropShadow_1762_9062'
            result='shape'
          />
        </filter>
        <clipPath
          id='bgblur_2_1762_9062_clip_path'
          transform='translate(-161.094 -155.156)'
        >
          <path d='M216.237 210.297C225.42 201.114 225.42 186.226 216.237 177.043C207.054 167.86 192.165 167.86 182.983 177.043C173.8 186.226 173.8 201.114 182.983 210.297C192.165 219.48 207.054 219.48 216.237 210.297Z' />
        </clipPath>
        <linearGradient
          id='paint0_linear_1762_9062'
          x1='120.935'
          y1='247.31'
          x2='100.287'
          y2='327.938'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#88FFD9' />
          <stop offset='1' stopColor='#00B4B4' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_1762_9062'
          x1='74.413'
          y1='188.643'
          x2='322.036'
          y2='214.687'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='white' stopOpacity='0.25' />
          <stop offset='1' stopColor='white' stopOpacity='0' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_1762_9062'
          x1='294.498'
          y1='248.894'
          x2='265.442'
          y2='316.731'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#88FFD9' />
          <stop offset='1' stopColor='#00B4B4' />
        </linearGradient>
        <linearGradient
          id='paint3_linear_1762_9062'
          x1='204.895'
          y1='337.144'
          x2='188.149'
          y2='385.765'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#88FFD9' />
          <stop offset='1' stopColor='#0DFFFF' />
        </linearGradient>
        <linearGradient
          id='paint4_linear_1762_9062'
          x1='204.895'
          y1='337.144'
          x2='188.149'
          y2='385.765'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#88FFD9' />
          <stop offset='1' stopColor='#00B4B4' />
        </linearGradient>
        <linearGradient
          id='paint5_linear_1762_9062'
          x1='146.53'
          y1='194.483'
          x2='202.216'
          y2='138.797'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#A9E0FF' />
          <stop offset='1' stopColor='#8DA0FF' />
        </linearGradient>
        <linearGradient
          id='paint6_linear_1762_9062'
          x1='203.257'
          y1='168.779'
          x2='167.423'
          y2='207.028'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='white' />
          <stop offset='1' stopColor='white' stopOpacity='0.2' />
        </linearGradient>
        <linearGradient
          id='paint7_linear_1762_9062'
          x1='175.523'
          y1='192.251'
          x2='223.008'
          y2='197.31'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='white' stopOpacity='0.25' />
          <stop offset='1' stopColor='white' stopOpacity='0' />
        </linearGradient>
      </defs>
    </svg>
  );
}
