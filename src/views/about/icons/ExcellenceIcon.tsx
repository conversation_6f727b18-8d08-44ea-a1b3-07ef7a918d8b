import { motion } from 'framer-motion';

export default function ExcellenceIcon({ animate }: { animate?: boolean }) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='350'
      height='290'
      viewBox='0 0 350 290'
      fill='none'
    >
      <motion.path
        animate={animate && { x: [0, 10], rotate: [0, 5] }}
        transition={{
          duration: 3,
          ease: 'easeInOut',
          repeat: Infinity,
          repeatType: 'reverse',
        }}
        d='M169.869 180.728C168.465 183.622 168.577 187.026 170.158 189.828L192.629 228.331C194.53 231.595 194.344 235.668 192.156 238.753C190.055 241.891 186.335 243.532 182.599 242.995L138.442 237.167C136.902 236.95 135.335 237.104 133.853 237.596L131.583 238.424C130.838 238.828 130.159 239.36 129.596 240.002L99.5711 272.973C98.0723 274.563 96.1138 275.647 93.959 276.062C88.6576 276.932 83.6264 273.413 82.6222 268.132L75.0552 224.198C74.4533 221.01 72.3418 218.316 69.3935 216.97L28.4556 198.675C25.0309 197.143 22.8078 193.777 22.7516 190.03C22.6651 186.304 24.6477 182.838 27.9091 181.033L67.3434 159.817C70.3909 158.336 72.47 155.395 72.8559 152.028L77.4926 107.726C77.6229 106.73 77.9032 105.755 78.3436 104.854L78.8839 103.993C79.1188 103.413 79.4399 102.888 79.8357 102.412L80.6267 101.744L81.8371 100.638L85.364 99.353C88.633 98.5316 92.0929 99.4058 94.5682 101.692L126.807 132.364C129.145 134.615 132.444 135.567 135.619 134.931L179.457 125.796C183.158 125.016 186.984 126.374 189.37 129.3C191.692 132.26 192.089 136.291 190.391 139.652L169.869 180.728Z'
        fill='url(#paint0_linear_1763_8912)'
      />
      <g opacity='0.5' filter='url(#filter0_f_1763_8912)'>
        <path
          d='M131.253 158.449C112.08 158.449 96.5195 174.013 96.5195 193.182C96.5195 212.358 112.08 227.915 131.253 227.915C150.46 227.915 165.986 212.358 165.986 193.182C165.986 174.013 150.46 158.449 131.253 158.449V158.449Z'
          fill='#FF7B0D'
        />
      </g>
      <foreignObject x='83.8203' y='2.32813' width='270.473' height='274.719'>
        <motion.div
          style={{
            backdropFilter: 'blur(12px)',
            clipPath: 'url(#bgblur_0_1763_8912_clip_path)',
            height: '100%',
            width: '100%',
          }}
          initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          exit={{
            opacity: 0,
          }}
          transition={{
            duration: 0.3,
            ease: 'easeInOut',
            delay: 0.3,
          }}
        ></motion.div>
      </foreignObject>
      <path
        data-figma-bg-blur-radius='24'
        d='M241.385 28.0045C241.402 28.0087 241.419 28.0141 241.435 28.02C245.484 29.4958 248.552 32.8429 249.659 36.9899L249.76 37.3939L249.761 37.4012L261.796 92.6191L261.797 92.6212C262.617 96.4928 265.296 99.7117 268.937 101.243L268.936 101.243L321.528 122.876L321.527 122.876C326.003 124.706 329.121 128.813 329.711 133.58L329.761 134.043L329.761 134.053L329.786 134.517C329.946 139.304 327.416 143.792 323.202 146.135L272.358 174.692C268.969 176.651 266.755 180.158 266.432 184.063L262.618 240.552L262.578 241.017C262.075 245.798 258.912 249.9 254.383 251.6L254.384 251.601C249.758 253.476 244.474 252.512 240.788 249.158L240.789 249.157L198.525 211.567L198.521 211.564C197.115 210.292 195.428 209.374 193.591 208.856L190.673 208.123C189.667 208.027 188.643 208.099 187.663 208.355L187.654 208.357L132.594 221.622L132.586 221.624C129.775 222.251 126.837 221.985 124.178 220.845L124.166 220.839C117.727 217.861 114.835 210.287 117.65 203.771L117.653 203.763L141.003 152.191L141.145 151.85C142.557 148.302 142.225 144.292 140.235 141.021L110.234 92.6148C107.625 88.3999 107.687 83.0698 110.403 78.9278C113.068 74.7925 117.802 72.4742 122.703 72.9169L122.709 72.9174L179.283 78.7153L179.303 78.7174C183.391 79.3076 187.501 77.7634 190.195 74.6304L226.64 31.3607L226.648 31.3508C227.528 30.3589 228.559 29.4979 229.715 28.8358L229.762 28.8124L230.909 28.3053C231.602 27.8481 232.347 27.5128 233.133 27.2852L233.219 27.2685L234.516 27.1303L236.568 26.8332C236.632 26.8238 236.698 26.8267 236.762 26.8426L241.385 28.0045Z'
        fill='#FFCBB9'
        fillOpacity='0.35'
        stroke='url(#paint1_linear_1763_8912)'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <defs>
        <filter
          id='filter0_f_1763_8912'
          x='41.0535'
          y='102.983'
          width='180.397'
          height='180.399'
          filterUnits='userSpaceOnUse'
          colorInterpolationFilters='sRGB'
        >
          <feFlood floodOpacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur
            stdDeviation='27.733'
            result='effect1_foregroundBlur_1763_8912'
          />
        </filter>
        <clipPath
          id='bgblur_0_1763_8912_clip_path'
          transform='translate(-83.8203 -2.32813)'
        >
          <path d='M241.385 28.0045C241.402 28.0087 241.419 28.0141 241.435 28.02C245.484 29.4958 248.552 32.8429 249.659 36.9899L249.76 37.3939L249.761 37.4012L261.796 92.6191L261.797 92.6212C262.617 96.4928 265.296 99.7117 268.937 101.243L268.936 101.243L321.528 122.876L321.527 122.876C326.003 124.706 329.121 128.813 329.711 133.58L329.761 134.043L329.761 134.053L329.786 134.517C329.946 139.304 327.416 143.792 323.202 146.135L272.358 174.692C268.969 176.651 266.755 180.158 266.432 184.063L262.618 240.552L262.578 241.017C262.075 245.798 258.912 249.9 254.383 251.6L254.384 251.601C249.758 253.476 244.474 252.512 240.788 249.158L240.789 249.157L198.525 211.567L198.521 211.564C197.115 210.292 195.428 209.374 193.591 208.856L190.673 208.123C189.667 208.027 188.643 208.099 187.663 208.355L187.654 208.357L132.594 221.622L132.586 221.624C129.775 222.251 126.837 221.985 124.178 220.845L124.166 220.839C117.727 217.861 114.835 210.287 117.65 203.771L117.653 203.763L141.003 152.191L141.145 151.85C142.557 148.302 142.225 144.292 140.235 141.021L110.234 92.6148C107.625 88.3999 107.687 83.0698 110.403 78.9278C113.068 74.7925 117.802 72.4742 122.703 72.9169L122.709 72.9174L179.283 78.7153L179.303 78.7174C183.391 79.3076 187.501 77.7634 190.195 74.6304L226.64 31.3607L226.648 31.3508C227.528 30.3589 228.559 29.4979 229.715 28.8358L229.762 28.8124L230.909 28.3053C231.602 27.8481 232.347 27.5128 233.133 27.2852L233.219 27.2685L234.516 27.1303L236.568 26.8332C236.632 26.8238 236.698 26.8267 236.762 26.8426L241.385 28.0045Z' />
        </clipPath>
        <linearGradient
          id='paint0_linear_1763_8912'
          x1='84.2031'
          y1='99.7761'
          x2='142.222'
          y2='258.958'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#FFB37F' />
          <stop offset='1' stopColor='#FF7B0D' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_1763_8912'
          x1='158.76'
          y1='33.6057'
          x2='247.195'
          y2='238.135'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='white' stopOpacity='0.25' />
          <stop offset='1' stopColor='white' stopOpacity='0' />
        </linearGradient>
      </defs>
    </svg>
  );
}
