import { motion } from 'framer-motion';

export default function ClientFocusIcon({ animate }: { animate?: boolean }) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='297'
      height='289'
      viewBox='0 0 297 289'
      fill='none'
    >
      <motion.path
        data-figma-bg-blur-radius='42.9827'
        d='M90.1396 167.118C41.6865 167.118 0.787483 174.818 0.787109 204.596C0.787109 234.35 41.948 241.795 90.1396 241.795C138.593 241.795 179.492 234.094 179.492 204.316C179.492 174.562 138.331 167.118 90.1396 167.118ZM90.1328 22.9551C57.3111 22.9551 31.0088 48.7138 31.0088 80.8564C31.0088 112.999 57.3111 138.758 90.1328 138.758C122.943 138.758 149.257 112.999 149.257 80.8564C149.257 48.7138 122.943 22.9551 90.1328 22.9551Z'
        fill='url(#paint0_linear_1763_8894)'
        animate={animate && { y: [0, -20] }}
        transition={{
          duration: 3,
          ease: 'easeInOut',
          repeat: Infinity,
          repeatType: 'reverse',
        }}
      />
      <g filter='url(#filter1_f_1763_8894)'>
        <path
          d='M109.543 186.201C138.967 186.201 164.098 191.145 164.098 210.906C164.098 230.682 139.126 235.797 109.543 235.797C80.119 235.797 54.9878 230.852 54.9878 211.092C54.9878 191.315 79.9592 186.201 109.543 186.201'
          fill='#0062FF'
        />
      </g>
      <g filter='url(#filter2_f_1763_8894)'>
        <path
          d='M104.583 110.155C88.0635 110.155 74.8254 96.917 74.8254 80.3978C74.8254 63.8787 88.0635 50.6406 104.583 50.6406C121.096 50.6406 134.34 63.8787 134.34 80.3978C134.34 96.917 121.096 110.155 104.583 110.155'
          fill='#0062FF'
        />
      </g>
      <foreignObject x='35.8516' y='-24' width='284.363' height='336.998'>
        <motion.div
          style={{
            backdropFilter: 'blur(12px)',
            clipPath: 'url(#bgblur_1_1763_8894_clip_path)',
            height: '100%',
            width: '100%',
          }}
          initial={{
            opacity: 0,
          }}
          animate={{
            opacity: 1,
          }}
          exit={{
            opacity: 0,
          }}
          transition={{
            duration: 0.3,
            ease: 'easeInOut',
            delay: 0.3,
          }}
        ></motion.div>
      </foreignObject>
      <g data-figma-bg-blur-radius='24'>
        <mask
          id='path-4-outside-1_1763_8894'
          maskUnits='userSpaceOnUse'
          x='59.2148'
          y='0'
          width='237'
          height='289'
          fill='black'
        >
          <rect fill='white' x='59.2148' width='237' height='289' />
          <path d='M178.034 190.062C114.49 190.062 60.8525 200.162 60.8525 239.214C60.8526 278.235 114.834 287.998 178.034 287.998C241.578 287.998 295.215 277.898 295.215 238.847C295.214 199.826 241.234 190.063 178.034 190.062ZM178.039 1C134.995 1.00002 100.501 34.7805 100.501 76.9336C100.501 119.087 134.995 152.868 178.039 152.868C221.067 152.868 255.577 119.087 255.577 76.9336C255.577 34.7805 221.067 1 178.039 1Z' />
        </mask>
        <path
          d='M178.034 190.062C114.49 190.062 60.8525 200.162 60.8525 239.214C60.8526 278.235 114.834 287.998 178.034 287.998C241.578 287.998 295.215 277.898 295.215 238.847C295.214 199.826 241.234 190.063 178.034 190.062ZM178.039 1C134.995 1.00002 100.501 34.7805 100.501 76.9336C100.501 119.087 134.995 152.868 178.039 152.868C221.067 152.868 255.577 119.087 255.577 76.9336C255.577 34.7805 221.067 1 178.039 1Z'
          fill='#3EA0FE'
          fill-opacity='0.35'
        />
        <path
          d='M178.034 190.062L178.034 189.063L178.034 189.063L178.034 190.062ZM60.8525 239.214L59.8525 239.214V239.214H60.8525ZM178.034 287.998L178.034 288.998H178.034V287.998ZM295.215 238.847L296.215 238.847V238.847H295.215ZM178.039 1L178.039 5.96046e-08H178.039V1ZM100.501 76.9336L99.501 76.9336L99.501 76.9336L100.501 76.9336ZM178.039 152.868L178.039 153.868L178.039 153.868L178.039 152.868ZM255.577 76.9336L256.577 76.9336V76.9336H255.577ZM178.034 190.062V191.062C146.286 191.062 117.13 193.589 95.9421 200.939C85.3543 204.611 76.8281 209.464 70.9567 215.742C65.1072 221.997 61.8525 229.707 61.8525 239.214H60.8525H59.8525C59.8525 229.195 63.3026 220.998 69.4959 214.376C75.6674 207.777 84.5221 202.783 95.2867 199.049C116.804 191.585 146.239 189.063 178.034 189.063V190.062ZM60.8525 239.214L61.8525 239.214C61.8526 248.711 65.1269 256.393 71.0016 262.612C76.8988 268.854 85.4567 273.663 96.0668 277.293C117.299 284.555 146.456 286.998 178.034 286.998V287.998V288.998C146.412 288.998 116.978 286.559 95.4195 279.185C84.6343 275.496 75.7469 270.548 69.5477 263.985C63.3259 257.399 59.8526 249.227 59.8525 239.214L60.8525 239.214ZM178.034 287.998L178.034 286.998C209.783 286.998 238.938 284.471 260.126 277.122C270.713 273.449 279.24 268.597 285.111 262.318C290.96 256.064 294.215 248.354 294.215 238.847H295.215H296.215C296.215 248.865 292.765 257.062 286.572 263.685C280.4 270.283 271.546 275.278 260.781 279.012C239.264 286.475 209.83 288.998 178.034 288.998L178.034 287.998ZM295.215 238.847L294.215 238.847C294.215 229.349 290.94 221.668 285.066 215.449C279.169 209.206 270.611 204.397 260.001 200.768C238.769 193.506 209.612 191.063 178.034 191.062L178.034 190.062L178.034 189.063C209.656 189.063 239.089 191.501 260.648 198.876C271.433 202.565 280.32 207.513 286.52 214.076C292.741 220.662 296.215 228.834 296.215 238.847L295.215 238.847ZM178.039 1L178.039 2C135.528 2.00002 101.501 35.3524 101.501 76.9336L100.501 76.9336L99.501 76.9336C99.5012 34.2086 134.463 2.23848e-05 178.039 5.96048e-08L178.039 1ZM100.501 76.9336H101.501C101.501 118.515 135.528 151.868 178.039 151.868L178.039 152.868L178.039 153.868C134.463 153.868 99.501 119.659 99.501 76.9336H100.501ZM178.039 152.868V151.868C220.535 151.868 254.577 118.515 254.577 76.9336H255.577H256.577C256.577 119.659 221.6 153.868 178.039 153.868V152.868ZM255.577 76.9336L254.577 76.9336C254.577 35.3525 220.535 2 178.039 2V1V5.96046e-08C221.6 5.96046e-08 256.577 34.2085 256.577 76.9336L255.577 76.9336Z'
          fill='url(#paint1_linear_1763_8894)'
          mask='url(#path-4-outside-1_1763_8894)'
        />
      </g>
      <defs>
        <clipPath
          id='bgblur_0_1763_8894_clip_path'
          transform='translate(42.1936 20.0276)'
        >
          <path d='M90.1396 167.118C41.6865 167.118 0.787483 174.818 0.787109 204.596C0.787109 234.35 41.948 241.795 90.1396 241.795C138.593 241.795 179.492 234.094 179.492 204.316C179.492 174.562 138.331 167.118 90.1396 167.118ZM90.1328 22.9551C57.3111 22.9551 31.0088 48.7138 31.0088 80.8564C31.0088 112.999 57.3111 138.758 90.1328 138.758C122.943 138.758 149.257 112.999 149.257 80.8564C149.257 48.7138 122.943 22.9551 90.1328 22.9551Z' />
        </clipPath>
        <filter
          id='filter1_f_1763_8894'
          x='12.0056'
          y='143.219'
          width='195.075'
          height='135.561'
          filterUnits='userSpaceOnUse'
          color-interpolation-filters='sRGB'
        >
          <feFlood flood-opacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur
            stdDeviation='21.4913'
            result='effect1_foregroundBlur_1763_8894'
          />
        </filter>
        <filter
          id='filter2_f_1763_8894'
          x='25.2289'
          y='1.04526'
          width='158.706'
          height='158.704'
          filterUnits='userSpaceOnUse'
          color-interpolation-filters='sRGB'
        >
          <feFlood flood-opacity='0' result='BackgroundImageFix' />
          <feBlend
            mode='normal'
            in='SourceGraphic'
            in2='BackgroundImageFix'
            result='shape'
          />
          <feGaussianBlur
            stdDeviation='24.7977'
            result='effect1_foregroundBlur_1763_8894'
          />
        </filter>
        <clipPath id='bgblur_1_1763_8894_clip_path' transform='translate(-35.8516 24)'>
          <path d='M178.034 190.062C114.49 190.062 60.8525 200.162 60.8525 239.214C60.8526 278.235 114.834 287.998 178.034 287.998C241.578 287.998 295.215 277.898 295.215 238.847C295.214 199.826 241.234 190.063 178.034 190.062ZM178.039 1C134.995 1.00002 100.501 34.7805 100.501 76.9336C100.501 119.087 134.995 152.868 178.039 152.868C221.067 152.868 255.577 119.087 255.577 76.9336C255.577 34.7805 221.067 1 178.039 1Z' />
        </clipPath>
        <linearGradient
          id='paint0_linear_1763_8894'
          x1='13.5692'
          y1='22.955'
          x2='223.561'
          y2='141.339'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='#39AFFD' />
          <stop offset='1' stop-color='#477FFF' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_1763_8894'
          x1='257.908'
          y1='34.4356'
          x2='70.641'
          y2='223.833'
          gradientUnits='userSpaceOnUse'
        >
          <stop stop-color='white' stopOpacity='0.25' />
          <stop offset='1' stop-color='white' stopOpacity='0' />
        </linearGradient>
      </defs>
    </svg>
  );
}
