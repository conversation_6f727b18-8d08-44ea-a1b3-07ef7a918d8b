import { getCarouselGroups, getPartners } from '@/services/directus';
import PartnersSection from '../home/<USER>';
import HeroSection from './HeroSection';
import MilestoneSection from './MilestoneSection';
import OurApproachSection from './OurApproachSection';
import OurCultureValueSection from './OurCultureValueSection';
import OurDNASection from './OurDNASection';
export default async function AboutView() {
  const partners = await getPartners();
  const carouselGroups = await getCarouselGroups('aboutus');

  return (
    <section className='bg-background'>
      <HeroSection />
      <OurDNASection />
      <MilestoneSection />
      <OurCultureValueSection />
      <PartnersSection partners={partners} />
      <OurApproachSection carouselGroups={carouselGroups} />
    </section>
  );
}
