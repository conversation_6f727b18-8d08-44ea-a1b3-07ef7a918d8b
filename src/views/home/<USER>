'use client';
import BlurShine from '@/components/common/BlurShine';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import GalleryDialog from '@/components/layout/GalleryDialog';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { cn } from '@/utils/cn';
import { AnimatePresence, motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { HTMLAttributes, useEffect, useState } from 'react';

export default function LifeAtSection() {
  const t = useTranslations();
  const [activeIndex, setActiveIndex] = useState(0);
  const lifeAtItems = [
    {
      image: [
        ['/home/<USER>/1.webp'],
        ['/home/<USER>/13.webp', '/home/<USER>/14.webp'],
      ],
      className: 'col-span-2',
      isCol: true,
    },
    {
      image: [
        ['/home/<USER>/17.webp'],
        ['/home/<USER>/2.webp', '/home/<USER>/3.webp'],
        ['/home/<USER>/12.webp'],
      ],
      className: 'col-span-2',
      isCol: true,
    },

    {
      image: [
        ['/home/<USER>/4.webp'],
        ['/home/<USER>/15.webp', '/home/<USER>/16.webp'],
      ],
      className: 'col-span-1 row-span-2',
      isCol: false,
    },
    {
      image: [
        ['/home/<USER>/19.webp'],
        ['/home/<USER>/5.webp', '/home/<USER>/8.webp'],
        ['/home/<USER>/18.webp'],
      ],
      className: 'col-span-1 row-span-2',
      isCol: false,
    },
    {
      image: [
        ['/home/<USER>/6.webp'],
        ['/home/<USER>/20.webp', '/home/<USER>/21.webp'],
      ],
      className: 'col-span-1 row-span-2',
      isCol: false,
    },
    {
      image: [
        ['/home/<USER>/7.webp'],
        ['/home/<USER>/22.webp'],
        ['/home/<USER>/23.webp', '/home/<USER>/24.webp'],
      ],
      className: 'col-span-2',
      isCol: true,
    },

    {
      image: [
        ['/home/<USER>/9.webp', '/home/<USER>/10.webp'],
        ['/home/<USER>/26.webp'],
      ],
      className: 'col-span-2',
      isCol: true,
    },
    {
      image: [['/home/<USER>/11.webp'], ['/home/<USER>/27.webp']],
      className: 'col-span-1',
      isCol: true,
    },
  ] as {
    image: string[][];
    className: React.HTMLAttributes<HTMLDivElement>['className'];
    isCol: boolean;
  }[];
  const [activeIndexes, setActiveIndexes] = useState<number[]>(
    Array.from({ length: lifeAtItems.length }, (_) => 0)
  );
  useEffect(() => {
    const interval = setInterval(() => {
      const randomIndex: number = Math.floor(Math.random() * lifeAtItems.length);
      setActiveIndexes((prev) => {
        const newIndexes = [...prev];
        newIndexes[randomIndex] =
          (newIndexes[randomIndex] + 1) % lifeAtItems[randomIndex].image.length;
        return newIndexes;
      });
    }, 3000);
    return () => clearInterval(interval);
  }, [lifeAtItems.length]);

  return (
    <Section center container className='py-10 lg:py-24'>
      <BlurShine size={1600} />
      <HeroTitle
        tag={t('home.life-at-vietnam-silicon-tag')}
        title={t('home.life-at-vietnam-silicon-title')}
        description={t('home.life-at-vietnam-silicon-description')}
        descriptionClassName='max-w-[762px]'
      />
      <div className='mt-15 grid aspect-[1424/583] w-full grid-cols-5 gap-1 overflow-hidden md:gap-3 lg:gap-4'>
        {lifeAtItems.map((item, index) => (
          <GallerySection
            activeIndex={activeIndexes[index]}
            key={index}
            image={item.image}
            isCol={item.isCol}
            className={item.className}
            currentIndex={index}
          />
        ))}
      </div>
    </Section>
  );
}

const GallerySection = ({
  image,
  isCol,
  className,
  currentIndex,
  activeIndex,
}: {
  image: string[][];
  isCol: boolean;
  className: HTMLAttributes<HTMLDivElement>['className'];
  currentIndex: number;
  activeIndex: number;
}) => {
  const [openIndex, setOpenIndex] = useState('');
  return (
    <>
      <GalleryDialog
        type='image'
        src={openIndex}
        isOpen={openIndex !== ''}
        onOpenChange={(isOpen: boolean) => {
          if (!isOpen) setOpenIndex('');
        }}
      />
      <motion.div
        layout
        style={{
          gridTemplateColumns: `repeat(${!isCol ? 1 : image[activeIndex].length}, 1fr)`,
          gridTemplateRows: `repeat(${isCol ? 1 : image[activeIndex].length}, 1fr)`,
        }}
        className={cn('grid h-full w-full gap-1 md:gap-3 lg:gap-4', className)}
      >
        <AnimatePresence mode='popLayout'>
          {image?.[activeIndex]?.map((img) => (
            <motion.div
              onClick={() => {
                setOpenIndex(img);
              }}
              key={`card-${currentIndex}-${img}`}
              initial={{ filter: 'blur(10px)', scale: 0.9 }}
              animate={{ filter: 'blur(0px)', scale: 1 }}
              exit={{ filter: 'blur(10px)', scale: 0.9 }}
              transition={{ duration: 0.5 }}
              style={{ willChange: 'opacity' }}
              className='relative flex cursor-pointer items-center justify-center overflow-hidden will-change-transform'
            >
              <motion.img
                layoutId={`img-${img}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                aria-label='View image'
                alt='Life at Vietnam Silicon'
                whileHover={{
                  filter:
                    'grayscale(50%) brightness(70%) sepia(100%) hue-rotate(180deg) saturate(200%)',
                }}
                transition={{ duration: 0.5 }}
                src={img}
                className='absolute size-full object-cover'
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>
    </>
  );
};
