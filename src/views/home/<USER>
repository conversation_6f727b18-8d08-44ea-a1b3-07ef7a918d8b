'use client';
import BlurShine from '@/components/common/BlurShine';
import { Glasscard } from '@/components/common/Card';
import HeroTitle from '@/components/common/HeroTitle';
import LazyYouTube from '@/components/common/LazyYouTube';
import { Section } from '@/components/common/Section';
import { SlashDiv } from '@/components/common/SlashBackground';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { getCdnUrl } from '@/utils/url/asset';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';

export default function TechnologySolutionsSection() {
  const t = useTranslations('home');
  const sectionRef = useRef<HTMLDivElement>(null);
  const [sectionHeight, setSectionHeight] = useState(1000);

  useEffect(() => {
    const updateHeight = () => {
      if (sectionRef.current) {
        setSectionHeight(sectionRef.current.clientHeight);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);

    // Initial height calculation after content renders
    const timer = setTimeout(updateHeight, 100);

    return () => {
      window.removeEventListener('resize', updateHeight);
      clearTimeout(timer);
    };
  }, []);

  const techSolutions = [
    {
      title: t('ai-insights'),
      description: t('ai-insights-description'),
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='20'
          height='20'
          viewBox='0 0 20 20'
          fill='none'
        >
          <path
            d='M7.1875 12.5C12.5 12.5 12.5 7.1875 12.5 7.1875C12.5 7.1875 12.5 12.5 17.8125 12.5C12.5 12.5 12.5 17.8125 12.5 17.8125C12.5 17.8125 12.5 12.5 7.1875 12.5ZM5 2.1875C5 2.1875 5 5 2.1875 5C5 5 5 7.8125 5 7.8125C5 7.8125 5 5 7.8125 5C5 5 5 2.1875 5 2.1875Z'
            fill='white'
          />
          <path
            d='M7.1875 12.5C12.5 12.5 12.5 7.1875 12.5 7.1875C12.5 7.1875 12.5 12.5 17.8125 12.5C12.5 12.5 12.5 17.8125 12.5 17.8125C12.5 17.8125 12.5 12.5 7.1875 12.5ZM5 2.1875C5 2.1875 5 5 2.1875 5C5 5 5 7.8125 5 7.8125C5 7.8125 5 5 7.8125 5C5 5 5 2.1875 5 2.1875Z'
            stroke='white'
            strokeWidth='1.875'
            strokeLinecap='round'
            strokeLinejoin='round'
          />
        </svg>
      ),
    },
    {
      title: t('emerging-technologies'),
      description: t('emerging-technologies-description'),
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='12'
          height='18'
          viewBox='0 0 12 18'
          fill='none'
        >
          <path
            d='M6.0013 0.666672C4.45421 0.666672 2.97047 1.28125 1.87651 2.37522C0.78255 3.46918 0.167969 4.95291 0.167969 6.5C0.167969 8.48334 1.15964 10.225 2.66797 11.2833V13.1667C2.66797 13.3877 2.75577 13.5996 2.91205 13.7559C3.06833 13.9122 3.28029 14 3.5013 14H8.5013C8.72232 14 8.93428 13.9122 9.09056 13.7559C9.24684 13.5996 9.33464 13.3877 9.33464 13.1667V11.2833C10.843 10.225 11.8346 8.48334 11.8346 6.5C11.8346 4.95291 11.2201 3.46918 10.1261 2.37522C9.03213 1.28125 7.5484 0.666672 6.0013 0.666672ZM3.5013 16.5C3.5013 16.721 3.5891 16.933 3.74538 17.0893C3.90166 17.2455 4.11362 17.3333 4.33464 17.3333H7.66797C7.88898 17.3333 8.10094 17.2455 8.25723 17.0893C8.4135 16.933 8.5013 16.721 8.5013 16.5V15.6667H3.5013V16.5Z'
            fill='white'
          />
        </svg>
      ),
    },
    {
      title: t('cybersecurity'),
      description: t('cybersecurity-description'),
      icon: (
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='18'
          height='20'
          viewBox='0 0 18 20'
          fill='none'
        >
          <path
            d='M17.7 3.83919L9.2 0.0448461C9.13743 0.0153126 9.06914 0 9 0C8.93086 0 8.86257 0.0153126 8.8 0.0448461L0.3 3.83919C0.212109 3.87768 0.137097 3.94073 0.0839062 4.02081C0.0307154 4.1009 0.0015894 4.19466 0 4.2909V6.98107C0 11.9197 2.94 17.9024 8.82 19.9702C8.93673 20.0099 9.06327 20.0099 9.18 19.9702C15.06 17.9024 18 11.9197 18 6.98107V4.2909C17.9984 4.19466 17.9693 4.1009 17.9161 4.02081C17.8629 3.94073 17.7879 3.87768 17.7 3.83919ZM13.688 7.16175L8.408 13.9093C8.33125 14.0065 8.23495 14.0865 8.12537 14.1439C8.01579 14.2013 7.89538 14.2349 7.772 14.2425L7.72 14.2445C7.4875 14.2441 7.2646 14.1514 7.1 13.9866L4.38 11.2562C4.21596 11.0912 4.12397 10.8675 4.12425 10.6343C4.12453 10.4012 4.21707 10.1777 4.3815 10.013C4.54593 9.84837 4.7688 9.75602 5.00106 9.75631C5.23332 9.75659 5.45596 9.84948 5.62 10.0145L7.642 12.0442L12.312 6.07665C12.458 5.90102 12.6664 5.78942 12.893 5.76551C13.1197 5.74161 13.3467 5.80729 13.5258 5.94861C13.705 6.08993 13.8223 6.29578 13.8527 6.52246C13.8832 6.74915 13.8245 6.97882 13.689 7.16275L13.688 7.16175Z'
            fill='white'
          />
        </svg>
      ),
    },
  ];
  return (
    <>
      <Section ref={sectionRef} container center className='z-10 gap-6 pb-10 lg:pb-40'>
        <BlurShine size={1000} direction='right' />
        <SlashDiv
          additionalHeight={700}
          className='flex flex-col items-center justify-center gap-6'
        >
          <HeroTitle
            className='mt-10 md:mt-52'
            title={t('technology-solutions-title')}
            description={t('technology-solutions-description')}
            tag={t('technology-solutions')}
            descriptionClassName='max-w-[655px]'
            // titleClassName='md:text-[4.875rem] text-5xl leading-tight'
          />
          <ScrollReveal className='mt-6 flex w-full justify-center' blurred delay={0.5}>
            <Glasscard
              className='w-full max-w-[1140px] bg-white/5 p-0 backdrop-blur-3xl md:p-6 lg:p-10'
              autoRotate={{ duration: 3 }}
              borderColor='#FFFFFF63'
              isShadow={false}
            >
              <LazyYouTube
                videoId='zzA9hI70dcg'
                title='Vietnam Silicon CEO: Our Vision for Technology Innovation in Vietnam and Southeast Asia'
                className='rounded-2xl'
                loop={true}
                playlist='zzA9hI70dcg'
                thumbnail={getCdnUrl('/home/<USER>')}
              />
            </Glasscard>
          </ScrollReveal>

          <ScrollReveal
            blurred
            className='grid max-w-[1140px] grid-cols-1 gap-6 lg:grid-cols-3'
          >
            {techSolutions.map((solution) => (
              <motion.div className='rounded-2xl' key={solution.title}>
                <Glasscard
                  className='h-full w-full max-w-[1140px] bg-white/5 p-4 lg:min-h-[310px]'
                  borderColor='#FFFFFF63'
                >
                  <div className='bg-primary mt-3 flex size-11 items-center justify-center rounded-xl'>
                    {solution.icon}
                  </div>
                  <div className='mt-4 text-2xl font-medium max-lg:mt-4 md:text-3xl lg:text-[40px] lg:whitespace-pre-wrap'>
                    {solution.title.replaceAll(' ', '\n')}
                  </div>
                  <div className='mt-1 text-sm text-white/60'>{solution.description}</div>
                </Glasscard>
              </motion.div>
            ))}
          </ScrollReveal>
        </SlashDiv>
      </Section>
    </>
  );
}
