'use client';
import { Button } from '@/components/common/Button';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { AuroraBackground } from '@/components/layout/AuroraBackground';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export default function HeroSection() {
  const t = useTranslations('home');
  return (
    <Section className='flex min-h-[520px]'>
      <AuroraBackground>
        <HeroTitle
          className='mt-32 md:mt-52'
          title={t('hero-title')}
          description={t('hero-description')}
          tag={t('hero-tag')}
          titleClassName='md:text-[4.875rem] text-5xl leading-tight'
          descriptionClassName='lg:text-lg'
        />
        <ScrollReveal delay={0.7} className='mt-13 flex flex-col items-center gap-4'>
          <Link href={'/careers/open-positions'}>
            <Button className='rounded-lg px-8 py-3 text-base' aria-label='View all jobs'>
              {t('hero-button')}
            </Button>
          </Link>
        </ScrollReveal>
      </AuroraBackground>
    </Section>
  );
}
