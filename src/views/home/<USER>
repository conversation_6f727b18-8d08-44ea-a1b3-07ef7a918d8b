import { getPartners } from '@/services/directus';
import HeroSection from './HeroSection';
import InnovationInActionSection from './InnovationInActionSection';
import InsightsSection from './InsightsSection';
import LifeAtSection from './LifeAtSection';
import PartnersSection from './PartnersSection';
import TechnologySolutionsSection from './TechnologySolutionsSection';
import WhatWeOfferSection from './WhatWeOfferSection';
export default async function HomeView() {
  const partners = await getPartners();

  return (
    <>
      <HeroSection />
      <TechnologySolutionsSection />
      <WhatWeOfferSection />
      <InnovationInActionSection />

      <InsightsSection />
      <PartnersSection partners={partners} />
      <LifeAtSection />
      {/* <div className='h-[1000px]'></div> */}
    </>
  );
}
