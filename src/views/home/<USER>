'use client';
import { Glasscard } from '@/components/common/Card';
import { CarouselButton } from '@/components/common/CarouselButton';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { SlashBackground2 } from '@/components/common/SlashBackground';
import ScrollReveal from '@/components/motion/ScrollReveal';
import useWindowDimensions from '@/hooks/useWindowDimensions';
import { Link } from '@/i18n/navigation';
import { getDynamicSections } from '@/services/directus';
import { cn } from '@/utils/cn';
import { TAB } from '@/utils/constants/resources';
import { getAssetUrl } from '@/utils/url/asset';
import { IconArrowRight } from '@tabler/icons-react';
import { AnimatePresence, motion, useInView } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { match, P } from 'ts-pattern';
export default function InsightsSection() {
  const [currentSlideIndex, setSlideIndex] = useState(2);
  const [insightItems, setInsightItems] = useState<any>([]);
  const t = useTranslations('home');
  const [animatedNumber, setAnimatedNumber] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);
  const [canSlide, setCanSlide] = useState(true);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(sectionRef);

  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying && isInView) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, isInView]);

  const handlePrev = useCallback(() => {
    if (!canSlide) return;
    setSlideIndex(
      currentSlideIndex === 0 ? insightItems.length - 1 : currentSlideIndex - 1
    );
    setTimeout(() => setCanSlide(true), 500);
  }, [currentSlideIndex, insightItems, canSlide]);

  const handleNext = useCallback(() => {
    if (!canSlide) return;

    setSlideIndex(
      currentSlideIndex === insightItems.length - 1 ? 0 : currentSlideIndex + 1
    );
    setTimeout(() => setCanSlide(true), 500);
  }, [currentSlideIndex, insightItems, canSlide]);

  useEffect(() => {
    if (Math.floor(animatedNumber) === 9) {
      handleNext();
    }
  }, [Math.floor(animatedNumber)]);

  useEffect(() => {
    setCanSlide(false);
    // Reset animation when slide changes
    progressRef.current = 0;
    setAnimatedNumber(0);
    setTimeout(() => setCanSlide(true), 500);
  }, [currentSlideIndex]);

  useEffect(() => {
    const getInsightsSection = async () => {
      const res = await getDynamicSections(
        'insight-v2',
        {
          Resources: [
            'title',
            'description',
            'id',
            'slug',
            'thumbnail',
            { type: ['value'] },
          ],
        }
        // [{ items: { 'item:Resources': { type: { _neq: 'videos' } } } }]
      );
      if (res.success) {
        setInsightItems(
          res?.data?.filter((item: any) => item?.item?.type?.value === TAB.ARTICLE)
        );
      }
    };
    getInsightsSection();
  }, []);
  const { width } = useWindowDimensions();

  const orderItems = useMemo(() => {
    const items: any[] = [];
    if (!insightItems?.length) return items;
    for (let i = -2; i <= 2; i++) {
      const index = currentSlideIndex + i;
      if (index < 0) {
        items.push(insightItems[insightItems.length + index]);
      } else if (index >= insightItems.length) {
        items.push(insightItems[index - insightItems.length]);
      } else {
        items.push(insightItems[index]);
      }
    }
    return items;
  }, [insightItems, currentSlideIndex]);

  return (
    <>
      <Section container center className='mt-10 md:mt-20 md:mb-20'>
        <HeroTitle
          tag={t('insights-tag')}
          title={t('insights-title')}
          description={t('insights-description')}
          titleClassName='text-center max-w-[607px]'
          descriptionClassName='max-w-[607px] mb-20'
        />

        <ScrollReveal className='relative mb-12 flex w-full items-center justify-center gap-10 max-md:mt-10 md:mt-20 lg:mt-10'>
          <SlashBackground2 h={2000} />
          <AnimatePresence mode='wait'>
            <motion.div
              className='absolute inset-0 flex size-full items-center justify-center gap-10'
              key={`insight-blur-${currentSlideIndex}`}
              initial={{
                opacity: 0,
              }}
              animate={{
                opacity: 1,
              }}
              exit={{
                opacity: 0,
              }}
              transition={{
                duration: 0.5,
                ease: 'easeInOut',
              }}
            >
              <motion.div
                animate={{
                  y: -60,
                  skewX: -30,
                  x: 100,
                }}
                className='h-[480px] w-[200px] border border-white/10 will-change-transform'
              ></motion.div>
              <motion.div className='h-[480px] w-[900px] skew-x-[-30deg] bg-blue-800 blur-2xl'></motion.div>
              <motion.div
                animate={{
                  y: 30,
                  skewX: -30,
                  x: 0,
                }}
                className='h-[480px] w-[200px] border border-white/10'
              ></motion.div>
            </motion.div>
          </AnimatePresence>
          <motion.div className='absolute left-0 z-10 flex h-full items-center'>
            <CarouselButton direction='left' onClick={handlePrev} />
          </motion.div>
          <motion.div className='absolute right-0 z-10 flex h-full items-center'>
            <CarouselButton direction='right' onClick={handleNext} />
          </motion.div>
          {orderItems?.map(({ item }: any, index: number) => (
            <Link
              href={`/resources/${item.slug}`}
              key={`insight-card-${item.id}`}
              onMouseLeave={() => setIsPlaying(true)}
              onMouseEnter={() => setIsPlaying(false)}
            >
              <Glasscard
                isShadow={false}
                borderZIndex={4}
                layoutId={index === 0 || index === 4 ? `insight-card-${item.id}` : ''}
                borderColor='var(--color-blue-800)'
                animate={{
                  width: match([index, width])
                    .with([2, P.when((width) => width && width > 1024)], () => 800)
                    .with([2, P.when((width) => width && width > 640)], () => 430)
                    .with([2, P.when((width) => width && width <= 640)], () => 350)
                    .with([1, P.any], [3, P.any], () => 200)
                    .otherwise(() => 0),
                  // transform: 'skewX(-30deg)',
                  skewX: width && width <= 640 ? 0 : -30,
                }}
                transition={{
                  duration: 0.3,
                  ease: 'easeInOut',
                  width: {
                    delay: 0.3,
                    duration: 0.4,
                  },
                }}
                className={cn('h-[480px] overflow-hidden')}
                borderRadius={0}
              >
                <div className='absolute top-0 left-0 size-full sm:-left-36 sm:skew-x-[30deg]'>
                  <motion.div
                    className='relative h-full bg-amber-400 will-change-transform'
                    animate={{
                      width: match([index, width])
                        .with([2, P.when((width) => width && width > 1024)], () => 1080)
                        .with([2, P.when((width) => width && width > 640)], () => 620)
                        .with([2, P.when((width) => width && width <= 640)], () => 350)
                        .with([1, P.any], [3, P.any], () => 900)
                        .otherwise(() => 0),
                    }}
                    transition={{
                      duration: 0.4,
                      ease: 'easeInOut',
                      delay: 0.3,
                    }}
                  >
                    <img
                      src={getAssetUrl(item.thumbnail)}
                      alt={item.title}
                      className='h-full w-[100%] object-cover object-top'
                      loading='lazy'
                      decoding='async'
                    />
                    <AnimatePresence mode='wait'>
                      {index === 2 && (
                        <motion.div
                          className='from-background absolute right-0 bottom-0 left-0 flex w-full flex-col items-center justify-center bg-gradient-to-t to-transparent p-4 text-white will-change-transform'
                          initial={{
                            opacity: 0,
                            filter: 'blur(10px)',
                          }}
                          animate={{
                            opacity: 1,
                            filter: 'blur(0px)',
                          }}
                          exit={{
                            opacity: 0,
                            filter: 'blur(10px)',
                            transition: {
                              delay: 0,
                            },
                          }}
                          transition={{
                            duration: 0.3,
                            ease: 'easeInOut',
                            delay: 0.5,
                          }}
                        >
                          <div className='w-full max-lg:-ml-10 max-md:-ml-5 max-sm:-ml-0 sm:w-[60%] md:w-3/5 lg:w-[65%]'>
                            <div className='line-clamp-2 w-4/5 text-xl font-bold lg:text-3xl lg:leading-[48px]'>
                              {item.title}
                            </div>
                            <div className='mt-2 mb-5 flex w-full flex-col justify-between gap-1 text-sm md:w-9/10 lg:flex-row lg:items-end lg:gap-10'>
                              <div className='line-clamp-2 sm:line-clamp-3'>
                                {item.description}
                              </div>
                              <div className='flex flex-none items-center gap-2 text-sm lg:self-end'>
                                Explore more <IconArrowRight />
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                    <AnimatePresence mode='wait'>
                      {index !== 2 && (
                        <motion.div
                          initial={{
                            opacity: 0,
                          }}
                          animate={{
                            opacity: 1,
                          }}
                          exit={{
                            opacity: 0,
                          }}
                          transition={{
                            duration: 0.3,
                            ease: 'easeInOut',
                          }}
                          className='absolute right-0 bottom-0 left-0 flex size-full justify-center bg-black/50'
                        ></motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                </div>
              </Glasscard>
            </Link>
          ))}
        </ScrollReveal>
        <div className='z-10 flex items-center justify-center gap-2' ref={sectionRef}>
          {insightItems?.map(({ item }: any, index: number) => (
            <motion.div
              key={`insight-dot-${index}`}
              onClick={() => setSlideIndex(index)}
              className={cn('h-0.5 w-12 cursor-pointer rounded-2xl bg-white')}
            >
              <motion.div
                animate={{
                  width: index === currentSlideIndex ? `${animatedNumber * 12}%` : 0,
                }}
                className='bg-primary h-full'
              />
            </motion.div>
          ))}
        </div>
      </Section>
    </>
  );
}
