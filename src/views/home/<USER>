'use client';

import { Glasscard } from '@/components/common/Card';
import { CarouselButton } from '@/components/common/CarouselButton';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { AnimatePresence, motion, useInView } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';
import BusinessImpactIcon from './icons/BusinessImpactIcon';
import CareerGrowthIcon from './icons/CareerGrowthIcon';
import FutureReadySolutionIcon from './icons/FutureReadySolutionIcon';
import { GlobalExposureIcon } from './icons/GlobalExposureIcon';
import InnovationLeadershipIcon from './icons/InnovationLeadershipIcon';
import { TechnicalExcellenceIcon } from './icons/TechnicalExcellenceIcon';
export default function WhatWeOfferSection() {
  const t = useTranslations('home');
  return (
    <Section
      container
      center
      className='my-20 grid w-full max-w-[1140px] grid-cols-2 *:flex *:flex-col *:items-center *:justify-center lg:my-40'
    >
      <ScrollReveal className='col-span-2 mb-10 flex flex-col lg:col-span-1 lg:mb-24 xl:mb-0 xl:!items-start xl:!text-start'>
        <HeroTitle
          className='lg:items-start lg:justify-start lg:text-start'
          title={t('where-innovation-meets-opportunity')}
          description={t('what-we-offer-description')}
          tag={t('what-we-offer')}
        />
      </ScrollReveal>
      <ScrollReveal className='col-span-2 max-lg:mt-10 lg:col-span-1 lg:!items-end'>
        <WhatWeOfferCards />
      </ScrollReveal>
    </Section>
  );
}

export const WhatWeOfferCards = () => {
  const [activeCard, setActiveCard] = useState(0);
  const t = useTranslations('home');
  const cards = [
    {
      title: t('technical-excellence'),
      description: t('technical-excellence-description'),
      icon: <TechnicalExcellenceIcon />,
    },
    {
      title: t('innovation-leadership'),
      description: t('innovation-leadership-description'),
      icon: <InnovationLeadershipIcon />,
    },
    {
      title: t('business-impact'),
      description: t('business-impact-description'),
      icon: <BusinessImpactIcon />,
    },
    {
      title: t('future-ready-solutions'),
      description: t('future-ready-solutions-description'),
      icon: <FutureReadySolutionIcon />,
    },
    {
      title: t('global-exposure'),
      description: t('global-exposure-description'),
      icon: <GlobalExposureIcon />,
    },
    {
      title: t('career-growth'),
      description: t('career-growth-description'),
      icon: <CareerGrowthIcon />,
    },
  ];
  const [isPlaying, setIsPlaying] = useState(true);
  const lastTimestampRef = useRef<number | null>(null);
  const progressRef = useRef(0);
  const [animatedNumber, setAnimatedNumber] = useState(0);

  const sectionRef = useRef<HTMLDivElement>(null);

  const isInView = useInView(sectionRef);

  useEffect(() => {
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!lastTimestampRef.current) lastTimestampRef.current = timestamp;

      progressRef.current += (timestamp - lastTimestampRef.current) / 5000;
      setAnimatedNumber((progressRef.current * 10) % 10);
      lastTimestampRef.current = timestamp;

      animationFrame = requestAnimationFrame(animate);
    };

    if (isPlaying && isInView) {
      animationFrame = requestAnimationFrame(animate);
    } else {
      lastTimestampRef.current = null;
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isPlaying, isInView]);
  useEffect(() => {
    // Reset animation when slide changes
    progressRef.current = 0;
    setAnimatedNumber(0);
  }, [activeCard]);
  useEffect(() => {
    if (Math.floor(animatedNumber) === 9) {
      setActiveCard(activeCard === cards.length - 1 ? 0 : activeCard + 1);
    }
  }, [Math.floor(animatedNumber)]);
  return (
    <div ref={sectionRef} className='flex flex-col gap-4'>
      <div className='relative h-[440px] w-[350px] sm:w-[465px]'>
        {cards.map((card, index) => (
          <motion.div
            onClick={() => index !== activeCard && setActiveCard(index)}
            key={index}
            className='absolute flex h-[440px] w-full flex-col gap-2 rounded-2xl will-change-transform'
            initial={{}}
            animate={{
              zIndex: 5 - (index - activeCard),
              scale: 1 - 0.08 * (index - activeCard),
              opacity: index < activeCard ? 0 : 1 - 0.2 * (index - activeCard),
              display: index >= activeCard ? 'block' : 'none',
              bottom: index >= activeCard ? 40 * (index - activeCard) : 0,
              x: index < activeCard ? 200 : 0,
              filter: index < activeCard ? 'blur(10px)' : 'blur(0px)',
              cursor: index === activeCard ? 'default' : 'pointer',
              rotate: index < activeCard ? 10 : 0,
            }}
            transition={{
              duration: 0.5,
              display: { delay: index >= activeCard ? 0 : 0.5 },
              x: { duration: index < activeCard ? 0.5 : 0 },
              rotate: { duration: index < activeCard ? 0.5 : 0 },
            }}
          >
            <Glasscard
              autoRotate={index === activeCard}
              borderColor='#FFFFFF63'
              isShadow={index === activeCard}
              className='justify-center/80 relative flex h-full w-full items-center !bg-[#011931]/90 p-10 text-center !backdrop-blur-none'
            >
              <div className='relative flex h-full w-full flex-col items-center justify-center gap-4'>
                <AnimatePresence>
                  {index === activeCard && (
                    <motion.div
                      key={card.title}
                      initial={{ display: 'none' }}
                      animate={{ display: 'block' }}
                      exit={{ display: 'none' }}
                      transition={{ duration: 0.5 }}
                    >
                      {card.icon}
                    </motion.div>
                  )}
                </AnimatePresence>
                <motion.h3
                  animate={{
                    filter: index !== activeCard ? 'blur(10px)' : 'blur(0px)',
                  }}
                  className='text-2xl font-bold md:text-3xl'
                >
                  {card.title}
                </motion.h3>
                <motion.p
                  animate={{
                    filter: index !== activeCard ? 'blur(10px)' : 'blur(0px)',
                  }}
                  className='text-sm text-gray-50 md:text-base'
                >
                  {card.description}
                </motion.p>
              </div>
            </Glasscard>
          </motion.div>
        ))}
      </div>
      <div className='flex w-full items-center justify-between'>
        <div className='flex gap-2'>
          <CarouselButton
            direction='left'
            onClick={() =>
              setActiveCard(activeCard === 0 ? cards.length - 1 : activeCard - 1)
            }
          />
          <CarouselButton
            direction='right'
            onClick={() =>
              setActiveCard(activeCard === cards.length - 1 ? 0 : activeCard + 1)
            }
          />
        </div>
        <div className='text-base text-white'>
          <AnimatePresence mode='popLayout'>
            <motion.span
              key={activeCard}
              initial={{ opacity: 0, filter: 'blur(10px)' }}
              animate={{ opacity: 1, filter: 'blur(0px)' }}
              exit={{ opacity: 0, filter: 'blur(10px)' }}
              transition={{ duration: 0.5 }}
            >
              {activeCard + 1}
            </motion.span>
          </AnimatePresence>
          /{cards.length}
        </div>
      </div>
    </div>
  );
};
