'use client';
import DotIcon from './icons/DotIcon';
import { Autoplay, Navigation, Pagination } from 'swiper/modules';

import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import { useTranslations } from 'next-intl';
import { Swiper, SwiperRef, SwiperSlide, useSwiper } from 'swiper/react';
import { useState, useRef, useCallback } from 'react';
import { CarouselButton } from '@/components/common/CarouselButton';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { motion } from 'framer-motion';
import GalleryDialog from '@/components/layout/GalleryDialog';
export default function InnovationInActionSection() {
  const t = useTranslations('home');
  const [currentIndex, setCurrentIndex] = useState(0);
  const sliderRef = useRef<SwiperRef>(null);
  const handlePrev = useCallback(() => {
    if (!sliderRef.current?.swiper) return;
    sliderRef.current.swiper.slidePrev();
  }, []);

  const handleNext = useCallback(() => {
    if (!sliderRef.current?.swiper) return;
    sliderRef.current.swiper.slideNext();
  }, []);
  const [openIndex, setOpenIndex] = useState('');
  const handleOpen = (index: number) => {
    setOpenIndex(`/home/<USER>/${(index % 3) + 1}.mp4`);
  };
  return (
    <>
      <GalleryDialog
        type='video'
        isOpen={openIndex !== ''}
        onOpenChange={(isOpen: boolean) => {
          if (!isOpen) setOpenIndex('');
        }}
        src={openIndex}
      />
      <Section center className='gap-10 py-0 md:py-20 lg:py-40'>
        <HeroTitle
          tag={t('innovation-in-action-tag')}
          title={t('innovation-in-action-title')}
          description={t('innovation-in-action-description')}
          descriptionClassName=' max-w-[762px]  !opacity-60'
        />
        <ScrollReveal className='relative container flex h-[300px] items-center sm:mt-6 md:mt-10 md:h-[380px] lg:mt-16'>
          <div className='-mt-10 scale-50 sm:scale-60 md:scale-80 lg:scale-100'>
            <DotIcon indexNumber={currentIndex} />
          </div>
          <div className='absolute top-1/2 left-24 z-[1] w-screen -translate-y-1/2 md:left-28 lg:left-40'>
            <Swiper
              modules={[Pagination, Navigation, Autoplay]}
              ref={sliderRef}
              speed={1000}
              slidesPerView={'auto'}
              spaceBetween={25}
              loop={true}
              autoplay={{ delay: 3000 }}
              className='px-20'
              onRealIndexChange={(swiper) => setCurrentIndex(swiper.realIndex)}
              onInit={(swiper) => setCurrentIndex(swiper.realIndex)}
              onReachEnd={(swiper) => {
                setTimeout(() => {
                  swiper.slideNext(300);
                }, 500);
              }}
            >
              {[...Array(15)].map((_, index) => (
                <SwiperSlide key={index} className='!w-fit'>
                  <motion.div
                    initial={{
                      opacity: 0.8,
                    }}
                    whileHover={{
                      opacity: 1,
                    }}
                    transition={{
                      duration: 0.3,
                      ease: 'easeInOut',
                    }}
                    className='cursor-pointer'
                    onClick={() => handleOpen(index)}
                  >
                    <div className='absolute inset-0 z-10 size-full'></div>
                    <div className='absolute inset-0 z-10 flex size-full items-center justify-center'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        width='64'
                        height='64'
                        viewBox='0 0 64 64'
                        fill='none'
                      >
                        <g data-figma-bg-blur-radius='25.6'>
                          <path
                            d='M32 0C49.6731 0 64 14.3269 64 32C64 49.6731 49.6731 64 32 64C14.3269 64 0 49.6731 0 32C0 14.3269 14.3269 0 32 0ZM27 20.2627C25.6667 19.5174 24 20.449 24 21.9395V42.0605C24 43.551 25.6667 44.4826 27 43.7373L45 33.6768C46.3333 32.9315 46.3333 31.0685 45 30.3232L27 20.2627Z'
                            fill='white'
                            fillOpacity='0.89'
                          />
                        </g>
                        <defs>
                          <clipPath
                            id='bgblur_0_191_2166_clip_path'
                            transform='translate(25.6 25.6)'
                          >
                            <path d='M0 32C0 14.3269 14.3269 0 32 0V0C49.6731 0 64 14.3269 64 32V32C64 49.6731 49.6731 64 32 64V64C14.3269 64 0 49.6731 0 32V32Z' />
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                    <video
                      src={`/home/<USER>/${(index % 3) + 1}.mp4`}
                      muted
                      loop
                      className='aspect-[588/330] w-[300px] bg-blue-400 opacity-80 md:w-[500px] lg:w-[588px]'
                    />
                  </motion.div>
                </SwiperSlide>
              ))}{' '}
              <div className='flex gap-2 py-6 pt-2'>
                <CarouselButton direction='left' onClick={handlePrev} />
                <CarouselButton direction='right' onClick={handleNext} />
              </div>
            </Swiper>
          </div>
        </ScrollReveal>
      </Section>{' '}
    </>
  );
}
