'use client';
import HeroTitle from '@/components/common/HeroTitle';
import { Section } from '@/components/common/Section';
import ScrollReveal from '@/components/motion/ScrollReveal';
import { getAssetUrl } from '@/utils/url/asset';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import Link from 'next/link';

export default function PartnersSection({ partners }: { partners: any }) {
  const images = partners?.data?.map((partner: any) => ({
    url: getAssetUrl(partner.asset.filename_disk),
    width: 80,
    height: 30,
    click_url: partner.click_url,
  }));

  const t = useTranslations('home');
  return (
    <Section container center className='py-20 md:py-32 lg:py-40'>
      <HeroTitle
        title={t('partners-title')}
        description={t('partners-description')}
        descriptionClassName='max-w-[700px]'
        titleClassName='max-md:max-w-[300px]'
      />
      <div className='mt-20 !bg-transparent'>
        <ScrollReveal className='flex flex-wrap justify-center lg:grid lg:grid-cols-5'>
          {images?.map((image: any, index: number) =>
            image.click_url ? (
              <Link href={image.click_url} key={index} target='_blank'>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className='flex size-[100px] items-center justify-center border border-[#FFFFFF66] p-3 px-4 duration-300 md:size-[200px]'
                >
                  <Image
                    src={image.url}
                    alt={`Partner logo`}
                    unoptimized
                    width={600}
                    height={600}
                    className='h-15 w-28 object-contain'
                  />
                </motion.div>
              </Link>
            ) : (
              <motion.div
                key={index}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className='flex size-[100px] items-center justify-center border border-[#FFFFFF66] p-3 px-4 duration-300 md:size-[200px]'
              >
                <Image
                  src={image.url}
                  alt={`Partner logo`}
                  unoptimized
                  width={600}
                  height={600}
                  className='h-15 w-28 object-contain'
                />
              </motion.div>
            )
          )}
        </ScrollReveal>
      </div>
    </Section>
  );
}
