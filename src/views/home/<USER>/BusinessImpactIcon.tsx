import { motion } from 'framer-motion';

export default function BusinessImpactIcon() {
  return (
    <div className='relative'>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='217'
        height='202'
        viewBox='0 0 217 202'
        fill='none'
      >
        <foreignObject x='-15.927' y='-16.7454' width='224.616' height='224.613'>
          <div
            style={{
              backdropFilter: 'blur(12.73px)',
              clipPath: 'url(#bgblur_0_747_32690_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <motion.path
          animate={{ x: [0, -8, 0], y: [0, 10, 0] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
          data-figma-bg-blur-radius='25.4583'
          d='M111.427 10.7545L41.0064 29.6235C15.3443 36.4996 4.7002 54.9338 11.5757 80.5935L30.425 150.94C37.3005 176.6 55.7356 187.242 81.3977 180.366L151.818 161.497C177.48 154.621 188.057 136.205 181.182 110.545L162.332 40.1984C155.457 14.5387 137.089 3.87833 111.427 10.7545Z'
          fill='url(#paint0_linear_747_32690)'
        />
        <g opacity='0.5' filter='url(#filter1_f_747_32690)'>
          <path
            d='M146.127 102.945L133.339 54.5037C128.701 36.933 117.091 29.0544 101.827 33.0842L50.1267 46.7326C34.6117 50.8285 28.604 63.3577 33.2425 80.9285L46.0306 129.369C50.7439 147.223 62.0767 154.866 77.5917 150.77L129.292 137.121C144.556 133.092 150.841 120.799 146.127 102.945Z'
            fill='#FF624D'
          />
        </g>
        <foreignObject x='26.797' y='12.6065' width='218.726' height='218.727'>
          <div
            style={{
              backdropFilter: 'blur(14.69px)',
              clipPath: 'url(#bgblur_1_747_32690_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g data-figma-bg-blur-radius='29.3749'>
          <mask
            id='path-3-outside-1_747_32690'
            maskUnits='userSpaceOnUse'
            x='56.1289'
            y='41.9399'
            width='161'
            height='161'
            fill='black'
          >
            <rect fill='white' x='56.1289' y='41.9399' width='161' height='161' />
            <path d='M172.642 43.9399H99.7449C73.18 43.9399 58.1289 58.9911 58.1289 85.556V158.384C58.1289 184.949 73.18 200 99.7449 200H172.642C199.207 200 214.189 184.949 214.189 158.384V85.556C214.189 58.9911 199.207 43.9399 172.642 43.9399Z' />
          </mask>
          <motion.path
            d='M172.642 43.9399H99.7449C73.18 43.9399 58.1289 58.9911 58.1289 85.556V158.384C58.1289 184.949 73.18 200 99.7449 200H172.642C199.207 200 214.189 184.949 214.189 158.384V85.556C214.189 58.9911 199.207 43.9399 172.642 43.9399Z'
            fill='#FFAC95'
            animate={{ fillOpacity: [0.3, 0.5, 0.3] }}
            transition={{
              duration: 4,
              repeat: Infinity,
            }}
          />
          <path
            d='M172.642 45.8983C173.724 45.8983 174.601 45.0215 174.601 43.9399C174.601 42.8584 173.724 41.9816 172.642 41.9816V45.8983ZM172.642 41.9816C171.561 41.9816 170.684 42.8584 170.684 43.9399C170.684 45.0215 171.561 45.8983 172.642 45.8983V41.9816ZM172.642 43.9399V41.9816H99.7449V43.9399V45.8983H172.642V43.9399ZM99.7449 43.9399V41.9816C86.0984 41.9816 75.1379 45.8538 67.5903 53.4014C60.0428 60.9489 56.1706 71.9094 56.1706 85.556H58.1289H60.0872C60.0872 72.6376 63.7406 62.7901 70.3598 56.1709C76.9791 49.5516 86.8265 45.8983 99.7449 45.8983V43.9399ZM58.1289 85.556H56.1706V158.384H58.1289H60.0872V85.556H58.1289ZM58.1289 158.384H56.1706C56.1706 172.03 60.0428 182.991 67.5903 190.539C75.1379 198.086 86.0984 201.958 99.7449 201.958V200V198.042C86.8265 198.042 76.9791 194.388 70.3598 187.769C63.7406 181.15 60.0872 171.302 60.0872 158.384H58.1289ZM99.7449 200V201.958H172.642V200V198.042H99.7449V200ZM172.642 200V201.958C186.289 201.958 197.233 198.086 204.764 190.537C212.294 182.988 216.147 172.028 216.147 158.384H214.189H212.231C212.231 171.305 208.593 181.152 201.991 187.771C195.39 194.388 185.561 198.042 172.642 198.042V200ZM214.189 158.384H216.147V85.556H214.189H212.231V158.384H214.189ZM214.189 85.556H216.147C216.147 71.9119 212.294 60.9515 204.764 53.4031C197.233 45.8539 186.289 41.9816 172.642 41.9816V43.9399V45.8983C185.561 45.8983 195.39 49.5515 201.991 56.1692C208.593 62.7876 212.231 72.6352 212.231 85.556H214.189Z'
            fill='url(#paint1_linear_747_32690)'
            mask='url(#path-3-outside-1_747_32690)'
          />
        </g>
        <foreignObject x='64.1993' y='46.4718' width='143.996' height='150.931'>
          <div
            style={{
              backdropFilter: 'blur(14.69px)',
              clipPath: 'url(#bgblur_2_747_32690_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g filter='url(#filter3_d_747_32690)' data-figma-bg-blur-radius='29.3749'>
          <mask id='path-5-inside-2_747_32690' fill='white'>
            <path
              fillRule='evenodd'
              clipRule='evenodd'
              d='M136.437 75.8467C132.9 75.8467 129.987 78.7598 129.987 82.3665V161.576C129.987 165.113 132.9 168.026 136.437 168.026C140.044 168.026 142.957 165.113 142.957 161.576V82.3665C142.957 78.7598 140.044 75.8467 136.437 75.8467ZM100.025 101.443C96.4873 101.443 93.5742 104.356 93.5742 107.962V161.578C93.5742 165.115 96.4873 168.028 100.025 168.028C103.631 168.028 106.545 165.115 106.545 161.578V107.962C106.545 104.356 103.631 101.443 100.025 101.443ZM165.849 136.261C165.849 132.654 168.762 129.741 172.369 129.741C175.906 129.741 178.819 132.654 178.819 136.261V161.577C178.819 165.114 175.906 168.028 172.299 168.028C168.762 168.028 165.849 165.114 165.849 161.577V136.261Z'
            />
          </mask>
          <path
            fillRule='evenodd'
            clipRule='evenodd'
            d='M136.437 75.8467C132.9 75.8467 129.987 78.7598 129.987 82.3665V161.576C129.987 165.113 132.9 168.026 136.437 168.026C140.044 168.026 142.957 165.113 142.957 161.576V82.3665C142.957 78.7598 140.044 75.8467 136.437 75.8467ZM100.025 101.443C96.4873 101.443 93.5742 104.356 93.5742 107.962V161.578C93.5742 165.115 96.4873 168.028 100.025 168.028C103.631 168.028 106.545 165.115 106.545 161.578V107.962C106.545 104.356 103.631 101.443 100.025 101.443ZM165.849 136.261C165.849 132.654 168.762 129.741 172.369 129.741C175.906 129.741 178.819 132.654 178.819 136.261V161.577C178.819 165.114 175.906 168.028 172.299 168.028C168.762 168.028 165.849 165.114 165.849 161.577V136.261Z'
            fill='url(#paint2_linear_747_32690)'
          />
          <path
            d='M129.987 82.3665H130.77C130.77 79.187 133.338 76.63 136.437 76.63V75.8467V75.0633C132.462 75.0633 129.204 78.3326 129.204 82.3665H129.987ZM129.987 161.576H130.77V82.3665H129.987H129.204V161.576H129.987ZM136.437 168.026V167.243C133.333 167.243 130.77 164.68 130.77 161.576H129.987H129.204C129.204 165.546 132.467 168.809 136.437 168.809V168.026ZM142.957 161.576H142.174C142.174 164.675 139.617 167.243 136.437 167.243V168.026V168.809C140.471 168.809 143.741 165.551 143.741 161.576H142.957ZM142.957 82.3665H142.174V161.576H142.957H143.741V82.3665H142.957ZM136.437 75.8467V76.63C139.611 76.63 142.174 79.1924 142.174 82.3665H142.957H143.741C143.741 78.3272 140.477 75.0633 136.437 75.0633V75.8467ZM93.5742 107.962H94.3575C94.3575 104.783 96.9254 102.226 100.025 102.226V101.443V100.659C96.0493 100.659 92.7909 103.929 92.7909 107.962H93.5742ZM93.5742 161.578H94.3575V107.962H93.5742H92.7909V161.578H93.5742ZM100.025 168.028V167.245C96.92 167.245 94.3575 164.682 94.3575 161.578H93.5742H92.7909C92.7909 165.548 96.0547 168.812 100.025 168.812V168.028ZM106.545 161.578H105.761C105.761 164.677 103.204 167.245 100.025 167.245V168.028V168.812C104.059 168.812 107.328 165.553 107.328 161.578H106.545ZM106.545 107.962H105.761V161.578H106.545H107.328V107.962H106.545ZM100.025 101.443V102.226C103.199 102.226 105.761 104.788 105.761 107.962H106.545H107.328C107.328 103.923 104.064 100.659 100.025 100.659V101.443ZM172.369 129.741V128.958C168.329 128.958 165.065 132.221 165.065 136.261H165.849H166.632C166.632 133.087 169.194 130.524 172.369 130.524V129.741ZM178.819 136.261H179.602C179.602 132.227 176.344 128.958 172.369 128.958V129.741V130.524C175.468 130.524 178.036 133.081 178.036 136.261H178.819ZM178.819 161.577H179.602V136.261H178.819H178.036V161.577H178.819ZM172.299 168.028V168.811C176.333 168.811 179.602 165.553 179.602 161.577H178.819H178.036C178.036 164.676 175.479 167.244 172.299 167.244V168.028ZM165.849 161.577H165.065C165.065 165.547 168.329 168.811 172.299 168.811V168.028V167.244C169.194 167.244 166.632 164.682 166.632 161.577H165.849ZM165.849 136.261H165.065V161.577H165.849H166.632V136.261H165.849Z'
            fill='url(#paint3_linear_747_32690)'
            mask='url(#path-5-inside-2_747_32690)'
          />
        </g>
        <defs>
          <clipPath
            id='bgblur_0_747_32690_clip_path'
            transform='translate(15.927 16.7454)'
          >
            <path d='M111.427 10.7545L41.0064 29.6235C15.3443 36.4996 4.7002 54.9338 11.5757 80.5935L30.425 150.94C37.3005 176.6 55.7356 187.242 81.3977 180.366L151.818 161.497C177.48 154.621 188.057 136.205 181.182 110.545L162.332 40.1984C155.457 14.5387 137.089 3.87833 111.427 10.7545Z' />
          </clipPath>
          <filter
            id='filter1_f_747_32690'
            x='6.25659'
            y='6.59204'
            width='166.866'
            height='170.716'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='BackgroundImageFix'
              result='shape'
            />
            <feGaussianBlur
              stdDeviation='12.7291'
              result='effect1_foregroundBlur_747_32690'
            />
          </filter>
          <clipPath
            id='bgblur_1_747_32690_clip_path'
            transform='translate(-26.797 -12.6065)'
          >
            <path d='M172.642 43.9399H99.7449C73.18 43.9399 58.1289 58.9911 58.1289 85.556V158.384C58.1289 184.949 73.18 200 99.7449 200H172.642C199.207 200 214.189 184.949 214.189 158.384V85.556C214.189 58.9911 199.207 43.9399 172.642 43.9399Z' />
          </clipPath>
          <filter
            id='filter3_d_747_32690'
            x='64.1993'
            y='46.4718'
            width='143.996'
            height='150.931'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='9.79164' dy='9.79164' />
            <feGaussianBlur stdDeviation='9.79164' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 1 0 0 0 0 0.447059 0 0 0 0 0.368627 0 0 0 0.5 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_747_32690'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_747_32690'
              result='shape'
            />
          </filter>
          <clipPath
            id='bgblur_2_747_32690_clip_path'
            transform='translate(-64.1993 -46.4718)'
          >
            <path
              fillRule='evenodd'
              clipRule='evenodd'
              d='M136.437 75.8467C132.9 75.8467 129.987 78.7598 129.987 82.3665V161.576C129.987 165.113 132.9 168.026 136.437 168.026C140.044 168.026 142.957 165.113 142.957 161.576V82.3665C142.957 78.7598 140.044 75.8467 136.437 75.8467ZM100.025 101.443C96.4873 101.443 93.5742 104.356 93.5742 107.962V161.578C93.5742 165.115 96.4873 168.028 100.025 168.028C103.631 168.028 106.545 165.115 106.545 161.578V107.962C106.545 104.356 103.631 101.443 100.025 101.443ZM165.849 136.261C165.849 132.654 168.762 129.741 172.369 129.741C175.906 129.741 178.819 132.654 178.819 136.261V161.577C178.819 165.114 175.906 168.028 172.299 168.028C168.762 168.028 165.849 165.114 165.849 161.577V136.261Z'
            />
          </clipPath>
          <linearGradient
            id='paint0_linear_747_32690'
            x1='110.182'
            y1='102.69'
            x2='-5.0221'
            y2='202.596'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#FFA78F' />
            <stop offset='1' stopColor='#F23E2C' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_747_32690'
            x1='82.9711'
            y1='62.1211'
            x2='182.521'
            y2='185.416'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_747_32690'
            x1='71.5087'
            y1='144.266'
            x2='111.441'
            y2='226.301'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' />
            <stop offset='1' stopColor='white' stopOpacity='0.2' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_747_32690'
            x1='107.144'
            y1='86.586'
            x2='166.747'
            y2='154.851'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
        </defs>
      </svg>
      <div className='absolute top-[66px] right-[8px] z-10'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='126'
          height='132'
          viewBox='0 0 126 132'
          fill='none'
        >
          <g filter='url(#filter0_d_747_32696)' data-figma-bg-blur-radius='29.3749'>
            <mask id='path-1-inside-1_747_32696' fill='white'>
              <path
                fill-rule='evenodd'
                clip-rule='evenodd'
                d='M53.4373 9.84668C49.9 9.84668 46.9869 12.7598 46.9869 16.3665V95.5757C46.9869 99.113 49.9 102.026 53.4373 102.026C57.0441 102.026 59.9572 99.113 59.9572 95.5757V16.3665C59.9572 12.7598 57.0441 9.84668 53.4373 9.84668ZM17.0247 35.4426C13.4873 35.4426 10.5742 38.3557 10.5742 41.9625V95.5778C10.5742 99.1151 13.4873 102.028 17.0247 102.028C20.6314 102.028 23.5445 99.1151 23.5445 95.5778V41.9625C23.5445 38.3557 20.6314 35.4426 17.0247 35.4426ZM82.8487 70.2607C82.8487 66.654 85.7619 63.7408 89.3686 63.7408C92.906 63.7408 95.8191 66.654 95.8191 70.2607V95.5771C95.8191 99.1144 92.906 102.028 89.2992 102.028C85.7619 102.028 82.8487 99.1144 82.8487 95.5771V70.2607Z'
              />
            </mask>
            <path
              fill-rule='evenodd'
              clip-rule='evenodd'
              d='M53.4373 9.84668C49.9 9.84668 46.9869 12.7598 46.9869 16.3665V95.5757C46.9869 99.113 49.9 102.026 53.4373 102.026C57.0441 102.026 59.9572 99.113 59.9572 95.5757V16.3665C59.9572 12.7598 57.0441 9.84668 53.4373 9.84668ZM17.0247 35.4426C13.4873 35.4426 10.5742 38.3557 10.5742 41.9625V95.5778C10.5742 99.1151 13.4873 102.028 17.0247 102.028C20.6314 102.028 23.5445 99.1151 23.5445 95.5778V41.9625C23.5445 38.3557 20.6314 35.4426 17.0247 35.4426ZM82.8487 70.2607C82.8487 66.654 85.7619 63.7408 89.3686 63.7408C92.906 63.7408 95.8191 66.654 95.8191 70.2607V95.5771C95.8191 99.1144 92.906 102.028 89.2992 102.028C85.7619 102.028 82.8487 99.1144 82.8487 95.5771V70.2607Z'
              fill='url(#paint0_linear_747_32696)'
            />
            <path
              d='M46.9869 16.3665H47.7702C47.7702 13.187 50.338 10.63 53.4373 10.63V9.84668V9.06335C49.4619 9.06335 46.2035 12.3326 46.2035 16.3665H46.9869ZM46.9869 95.5757H47.7702V16.3665H46.9869H46.2035V95.5757H46.9869ZM53.4373 102.026V101.243C50.3326 101.243 47.7702 98.6804 47.7702 95.5757H46.9869H46.2035C46.2035 99.5456 49.4674 102.809 53.4373 102.809V102.026ZM59.9572 95.5757H59.1739C59.1739 98.6749 56.6169 101.243 53.4373 101.243V102.026V102.809C57.4712 102.809 60.7405 99.5511 60.7405 95.5757H59.9572ZM59.9572 16.3665H59.1739V95.5757H59.9572H60.7405V16.3665H59.9572ZM53.4373 9.84668V10.63C56.6114 10.63 59.1739 13.1924 59.1739 16.3665H59.9572H60.7405C60.7405 12.3272 57.4767 9.06335 53.4373 9.06335V9.84668ZM10.5742 41.9625H11.3575C11.3575 38.7829 13.9254 36.226 17.0247 36.226V35.4426V34.6593C13.0493 34.6593 9.79089 37.9286 9.79089 41.9625H10.5742ZM10.5742 95.5778H11.3575L11.3575 41.9625H10.5742H9.79089L9.79089 95.5778H10.5742ZM17.0247 102.028V101.245C13.92 101.245 11.3575 98.6825 11.3575 95.5778H10.5742H9.79089C9.79089 99.5477 13.0547 102.812 17.0247 102.812V102.028ZM23.5445 95.5778H22.7612C22.7612 98.6771 20.2043 101.245 17.0247 101.245V102.028V102.812C21.0586 102.812 24.3279 99.5532 24.3279 95.5778H23.5445ZM23.5445 41.9625H22.7612V95.5778H23.5445H24.3279V41.9625H23.5445ZM17.0247 35.4426V36.226C20.1988 36.226 22.7612 38.7884 22.7612 41.9625H23.5445H24.3279C24.3279 37.9231 21.064 34.6593 17.0247 34.6593V35.4426ZM89.3686 63.7408V62.9575C85.3292 62.9575 82.0654 66.2213 82.0654 70.2607H82.8487H83.6321C83.6321 67.0866 86.1945 64.5242 89.3686 64.5242V63.7408ZM95.8191 70.2607H96.6024C96.6024 66.2268 93.344 62.9575 89.3686 62.9575V63.7408V64.5242C92.4679 64.5242 95.0358 67.0811 95.0358 70.2607H95.8191ZM95.8191 95.5771H96.6024V70.2607H95.8191H95.0358V95.5771H95.8191ZM89.2992 102.028V102.811C93.3331 102.811 96.6024 99.5525 96.6024 95.5771H95.8191H95.0358C95.0358 98.6764 92.4788 101.244 89.2992 101.244V102.028ZM82.8487 95.5771H82.0654C82.0654 99.5471 85.3292 102.811 89.2992 102.811V102.028V101.244C86.1945 101.244 83.6321 98.6818 83.6321 95.5771H82.8487ZM82.8487 70.2607H82.0654V95.5771H82.8487H83.6321V70.2607H82.8487Z'
              fill='url(#paint1_linear_747_32696)'
              mask='url(#path-1-inside-1_747_32696)'
            />
          </g>
          <defs>
            <filter
              id='filter0_d_747_32696'
              x='-18.8007'
              y='-19.5282'
              width='143.996'
              height='150.931'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='9.79164' dy='9.79164' />
              <feGaussianBlur stdDeviation='9.79164' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 1 0 0 0 0 0.447059 0 0 0 0 0.368627 0 0 0 0.5 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_747_32696'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_747_32696'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_0_747_32696_clip_path'
              transform='translate(18.8007 19.5282)'
            >
              <path
                fillRule='evenodd'
                clipRule='evenodd'
                d='M53.4373 9.84668C49.9 9.84668 46.9869 12.7598 46.9869 16.3665V95.5757C46.9869 99.113 49.9 102.026 53.4373 102.026C57.0441 102.026 59.9572 99.113 59.9572 95.5757V16.3665C59.9572 12.7598 57.0441 9.84668 53.4373 9.84668ZM17.0247 35.4426C13.4873 35.4426 10.5742 38.3557 10.5742 41.9625V95.5778C10.5742 99.1151 13.4873 102.028 17.0247 102.028C20.6314 102.028 23.5445 99.1151 23.5445 95.5778V41.9625C23.5445 38.3557 20.6314 35.4426 17.0247 35.4426ZM82.8487 70.2607C82.8487 66.654 85.7619 63.7408 89.3686 63.7408C92.906 63.7408 95.8191 66.654 95.8191 70.2607V95.5771C95.8191 99.1144 92.906 102.028 89.2992 102.028C85.7619 102.028 82.8487 99.1144 82.8487 95.5771V70.2607Z'
              />
            </clipPath>
            <linearGradient
              id='paint0_linear_747_32696'
              x1='-11.4913'
              y1='78.2657'
              x2='28.4406'
              y2='160.301'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' />
              <stop offset='1' stopColor='white' stopOpacity='0.2' />
            </linearGradient>
            <linearGradient
              id='paint1_linear_747_32696'
              x1='24.1438'
              y1='20.586'
              x2='83.7469'
              y2='88.8508'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' stopOpacity='0.25' />
              <stop offset='1' stopColor='white' stopOpacity='0' />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
}
