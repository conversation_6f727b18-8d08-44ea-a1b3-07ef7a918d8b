import { motion } from 'framer-motion';

export default function CareerGrowthIcon() {
  return (
    <div className='relative'>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='221'
        height='203'
        viewBox='0 0 221 203'
        fill='none'
      >
        <motion.path
          d='M109.354 10.5517L40.2582 29.0658C15.0787 35.8126 4.63476 53.9001 11.381 79.0773L29.8757 148.101C36.6219 173.278 54.7104 183.72 79.8899 176.973L148.986 158.459C174.165 151.712 184.543 133.642 177.797 108.465L159.302 39.4418C152.556 14.2647 134.533 3.80483 109.354 10.5517Z'
          fill='url(#paint0_linear_747_32731)'
          animate={{ x: [-10, 4, -10], rotate: [0, 10, 0] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
        />
        <g opacity='0.5' filter='url(#filter0_f_747_32731)'>
          <path
            d='M104.981 41.1249L59.3106 53.3621C42.6678 57.8215 35.6428 69.3218 39.8866 85.16L51.5212 128.581C55.765 144.419 67.599 150.866 84.2418 146.407L129.912 134.169C146.555 129.71 153.536 118.221 149.292 102.383L137.658 58.9623C133.414 43.1241 121.623 36.6654 104.981 41.1249Z'
            fill='#E31352'
          />
        </g>
        <foreignObject x='-4.05656' y='-4.86418' width='256.605' height='256.604'>
          <div
            style={{
              backdropFilter: 'blur(24.83px)',
              clipPath: 'url(#bgblur_0_747_32731_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <motion.path
          data-figma-bg-blur-radius='49.6698'
          d='M157.475 45.8398C170.703 45.8399 181.818 50.49 189.623 58.6592C197.424 66.8242 201.844 78.4257 201.844 92.2002V154.752C201.844 168.489 197.423 180.071 189.622 188.227C181.817 196.386 170.703 201.035 157.475 201.035H91.0947C77.8663 201.035 66.7331 196.386 58.9092 188.228C51.089 180.073 46.6485 168.49 46.6484 154.752V92.2002C46.6484 78.4243 51.0889 66.8228 58.9092 58.6582C66.7332 50.4898 77.8662 45.8398 91.0947 45.8398H157.475Z'
          fill='#FF749F'
          fillOpacity='0.35'
          stroke='url(#paint1_linear_747_32731)'
          strokeWidth='2.06958'
          strokeLinecap='round'
          strokeLinejoin='round'
          animate={{ fillOpacity: [0.25, 0.5, 0.25] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
        />
        <foreignObject x='166.636' y='4.58184' width='84.5873' height='84.5873'>
          <div
            style={{
              backdropFilter: 'blur(15.52px)',
              clipPath: 'url(#bgblur_1_747_32731_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <path
          data-figma-bg-blur-radius='31.0437'
          d='M208.93 35.6255C202.716 35.6255 197.68 40.6623 197.68 46.8755C197.68 53.0887 202.716 58.1255 208.93 58.1255C215.143 58.1255 220.18 53.0887 220.18 46.8755C220.18 43.8918 218.994 41.0303 216.885 38.9205C214.775 36.8108 211.913 35.6255 208.93 35.6255Z'
          fill='url(#paint2_linear_747_32731)'
        />
        <foreignObject x='52.9407' y='69.535' width='143.556' height='111.271'>
          <div
            style={{
              backdropFilter: 'blur(15.52px)',
              clipPath: 'url(#bgblur_2_747_32731_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g filter='url(#filter3_d_747_32731)' data-figma-bg-blur-radius='31.0437'>
          <path
            d='M164.31 109.878L142.429 138.113C141.509 139.34 140.128 140.141 138.604 140.331C137.081 140.521 135.545 140.085 134.35 139.121L113.345 122.651L94.427 147.121C93.0571 149.297 90.3889 150.274 87.935 149.5C85.4812 148.725 83.8605 146.394 83.9918 143.827C83.9805 142.557 84.4085 141.323 85.2036 140.331L107.825 111.156C108.734 109.938 110.096 109.138 111.604 108.936C113.111 108.733 114.637 109.146 115.836 110.08L136.841 126.617L155.288 102.887C156.175 101.663 157.521 100.851 159.018 100.636C160.515 100.42 162.036 100.82 163.232 101.744C165.705 103.678 166.183 107.227 164.31 109.744V109.878Z'
            fill='url(#paint3_linear_747_32731)'
          />
          <path
            d='M159.077 101.045C160.377 100.859 161.696 101.172 162.769 101.917L162.979 102.072C165.273 103.867 165.716 107.161 163.978 109.497L163.896 109.607V109.736L142.102 137.86L142.098 137.865C141.245 139.002 139.965 139.744 138.553 139.92C137.141 140.097 135.718 139.692 134.61 138.799L134.605 138.795L113.601 122.326L113.272 122.068L113.018 122.398L94.0996 146.868L94.0879 146.884L94.0771 146.901C92.8076 148.917 90.3342 149.823 88.0596 149.105C85.7851 148.387 84.2836 146.226 84.4053 143.848L84.4062 143.836L84.4053 143.824C84.3948 142.649 84.7909 141.507 85.5264 140.59L85.5303 140.585L108.15 111.409L108.151 111.41L108.156 111.403C108.999 110.275 110.262 109.534 111.659 109.346C113.056 109.159 114.471 109.541 115.582 110.407V110.406L136.585 126.943L136.912 127.201L137.168 126.872L155.615 103.141L155.623 103.129C156.444 101.997 157.691 101.245 159.077 101.045Z'
            stroke='url(#paint4_linear_747_32731)'
            strokeWidth='0.827831'
          />
        </g>
        <defs>
          <filter
            id='filter0_f_747_32731'
            x='3.49298'
            y='4.5628'
            width='182.198'
            height='178.408'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='BackgroundImageFix'
              result='shape'
            />
            <feGaussianBlur
              stdDeviation='17.5914'
              result='effect1_foregroundBlur_747_32731'
            />
          </filter>
          <clipPath
            id='bgblur_0_747_32731_clip_path'
            transform='translate(4.05656 4.86418)'
          >
            <path d='M157.475 45.8398C170.703 45.8399 181.818 50.49 189.623 58.6592C197.424 66.8242 201.844 78.4257 201.844 92.2002V154.752C201.844 168.489 197.423 180.071 189.622 188.227C181.817 196.386 170.703 201.035 157.475 201.035H91.0947C77.8663 201.035 66.7331 196.386 58.9092 188.228C51.089 180.073 46.6485 168.49 46.6484 154.752V92.2002C46.6484 78.4243 51.0889 66.8228 58.9092 58.6582C66.7332 50.4898 77.8662 45.8398 91.0947 45.8398H157.475Z' />
          </clipPath>
          <clipPath
            id='bgblur_1_747_32731_clip_path'
            transform='translate(-166.636 -4.58184)'
          >
            <path d='M208.93 35.6255C202.716 35.6255 197.68 40.6623 197.68 46.8755C197.68 53.0887 202.716 58.1255 208.93 58.1255C215.143 58.1255 220.18 53.0887 220.18 46.8755C220.18 43.8918 218.994 41.0303 216.885 38.9205C214.775 36.8108 211.913 35.6255 208.93 35.6255Z' />
          </clipPath>
          <filter
            id='filter3_d_747_32731'
            x='52.9407'
            y='69.535'
            width='143.556'
            height='111.271'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='10.3479' dy='10.3479' />
            <feGaussianBlur stdDeviation='10.3479' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 1 0 0 0 0 0.321569 0 0 0 0 0.54902 0 0 0 0.5 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_747_32731'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_747_32731'
              result='shape'
            />
          </filter>
          <clipPath
            id='bgblur_2_747_32731_clip_path'
            transform='translate(-52.9407 -69.535)'
          >
            <path d='M164.31 109.878L142.429 138.113C141.509 139.34 140.128 140.141 138.604 140.331C137.081 140.521 135.545 140.085 134.35 139.121L113.345 122.651L94.427 147.121C93.0571 149.297 90.3889 150.274 87.935 149.5C85.4812 148.725 83.8605 146.394 83.9918 143.827C83.9805 142.557 84.4085 141.323 85.2036 140.331L107.825 111.156C108.734 109.938 110.096 109.138 111.604 108.936C113.111 108.733 114.637 109.146 115.836 110.08L136.841 126.617L155.288 102.887C156.175 101.663 157.521 100.851 159.018 100.636C160.515 100.42 162.036 100.82 163.232 101.744C165.705 103.678 166.183 107.227 164.31 109.744V109.878Z' />
          </clipPath>
          <linearGradient
            id='paint0_linear_747_32731'
            x1='108.133'
            y1='100.758'
            x2='-4.9047'
            y2='198.785'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#FF759F' />
            <stop offset='1' stopColor='#FF196E' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_747_32731'
            x1='72.0586'
            y1='64.7143'
            x2='169.736'
            y2='185.69'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_747_32731'
            x1='210.586'
            y1='48.3822'
            x2='190.816'
            y2='57.9951'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#FF759F' />
            <stop offset='1' stopColor='#FF196E' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_747_32731'
            x1='159.676'
            y1='109.46'
            x2='69.0964'
            y2='114.352'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' />
            <stop offset='1' stopColor='white' stopOpacity='0.2' />
          </linearGradient>
          <linearGradient
            id='paint4_linear_747_32731'
            x1='96.9528'
            y1='106.309'
            x2='122.235'
            y2='158.174'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
        </defs>
      </svg>
      <div className='absolute top-[90px] right-[24px] z-10'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='124'
          height='91'
          viewBox='0 0 124 91'
          fill='none'
        >
          <g filter='url(#filter0_d_747_32739)' data-figma-bg-blur-radius='31.0437'>
            <path
              d='M91.3096 19.8783L69.4292 48.113C68.5087 49.3402 67.128 50.1409 65.6043 50.3311C64.0806 50.5213 62.545 50.0846 61.3503 49.1214L40.3451 32.6511L21.427 57.1213C20.0571 59.2967 17.3889 60.2742 14.935 59.4996C12.4812 58.725 10.8605 56.3937 10.9918 53.8272C10.9805 52.5574 11.4085 51.3226 12.2036 50.3315L34.8246 21.1555C35.7341 19.9381 37.0964 19.1379 38.6038 18.9355C40.1113 18.7331 41.6369 19.1457 42.8361 20.0799L63.8413 36.6174L82.2881 12.8868C83.175 11.6633 84.5208 10.851 86.0179 10.6357C87.515 10.4203 89.0357 10.8202 90.2324 11.7439C92.7048 13.6775 93.1827 17.2268 91.3096 19.7438V19.8783Z'
              fill='url(#paint0_linear_747_32739)'
            />
            <path
              d='M86.0771 11.0454C87.3768 10.8585 88.6958 11.1718 89.7686 11.9165L89.9795 12.0718C92.2731 13.8674 92.7158 17.1607 90.9775 19.4966L90.8955 19.6069V19.7358L69.1016 47.8599L69.0977 47.8647C68.2446 49.0019 66.965 49.7441 65.5527 49.9204C64.1406 50.0966 62.7175 49.6918 61.6104 48.7993L61.6055 48.7954L40.6006 32.3257L40.2725 32.0679L40.0176 32.3979L21.0996 56.8677L21.0879 56.8843L21.0771 56.9009C19.8076 58.9169 17.3342 59.823 15.0596 59.105C12.7851 58.387 11.2836 56.226 11.4053 53.8481L11.4062 53.8364L11.4053 53.8237C11.3948 52.6494 11.7909 51.507 12.5264 50.5903L12.5303 50.5854L35.1504 21.4087L35.1514 21.4097L35.1562 21.4028C35.9991 20.2748 37.262 19.5338 38.6592 19.3462C40.0565 19.1587 41.4706 19.5409 42.582 20.4067V20.4058L63.585 36.9429L63.9121 37.2007L64.168 36.8716L82.6152 13.1411L82.623 13.1294C83.4441 11.9967 84.6908 11.2449 86.0771 11.0454Z'
              stroke='url(#paint1_linear_747_32739)'
              strokeWidth='0.827831'
            />
          </g>
          <defs>
            <filter
              id='filter0_d_747_32739'
              x='-20.0593'
              y='-20.465'
              width='143.556'
              height='111.271'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='10.3479' dy='10.3479' />
              <feGaussianBlur stdDeviation='10.3479' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 1 0 0 0 0 0.321569 0 0 0 0 0.54902 0 0 0 0.5 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_747_32739'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_747_32739'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_0_747_32739_clip_path'
              transform='translate(20.0593 20.465)'
            >
              <path d='M91.3096 19.8783L69.4292 48.113C68.5087 49.3402 67.128 50.1409 65.6043 50.3311C64.0806 50.5213 62.545 50.0846 61.3503 49.1214L40.3451 32.6511L21.427 57.1213C20.0571 59.2967 17.3889 60.2742 14.935 59.4996C12.4812 58.725 10.8605 56.3937 10.9918 53.8272C10.9805 52.5574 11.4085 51.3226 12.2036 50.3315L34.8246 21.1555C35.7341 19.9381 37.0964 19.1379 38.6038 18.9355C40.1113 18.7331 41.6369 19.1457 42.8361 20.0799L63.8413 36.6174L82.2881 12.8868C83.175 11.6633 84.5208 10.851 86.0179 10.6357C87.515 10.4203 89.0357 10.8202 90.2324 11.7439C92.7048 13.6775 93.1827 17.2268 91.3096 19.7438V19.8783Z' />
            </clipPath>
            <linearGradient
              id='paint0_linear_747_32739'
              x1='86.6764'
              y1='19.4603'
              x2='-3.90357'
              y2='24.3521'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' />
              <stop offset='1' stopColor='white' stopOpacity='0.2' />
            </linearGradient>
            <linearGradient
              id='paint1_linear_747_32739'
              x1='23.9528'
              y1='16.3086'
              x2='49.2349'
              y2='68.1745'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' stopOpacity='0.25' />
              <stop offset='1' stopColor='white' stopOpacity='0' />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
}
