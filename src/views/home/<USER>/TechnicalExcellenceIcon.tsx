import { cn } from '@/utils/cn';
import { motion } from 'framer-motion';

export const TechnicalExcellenceIcon = ({ className = '' }: { className?: string }) => {
  return (
    <div className={cn('relative', className)}>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width={171}
        height={201}
        viewBox='0 0 171 201'
        fill='none'
      >
        <motion.path
          animate={{ x: [3, -7, 3], y: [0, 10, 0] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
          fillRule='evenodd'
          clipRule='evenodd'
          d='M104.777 38.8766L61.895 82.1603L11.6901 51.1775C5.11153 47.1169 6.44492 37.0991 13.8559 34.9424L132.388 0.36009C139.129 -1.59586 145.361 4.74052 143.343 11.5305L108.173 130.256C105.971 137.685 96.0918 138.972 92.099 132.338L61.895 82.1603'
          fill='url(#paint0_linear_781_2962)'
        />
        <g opacity='0.5' filter='url(#filter0_f_781_2962)'>
          <path
            fillRule='evenodd'
            clipRule='evenodd'
            d='M98.6312 48.6153L73.0578 74.4281L43.1174 55.9511C39.1942 53.5294 39.9893 47.5552 44.409 46.269L115.097 25.6454C119.117 24.479 122.834 28.2577 121.63 32.3071L100.656 103.11C99.3429 107.541 93.4515 108.308 91.0703 104.352L73.0578 74.4281'
            fill='#E46800'
          />
        </g>
        <foreignObject x='-23.5' y='6.55078' width='218' height='218.45'>
          <div
            style={{
              backdropFilter: 'blur(12px)',
              willChange: 'backdrop-filter',
              WebkitBackdropFilter: 'blur(12px)',
              clipPath: 'url(#bgblur_0_781_2962_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g data-figma-bg-blur-radius='24'>
          <mask
            id='path-3-outside-1_781_2962'
            maskUnits='userSpaceOnUse'
            x='0.5'
            y='29.5527'
            width='171'
            height='172'
            fill='black'
          >
            <rect fill='white' x='0.5' y='29.5527' width='171' height='172' />
            <path
              fillRule='evenodd'
              clipRule='evenodd'
              d='M121.484 79.477L68.6216 132.834L6.73267 94.6406C-1.37692 89.635 0.266794 77.2859 9.40256 74.6272L155.52 31.9966C163.83 29.5855 171.513 37.3965 169.024 45.7668L125.67 192.122C122.955 201.281 110.777 202.867 105.855 194.689L68.6216 132.834'
            />
          </mask>
          <motion.path
            animate={{ fillOpacity: [0.25, 0.5, 0.25] }}
            transition={{
              duration: 4,
              repeat: Infinity,
            }}
            fillRule='evenodd'
            clipRule='evenodd'
            d='M121.484 79.477L68.6216 132.834L6.73267 94.6406C-1.37692 89.635 0.266794 77.2859 9.40256 74.6272L155.52 31.9966C163.83 29.5855 171.513 37.3965 169.024 45.7668L125.67 192.122C122.955 201.281 110.777 202.867 105.855 194.689L68.6216 132.834'
            fill='#FFCBB9'
          />
          <path
            d='M122.194 80.1808C122.583 79.7884 122.58 79.1553 122.188 78.7666C121.795 78.3779 121.162 78.3808 120.773 78.7732L122.194 80.1808ZM68.6216 132.834L68.0964 133.685C68.4924 133.929 69.0045 133.868 69.332 133.538L68.6216 132.834ZM6.73267 94.6406L6.20742 95.4916L6.20749 95.4916L6.73267 94.6406ZM9.40256 74.6272L9.68199 75.5873L9.68264 75.5872L9.40256 74.6272ZM155.52 31.9966L155.242 31.0362L155.24 31.0366L155.52 31.9966ZM169.024 45.7668L168.066 45.4818L168.065 45.4827L169.024 45.7668ZM125.67 192.122L126.629 192.407L126.629 192.406L125.67 192.122ZM105.855 194.689L106.712 194.174L106.712 194.174L105.855 194.689ZM69.4784 132.318C69.1936 131.845 68.5791 131.692 68.1059 131.977C67.6327 132.262 67.48 132.876 67.7648 133.35L69.4784 132.318ZM121.484 79.477L120.773 78.7732L67.9112 132.13L68.6216 132.834L69.332 133.538L122.194 80.1808L121.484 79.477ZM68.6216 132.834L69.1468 131.983L7.25784 93.7896L6.73267 94.6406L6.20749 95.4916L68.0964 133.685L68.6216 132.834ZM6.73267 94.6406L7.25792 93.7897C-0.120926 89.2351 1.38587 78.0017 9.68199 75.5873L9.40256 74.6272L9.12314 73.667C-0.852278 76.57 -2.63291 90.0349 6.20742 95.4916L6.73267 94.6406ZM9.40256 74.6272L9.68264 75.5872L155.8 32.9566L155.52 31.9966L155.24 31.0366L9.12249 73.6672L9.40256 74.6272ZM155.52 31.9966L155.799 32.957C163.333 30.771 170.333 37.854 168.066 45.4818L169.024 45.7668L169.983 46.0517C172.692 36.939 164.327 28.4 155.242 31.0362L155.52 31.9966ZM169.024 45.7668L168.065 45.4827L124.711 191.838L125.67 192.122L126.629 192.406L169.983 46.0508L169.024 45.7668ZM125.67 192.122L124.711 191.838C122.241 200.171 111.181 201.6 106.712 194.174L105.855 194.689L104.998 195.205C110.372 204.134 123.669 202.392 126.629 192.407L125.67 192.122ZM105.855 194.689L106.712 194.174L69.4784 132.318L68.6216 132.834L67.7648 133.35L104.998 195.205L105.855 194.689Z'
            fill='url(#paint1_linear_781_2962)'
            mask='url(#path-3-outside-1_781_2962)'
          />
        </g>
        <foreignObject x='65.6172' y='62.0889' width='73.5508' height='73.668'>
          <div
            style={{
              backdropFilter: 'blur(7.5px)',
              willChange: 'backdrop-filter',
              WebkitBackdropFilter: 'blur(7.5px)',
              clipPath: 'url(#bgblur_1_781_2962_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g filter='url(#filter2_d_781_2962)' data-figma-bg-blur-radius='15'>
          <rect
            width='57.5633'
            height='9.9247'
            rx='4.96235'
            transform='matrix(0.706166 -0.708046 0.706166 0.708046 78.5625 115.788)'
            fill='url(#paint2_linear_781_2962)'
            style={{
              WebkitTransform:
                'matrix(0.706166 -0.708046 0.706166 0.708046 78.5625 115.788)',
              transform: 'matrix(0.706166 -0.708046 0.706166 0.708046 78.5625 115.788)',
            }}
          />
          <rect
            x='0.282466'
            width='57.1633'
            height='9.5247'
            rx='4.76235'
            transform='matrix(0.706166 -0.708046 0.706166 0.708046 78.6455 115.988)'
            stroke='url(#paint3_linear_781_2962)'
            strokeWidth='0.4'
            style={{
              WebkitTransform:
                'matrix(0.706166 -0.708046 0.706166 0.708046 78.6455 115.988)',
              transform: 'matrix(0.706166 -0.708046 0.706166 0.708046 78.6455 115.988)',
            }}
          />
        </g>
        <defs>
          <filter
            id='filter0_f_781_2962'
            x='26.5859'
            y='11.4307'
            width='109.273'
            height='109.491'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='BackgroundImageFix'
              result='shape'
            />
            <feGaussianBlur stdDeviation='7' result='effect1_foregroundBlur_781_2962' />
          </filter>
          <clipPath id='bgblur_0_781_2962_clip_path' transform='translate(23.5 -6.55078)'>
            <path
              fillRule='evenodd'
              clipRule='evenodd'
              d='M121.484 79.477L68.6216 132.834L6.73267 94.6406C-1.37692 89.635 0.266794 77.2859 9.40256 74.6272L155.52 31.9966C163.83 29.5855 171.513 37.3965 169.024 45.7668L125.67 192.122C122.955 201.281 110.777 202.867 105.855 194.689L68.6216 132.834'
            />
          </clipPath>
          <filter
            id='filter2_d_781_2962'
            x='65.6172'
            y='62.0889'
            width='73.5508'
            height='73.668'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='5' dy='5' />
            <feGaussianBlur stdDeviation='5' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 1 0 0 0 0 0.580392 0 0 0 0 0.247059 0 0 0 0.5 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_781_2962'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_781_2962'
              result='shape'
            />
          </filter>
          <clipPath
            id='bgblur_1_781_2962_clip_path'
            transform='translate(-65.6172 -62.0889)'
          >
            <rect
              width='57.5633'
              height='9.9247'
              rx='4.96235'
              transform='matrix(0.706166 -0.708046 0.706166 0.708046 78.5625 115.788)'
            />
          </clipPath>
          <linearGradient
            id='paint0_linear_781_2962'
            x1='75.5869'
            y1='0'
            x2='75.5869'
            y2='136.646'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#FFB37F' />
            <stop offset='1' stopColor='#FF7B0D' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_781_2962'
            x1='28.2429'
            y1='51.1771'
            x2='135.754'
            y2='183.979'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_781_2962'
            x1='53.4821'
            y1='1.7922'
            x2='-8.48993'
            y2='13.5115'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' />
            <stop offset='1' stopColor='white' stopOpacity='0.2' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_781_2962'
            x1='9.16314'
            y1='1.15624'
            x2='10.932'
            y2='13.8626'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
        </defs>
      </svg>
      <div className='absolute top-[72px] right-[31px] z-10'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='65'
          height='64'
          viewBox='0 0 65 64'
          fill='none'
        >
          <g filter='url(#filter0_d_1842_3006)' data-figma-bg-blur-radius='15'>
            <rect
              width='57.5633'
              height='9.9247'
              rx='4.96235'
              transform='matrix(0.706166 -0.708046 0.706166 0.708046 3.5625 43.7881)'
              fill='url(#paint0_linear_1842_3006)'
            />
            <rect
              x='0.282466'
              width='57.1633'
              height='9.5247'
              rx='4.76235'
              transform='matrix(0.706166 -0.708046 0.706166 0.708046 3.6455 43.9881)'
              stroke='url(#paint1_linear_1842_3006)'
              strokeWidth='0.4'
            />
          </g>
          <defs>
            <filter
              id='filter0_d_1842_3006'
              x='-9.38281'
              y='-9.91113'
              width='73.5508'
              height='73.668'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='5' dy='5' />
              <feGaussianBlur stdDeviation='5' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 1 0 0 0 0 0.580392 0 0 0 0 0.247059 0 0 0 0.5 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_1842_3006'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_1842_3006'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_0_1842_3006_clip_path'
              transform='translate(9.38281 9.91113)'
            >
              <rect
                width='57.5633'
                height='9.9247'
                rx='4.96235'
                transform='matrix(0.706166 -0.708046 0.706166 0.708046 3.5625 43.7881)'
              />
            </clipPath>
            <linearGradient
              id='paint0_linear_1842_3006'
              x1='53.4821'
              y1='1.7922'
              x2='-8.48993'
              y2='13.5115'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' />
              <stop offset='1' stopColor='white' stopOpacity='0.2' />
            </linearGradient>
            <linearGradient
              id='paint1_linear_1842_3006'
              x1='9.16314'
              y1='1.15624'
              x2='10.932'
              y2='13.8626'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' stopOpacity='0.25' />
              <stop offset='1' stopColor='white' stopOpacity='0' />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
};
