import { motion } from 'framer-motion';

export const GlobalExposureIcon = () => {
  return (
    <div className='relative'>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='248'
        height='217'
        viewBox='0 0 248 217'
        fill='none'
      >
        <motion.path
          d='M181.701 100.266C181.701 147.911 143.079 186.533 95.4344 186.533C47.7894 186.533 9.16797 147.911 9.16797 100.266C9.16797 52.6301 47.7894 14 95.4344 14C143.079 14 181.701 52.6301 181.701 100.266Z'
          fill='url(#paint0_linear_747_32721)'
          animate={{ y: [-15, 0, -15] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
        />
        <g opacity='0.5' filter='url(#filter0_f_747_32721)'>
          <path
            d='M133.394 87.2907C133.394 112.961 112.586 133.769 86.9157 133.769C61.2458 133.769 40.4375 112.961 40.4375 87.2907C40.4375 61.6255 61.2458 40.8125 86.9157 40.8125C112.586 40.8125 133.394 61.6255 133.394 87.2907Z'
            fill='#E46800'
          />
        </g>
        <foreignObject x='-11.9652' y='-11.349' width='278.165' height='278.165'>
          <div
            style={{
              backdropFilter: 'blur(25.35px)',
              clipPath: 'url(#bgblur_0_747_32721_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <motion.path
          data-figma-bg-blur-radius='50.7035'
          d='M127.116 215.057C78.8879 215.057 39.7939 175.953 39.794 127.733C39.7941 79.5052 78.888 40.4112 127.116 40.4111C175.344 40.4111 214.439 79.5052 214.439 127.733C214.439 175.953 175.345 215.057 127.116 215.057Z'
          fill='#FFCBB9'
          fillOpacity='0.35'
          stroke='url(#paint1_linear_747_32721)'
          strokeWidth='2.11265'
          strokeLinecap='round'
          strokeLinejoin='round'
          animate={{ fillOpacity: [0.35, 0.6, 0.35] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
        />
        <foreignObject x='96.1111' y='-3.40504' width='168.575' height='168.346'>
          <div
            style={{
              backdropFilter: 'blur(15.84px)',
              clipPath: 'url(#bgblur_1_747_32721_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g filter='url(#filter2_d_747_32721)' data-figma-bg-blur-radius='31.6897'>
          <path
            d='M140.982 28.386C138.593 28.0342 136.165 28.6049 134.232 29.9722C132.3 31.3394 131.023 33.3911 130.682 35.6746L130.543 37.0198L127.855 119.188C127.565 121.892 128.48 124.599 130.377 126.643C132.274 128.687 134.976 129.879 137.819 129.926L223.617 133.243C226.006 133.34 228.329 132.523 230.072 130.971C231.814 129.42 232.832 127.262 232.901 124.976C232.979 124.695 233.009 124.403 232.99 124.111C230.254 75.3581 191.559 35.0998 140.982 28.386Z'
            fill='url(#paint2_linear_747_32721)'
          />
        </g>
        <foreignObject x='78.8845' y='83.7175' width='92.9575' height='92.9566'>
          <div
            style={{
              backdropFilter: 'blur(15.84px)',
              clipPath: 'url(#bgblur_2_747_32721_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g filter='url(#filter3_d_747_32721)' data-figma-bg-blur-radius='31.6897'>
          <circle
            cx='125.363'
            cy='130.196'
            r='14.7885'
            fill='url(#paint3_linear_747_32721)'
          />
          <circle
            cx='125.363'
            cy='130.196'
            r='14.366'
            stroke='url(#paint4_linear_747_32721)'
            strokeWidth='0.845059'
          />
        </g>
        <defs>
          <filter
            id='filter0_f_747_32721'
            x='0.297207'
            y='0.672207'
            width='173.238'
            height='173.237'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='BackgroundImageFix'
              result='shape'
            />
            <feGaussianBlur
              stdDeviation='20.0701'
              result='effect1_foregroundBlur_747_32721'
            />
          </filter>
          <clipPath
            id='bgblur_0_747_32721_clip_path'
            transform='translate(11.9652 11.349)'
          >
            <path d='M127.116 215.057C78.8879 215.057 39.7939 175.953 39.794 127.733C39.7941 79.5052 78.888 40.4112 127.116 40.4111C175.344 40.4111 214.439 79.5052 214.439 127.733C214.439 175.953 175.345 215.057 127.116 215.057Z' />
          </clipPath>
          <filter
            id='filter2_d_747_32721'
            x='96.1111'
            y='-3.40504'
            width='168.575'
            height='168.346'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='-6.33794' dy='10.5632' />
            <feGaussianBlur stdDeviation='10.5632' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 1 0 0 0 0 0.52549 0 0 0 0 0.137255 0 0 0 0.3 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_747_32721'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_747_32721'
              result='shape'
            />
          </filter>
          <clipPath
            id='bgblur_1_747_32721_clip_path'
            transform='translate(-96.1111 3.40504)'
          >
            <path d='M140.982 28.386C138.593 28.0342 136.165 28.6049 134.232 29.9722C132.3 31.3394 131.023 33.3911 130.682 35.6746L130.543 37.0198L127.855 119.188C127.565 121.892 128.48 124.599 130.377 126.643C132.274 128.687 134.976 129.879 137.819 129.926L223.617 133.243C226.006 133.34 228.329 132.523 230.072 130.971C231.814 129.42 232.832 127.262 232.901 124.976C232.979 124.695 233.009 124.403 232.99 124.111C230.254 75.3581 191.559 35.0998 140.982 28.386Z' />
          </clipPath>
          <filter
            id='filter3_d_747_32721'
            x='78.8845'
            y='83.7175'
            width='92.9575'
            height='92.9566'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='10.5632' dy='10.5632' />
            <feGaussianBlur stdDeviation='10.5632' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 1 0 0 0 0 0.52549 0 0 0 0 0.137255 0 0 0 0.5 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_747_32721'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_747_32721'
              result='shape'
            />
          </filter>
          <clipPath
            id='bgblur_2_747_32721_clip_path'
            transform='translate(-78.8845 -83.7175)'
          >
            <circle cx='125.363' cy='130.196' r='14.7885' />
          </clipPath>
          <linearGradient
            id='paint0_linear_747_32721'
            x1='95.4344'
            y1='14'
            x2='95.4344'
            y2='186.533'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#FFB37F' />
            <stop offset='1' stopColor='#FF7B0D' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_747_32721'
            x1='213.886'
            y1='215.173'
            x2='31.2373'
            y2='60.4783'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.4' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_747_32721'
            x1='232.132'
            y1='56.1852'
            x2='46.4033'
            y2='29.6147'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.2' />
            <stop offset='1' stopColor='white' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_747_32721'
            x1='138.054'
            y1='120.748'
            x2='88.0335'
            y2='118.951'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' />
            <stop offset='1' stopColor='white' stopOpacity='0.2' />
          </linearGradient>
          <linearGradient
            id='paint4_linear_747_32721'
            x1='115.282'
            y1='118.853'
            x2='134.149'
            y2='142.22'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
        </defs>
      </svg>
      <div className='absolute top-[17px] right-[0px] z-10'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='148'
          height='160'
          viewBox='0 0 148 160'
          fill='none'
        >
          <g filter='url(#filter0_d_203_14)' data-figma-bg-blur-radius='31.6897'>
            <path
              d='M40.9821 11.386C38.5932 11.0342 36.1648 11.6049 34.2325 12.9722C32.3002 14.3394 31.0227 16.3911 30.6817 18.6746L30.5429 20.0198L27.8553 102.188C27.5647 104.892 28.4802 107.599 30.3769 109.643C32.2736 111.687 34.9758 112.879 37.8195 112.926L123.617 116.243C126.006 116.34 128.329 115.523 130.072 113.971C131.814 112.42 132.832 110.262 132.901 107.976C132.979 107.695 133.009 107.403 132.99 107.111C130.254 58.3581 91.5594 18.0998 40.9821 11.386Z'
              fill='url(#paint0_linear_203_14)'
            />
          </g>

          <g filter='url(#filter1_d_203_14)' data-figma-bg-blur-radius='31.6897'>
            <circle
              cx='25.3627'
              cy='113.196'
              r='14.7885'
              fill='url(#paint1_linear_203_14)'
            />
            <circle
              cx='25.3627'
              cy='113.196'
              r='14.366'
              stroke='url(#paint2_linear_203_14)'
              strokeWidth='0.845059'
            />
          </g>
          <defs>
            <filter
              id='filter0_d_203_14'
              x='-3.88721'
              y='-20.4052'
              width='168.573'
              height='168.346'
              filterUnits='userSpaceOnUse'
              color-interpolation-filters='sRGB'
            >
              <feFlood flood-opacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='-6.33794' dy='10.5632' />
              <feGaussianBlur stdDeviation='10.5632' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 1 0 0 0 0 0.52549 0 0 0 0 0.137255 0 0 0 0.3 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_203_14'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_203_14'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_0_203_14_clip_path'
              transform='translate(3.88721 20.4052)'
            >
              <path d='M40.9821 11.386C38.5932 11.0342 36.1648 11.6049 34.2325 12.9722C32.3002 14.3394 31.0227 16.3911 30.6817 18.6746L30.5429 20.0198L27.8553 102.188C27.5647 104.892 28.4802 107.599 30.3769 109.643C32.2736 111.687 34.9758 112.879 37.8195 112.926L123.617 116.243C126.006 116.34 128.329 115.523 130.072 113.971C131.814 112.42 132.832 110.262 132.901 107.976C132.979 107.695 133.009 107.403 132.99 107.111C130.254 58.3581 91.5594 18.0998 40.9821 11.386Z' />
            </clipPath>
            <filter
              id='filter1_d_203_14'
              x='-21.1155'
              y='66.7175'
              width='92.9566'
              height='92.9564'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='10.5632' dy='10.5632' />
              <feGaussianBlur stdDeviation='10.5632' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 1 0 0 0 0 0.52549 0 0 0 0 0.137255 0 0 0 0.5 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_203_14'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_203_14'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_1_203_14_clip_path'
              transform='translate(21.1155 -66.7175)'
            >
              <circle cx='25.3627' cy='113.196' r='14.7885' />
            </clipPath>
            <linearGradient
              id='paint0_linear_203_14'
              x1='132.132'
              y1='39.1852'
              x2='-53.5967'
              y2='12.6147'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' stopOpacity='0.2' />
              <stop offset='1' stopColor='white' />
            </linearGradient>
            <linearGradient
              id='paint1_linear_203_14'
              x1='38.0543'
              y1='103.748'
              x2='-11.9665'
              y2='101.951'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' />
              <stop offset='1' stopColor='white' stopOpacity='0.2' />
            </linearGradient>
            <linearGradient
              id='paint2_linear_203_14'
              x1='15.2824'
              y1='101.853'
              x2='34.1494'
              y2='125.22'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' stopOpacity='0.25' />
              <stop offset='1' stopColor='white' stopOpacity='0' />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
};
