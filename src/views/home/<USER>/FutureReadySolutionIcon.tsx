import { motion } from 'framer-motion';

export default function FutureReadySolutionIcon() {
  return (
    <div className='relative'>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='218'
        height='219'
        viewBox='0 0 218 219'
        fill='none'
      >
        <foreignObject x='-11.1212' y='-10.6212' width='220.477' height='220.478'>
          <div
            style={{
              backdropFilter: 'blur(13.31px)',
              clipPath: 'url(#bgblur_0_747_32702_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <motion.path
          animate={{ y: [15, 0, 15] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
          data-figma-bg-blur-radius='26.6212'
          d='M182.736 99.6177C182.736 145.8 145.3 183.235 99.1178 183.235C52.9357 183.235 15.5 145.8 15.5 99.6177C15.5 53.444 52.9357 16 99.1178 16C145.3 16 182.736 53.444 182.736 99.6177Z'
          fill='url(#paint0_linear_747_32702)'
        />
        <g filter='url(#filter1_f_747_32702)'>
          <path
            d='M99.3384 45.1929C69.6455 45.1929 45.5469 69.2969 45.5469 98.9844C45.5469 128.683 69.6455 152.776 99.3384 152.776C129.085 152.776 153.13 128.683 153.13 98.9844C153.13 69.2969 129.085 45.1929 99.3384 45.1929Z'
            fill='#00D4DB'
          />
        </g>
        <foreignObject x='15.502' y='15.9996' width='232.765' height='232.765'>
          <div
            style={{
              backdropFilter: 'blur(15.36px)',
              clipPath: 'url(#bgblur_1_747_32702_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <motion.path
          animate={{ fillOpacity: [0.3, 0, 0.3] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
          data-figma-bg-blur-radius='30.7167'
          d='M131.884 47.7407C178.631 47.7409 216.525 85.6433 216.525 132.382C216.525 179.13 178.631 217.024 131.884 217.024C85.1364 217.024 47.2424 179.13 47.2422 132.382C47.2422 85.6432 85.1362 47.7407 131.884 47.7407Z'
          fill='#8FE1FF'
          stroke='url(#paint1_linear_747_32702)'
          strokeWidth='2.04778'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <foreignObject x='68.713' y='67.1182' width='126.351' height='126.433'>
          <div
            style={{
              backdropFilter: 'blur(15.36px)',
              clipPath: 'url(#bgblur_2_747_32702_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g filter='url(#filter3_d_747_32702)' data-figma-bg-blur-radius='30.7167'>
          <mask id='path-4-inside-1_747_32702' fill='white'>
            <path d='M164.163 102.781L150.617 145.593C150.115 147.349 148.694 148.771 146.938 149.281L104.293 162.652C101.449 163.58 98.6901 160.812 99.6099 157.969L112.989 115.073C113.49 113.317 114.912 111.979 116.668 111.394L159.48 98.015C162.407 97.0952 165.083 99.8546 164.163 102.781Z' />
          </mask>
          <path
            d='M164.163 102.781L150.617 145.593C150.115 147.349 148.694 148.771 146.938 149.281L104.293 162.652C101.449 163.58 98.6901 160.812 99.6099 157.969L112.989 115.073C113.49 113.317 114.912 111.979 116.668 111.394L159.48 98.015C162.407 97.0952 165.083 99.8546 164.163 102.781Z'
            fill='url(#paint2_linear_747_32702)'
          />
          <path
            d='M150.617 145.593L149.836 145.346L149.832 145.357L149.829 145.368L150.617 145.593ZM146.938 149.281L146.709 148.494L146.701 148.497L146.692 148.499L146.938 149.281ZM104.293 162.652L104.047 161.87L104.038 161.873L104.293 162.652ZM99.6099 157.969L100.389 158.221L100.392 158.213L99.6099 157.969ZM112.989 115.073L113.771 115.317L113.774 115.307L113.776 115.298L112.989 115.073ZM116.668 111.394L116.424 110.612L116.416 110.614L116.409 110.617L116.668 111.394ZM159.48 98.015L159.725 98.7968L159.726 98.7964L159.48 98.015ZM164.163 102.781L163.382 102.534L149.836 145.346L150.617 145.593L151.398 145.841L164.944 103.028L164.163 102.781ZM150.617 145.593L149.829 145.368C149.405 146.852 148.196 148.062 146.709 148.494L146.938 149.281L147.166 150.068C149.191 149.479 150.825 147.847 151.404 145.819L150.617 145.593ZM146.938 149.281L146.692 148.499L104.047 161.87L104.293 162.652L104.538 163.433L147.183 150.063L146.938 149.281ZM104.293 162.652L104.038 161.873C101.836 162.592 99.6741 160.432 100.389 158.221L99.6099 157.969L98.8306 157.717C97.7061 161.192 101.063 164.567 104.547 163.43L104.293 162.652ZM99.6099 157.969L100.392 158.213L113.771 115.317L112.989 115.073L112.207 114.829L98.8279 157.725L99.6099 157.969ZM112.989 115.073L113.776 115.298C114.194 113.837 115.391 112.683 116.927 112.171L116.668 111.394L116.409 110.617C114.433 111.276 112.787 112.797 112.201 114.848L112.989 115.073ZM116.668 111.394L116.912 112.176L159.725 98.7968L159.48 98.015L159.236 97.2332L116.424 110.612L116.668 111.394ZM159.48 98.015L159.726 98.7964C161.996 98.083 164.111 100.215 163.381 102.536L164.163 102.781L164.944 103.027C166.055 99.494 162.818 96.1073 159.235 97.2336L159.48 98.015Z'
            fill='url(#paint3_linear_747_32702)'
            mask='url(#path-4-inside-1_747_32702)'
          />
        </g>
        <defs>
          <clipPath
            id='bgblur_0_747_32702_clip_path'
            transform='translate(11.1212 10.6212)'
          >
            <path d='M182.736 99.6177C182.736 145.8 145.3 183.235 99.1178 183.235C52.9357 183.235 15.5 145.8 15.5 99.6177C15.5 53.444 52.9357 16 99.1178 16C145.3 16 182.736 53.444 182.736 99.6177Z' />
          </clipPath>
          <filter
            id='filter1_f_747_32702'
            x='0.495678'
            y='0.141674'
            width='197.684'
            height='197.685'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='BackgroundImageFix'
              result='shape'
            />
            <feGaussianBlur
              stdDeviation='22.5256'
              result='effect1_foregroundBlur_747_32702'
            />
          </filter>
          <clipPath
            id='bgblur_1_747_32702_clip_path'
            transform='translate(-15.502 -15.9996)'
          >
            <path d='M131.884 47.7407C178.631 47.7409 216.525 85.6433 216.525 132.382C216.525 179.13 178.631 217.024 131.884 217.024C85.1364 217.024 47.2424 179.13 47.2422 132.382C47.2422 85.6432 85.1362 47.7407 131.884 47.7407Z' />
          </clipPath>
          <filter
            id='filter3_d_747_32702'
            x='68.713'
            y='67.1182'
            width='126.351'
            height='126.433'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='10.2389' dy='10.2389' />
            <feGaussianBlur stdDeviation='10.2389' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 0.560784 0 0 0 0 0.582745 0 0 0 0 1 0 0 0 0.3 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_747_32702'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_747_32702'
              result='shape'
            />
          </filter>
          <clipPath
            id='bgblur_2_747_32702_clip_path'
            transform='translate(-68.713 -67.1182)'
          >
            <path d='M164.163 102.781L150.617 145.593C150.115 147.349 148.694 148.771 146.938 149.281L104.293 162.652C101.449 163.58 98.6901 160.812 99.6099 157.969L112.989 115.073C113.49 113.317 114.912 111.979 116.668 111.394L159.48 98.015C162.407 97.0952 165.083 99.8546 164.163 102.781Z' />
          </clipPath>
          <linearGradient
            id='paint0_linear_747_32702'
            x1='111.43'
            y1='110.817'
            x2='-35.5185'
            y2='182.266'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#88FFD9' />
            <stop offset='1' stopColor='#0DFFFF' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_747_32702'
            x1='74.8868'
            y1='68.2473'
            x2='181.565'
            y2='200.371'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_747_32702'
            x1='132.652'
            y1='109.174'
            x2='121.106'
            y2='159.02'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' />
            <stop offset='1' stopColor='white' stopOpacity='0.2' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_747_32702'
            x1='109.763'
            y1='105.407'
            x2='151.237'
            y2='156.709'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
        </defs>
      </svg>
      <div className='absolute top-[87px] right-[22px] z-10'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='107'
          height='107'
          viewBox='0 0 107 107'
          fill='none'
        >
          <g filter='url(#filter0_d_747_32708)' data-figma-bg-blur-radius='30.7167'>
            <mask id='path-1-inside-1_747_32708' fill='white'>
              <path d='M75.1628 15.7812L61.6167 58.5935C61.115 60.3495 59.6935 61.771 57.9375 62.281L15.2925 75.6515C12.4495 76.5797 9.69011 73.8119 10.6099 70.9689L23.9887 28.073C24.4905 26.317 25.912 24.9791 27.6679 24.3938L70.4802 11.015C73.4068 10.0952 76.0826 12.8546 75.1628 15.7812Z' />
            </mask>
            <path
              d='M75.1628 15.7812L61.6167 58.5935C61.115 60.3495 59.6935 61.771 57.9375 62.281L15.2925 75.6515C12.4495 76.5797 9.69011 73.8119 10.6099 70.9689L23.9887 28.073C24.4905 26.317 25.912 24.9791 27.6679 24.3938L70.4802 11.015C73.4068 10.0952 76.0826 12.8546 75.1628 15.7812Z'
              fill='url(#paint0_linear_747_32708)'
            />
            <path
              d='M61.6167 58.5935L60.8358 58.3464L60.8323 58.3574L60.8291 58.3685L61.6167 58.5935ZM57.9375 62.281L57.7091 61.4944L57.7007 61.4968L57.6925 61.4994L57.9375 62.281ZM15.2925 75.6515L15.0474 74.8699L15.0383 74.8728L15.2925 75.6515ZM10.6099 70.9689L11.3893 71.2211L11.3919 71.2128L10.6099 70.9689ZM23.9887 28.073L24.7707 28.3169L24.7736 28.3075L24.7763 28.298L23.9887 28.073ZM27.6679 24.3938L27.4236 23.612L27.4162 23.6143L27.4089 23.6167L27.6679 24.3938ZM70.4802 11.015L70.7245 11.7968L70.7258 11.7964L70.4802 11.015ZM75.1628 15.7812L74.3818 15.5341L60.8358 58.3464L61.6167 58.5935L62.3977 58.8406L75.9437 16.0283L75.1628 15.7812ZM61.6167 58.5935L60.8291 58.3685C60.4053 59.8519 59.1961 61.0625 57.7091 61.4944L57.9375 62.281L58.166 63.0676C60.1909 62.4795 61.8247 60.847 62.4043 58.8185L61.6167 58.5935ZM57.9375 62.281L57.6925 61.4994L15.0474 74.8699L15.2925 75.6515L15.5376 76.4331L58.1826 63.0626L57.9375 62.281ZM15.2925 75.6515L15.0383 74.8728C12.8359 75.5919 10.6741 73.4316 11.3892 71.2211L10.6099 70.9689L9.83057 70.7168C8.70614 74.1923 12.0631 77.5675 15.5467 76.4302L15.2925 75.6515ZM10.6099 70.9689L11.3919 71.2128L24.7707 28.3169L23.9887 28.073L23.2068 27.8291L9.82794 70.725L10.6099 70.9689ZM23.9887 28.073L24.7763 28.298C25.1939 26.8366 26.3914 25.6828 27.927 25.1709L27.6679 24.3938L27.4089 23.6167C25.4325 24.2755 23.787 25.7974 23.2011 27.848L23.9887 28.073ZM27.6679 24.3938L27.9122 25.1757L70.7245 11.7968L70.4802 11.015L70.2359 10.2332L27.4236 23.612L27.6679 24.3938ZM70.4802 11.015L70.7258 11.7964C72.9956 11.083 75.1106 13.2152 74.3814 15.5356L75.1628 15.7812L75.9442 16.0268C77.0545 12.494 73.8181 9.10733 70.2346 10.2336L70.4802 11.015Z'
              fill='url(#paint1_linear_747_32708)'
              mask='url(#path-1-inside-1_747_32708)'
            />
          </g>
          <defs>
            <filter
              id='filter0_d_747_32708'
              x='-20.287'
              y='-19.8818'
              width='126.351'
              height='126.433'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='10.2389' dy='10.2389' />
              <feGaussianBlur stdDeviation='10.2389' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 0.560784 0 0 0 0 0.582745 0 0 0 0 1 0 0 0 0.3 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_747_32708'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_747_32708'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_0_747_32708_clip_path'
              transform='translate(20.287 19.8818)'
            >
              <path d='M75.1628 15.7812L61.6167 58.5935C61.115 60.3495 59.6935 61.771 57.9375 62.281L15.2925 75.6515C12.4495 76.5797 9.69011 73.8119 10.6099 70.9689L23.9887 28.073C24.4905 26.317 25.912 24.9791 27.6679 24.3938L70.4802 11.015C73.4068 10.0952 76.0826 12.8546 75.1628 15.7812Z' />
            </clipPath>
            <linearGradient
              id='paint0_linear_747_32708'
              x1='43.6521'
              y1='22.1737'
              x2='32.1062'
              y2='72.0196'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' />
              <stop offset='1' stopColor='white' stopOpacity='0.2' />
            </linearGradient>
            <linearGradient
              id='paint1_linear_747_32708'
              x1='20.7635'
              y1='18.4075'
              x2='62.2371'
              y2='69.7088'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' stopOpacity='0.25' />
              <stop offset='1' stopColor='white' stopOpacity='0' />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
}
