import { motion } from 'framer-motion';

export default function DotIcon({ indexNumber }: { indexNumber: number }) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='125'
      height='125'
      viewBox='0 0 125 125'
      fill='none'
    >
      <motion.path
        d='M6.25 12.5C9.70178 12.5 12.5 9.70178 12.5 6.25C12.5 2.79822 9.70178 0 6.25 0C2.79822 0 0 2.79822 0 6.25C0 9.70178 2.79822 12.5 6.25 12.5Z'
        fill='#43729A'
        animate={{
          fillOpacity: [1, 0.55, 1],
        }}
        key={`1-${indexNumber}`}
      />
      <motion.path
        d='M37.5 12.5C40.9518 12.5 43.75 9.70178 43.75 6.25C43.75 2.79822 40.9518 0 37.5 0C34.0482 0 31.25 2.79822 31.25 6.25C31.25 9.70178 34.0482 12.5 37.5 12.5Z'
        fill='#43729A'
        animate={{
          fillOpacity: [0.55, 1, 0.55],
        }}
        transition={{
          delay: 0.1,
        }}
        key={`2-${indexNumber}`}
      />
      <motion.path
        d='M75 12.5C78.4518 12.5 81.25 9.70178 81.25 6.25C81.25 2.79822 78.4518 0 75 0C71.5482 0 68.75 2.79822 68.75 6.25C68.75 9.70178 71.5482 12.5 75 12.5Z'
        fill='#25425D'
        animate={{
          fillOpacity: [0.49, 1, 0.49],
        }}
        transition={{
          delay: 0.2,
        }}
        key={`3-${indexNumber}`}
      />

      <motion.path
        d='M6.25 43.75C9.70178 43.75 12.5 40.9518 12.5 37.5C12.5 34.0482 9.70178 31.25 6.25 31.25C2.79822 31.25 0 34.0482 0 37.5C0 40.9518 2.79822 43.75 6.25 43.75Z'
        fill='#43729A'
        animate={{
          fillOpacity: [1, 0.55, 1],
        }}
        transition={{
          delay: 0.2,
        }}
        key={`4-${indexNumber}`}
      />
      <motion.path
        d='M37.5 43.75C40.9518 43.75 43.75 40.9518 43.75 37.5C43.75 34.0482 40.9518 31.25 37.5 31.25C34.0482 31.25 31.25 34.0482 31.25 37.5C31.25 40.9518 34.0482 43.75 37.5 43.75Z'
        fill='#43729A'
        animate={{
          fillOpacity: [0.55, 1, 0.55],
        }}
        transition={{
          delay: 0.3,
        }}
        key={`5-${indexNumber}`}
      />
      <motion.path
        d='M75 43.75C78.4518 43.75 81.25 40.9518 81.25 37.5C81.25 34.0482 78.4518 31.25 75 31.25C71.5482 31.25 68.75 34.0482 68.75 37.5C68.75 40.9518 71.5482 43.75 75 43.75Z'
        fill='#25425D'
        animate={{
          fillOpacity: [0.49, 1, 0.49],
        }}
        transition={{
          delay: 0.4,
        }}
        key={`6-${indexNumber}`}
      />

      <motion.path
        d='M6.25 81.25C9.70178 81.25 12.5 78.4518 12.5 75C12.5 71.5482 9.70178 68.75 6.25 68.75C2.79822 68.75 0 71.5482 0 75C0 78.4518 2.79822 81.25 6.25 81.25Z'
        fill='#43729A'
        animate={{
          fillOpacity: [1, 0.55, 1],
        }}
        transition={{
          delay: 0.4,
        }}
        key={`7-${indexNumber}`}
      />
      <motion.path
        d='M37.5 81.25C40.9518 81.25 43.75 78.4518 43.75 75C43.75 71.5482 40.9518 68.75 37.5 68.75C34.0482 68.75 31.25 71.5482 31.25 75C31.25 78.4518 34.0482 81.25 37.5 81.25Z'
        fill='#43729A'
        animate={{
          fillOpacity: [0.55, 1, 0.55],
        }}
        transition={{
          delay: 0.5,
        }}
        key={`8-${indexNumber}`}
      />
      <motion.path
        d='M75 81.25C78.4518 81.25 81.25 78.4518 81.25 75C81.25 71.5482 78.4518 68.75 75 68.75C71.5482 68.75 68.75 71.5482 68.75 75C68.75 78.4518 71.5482 81.25 75 81.25Z'
        fill='#25425D'
        animate={{
          fillOpacity: [0.49, 1, 0.49],
        }}
        transition={{
          delay: 0.6,
        }}
        key={`9-${indexNumber}`}
      />
      <motion.path
        d='M112.5 81.25C115.952 81.25 118.75 78.4518 118.75 75C118.75 71.5482 115.952 68.75 112.5 68.75C109.048 68.75 106.25 71.5482 106.25 75C106.25 78.4518 109.048 81.25 112.5 81.25Z'
        fill='#122437'
        animate={{
          fillOpacity: [0.46, 1, 0.46],
        }}
        transition={{
          delay: 0.6,
        }}
        key={`10-${indexNumber}`}
      />
      <motion.path
        d='M6.25 118.75C9.70178 118.75 12.5 115.952 12.5 112.5C12.5 109.048 9.70178 106.25 6.25 106.25C2.79822 106.25 0 109.048 0 112.5C0 115.952 2.79822 118.75 6.25 118.75Z'
        fill='#43729A'
        animate={{
          fillOpacity: [0.46, 1, 0.46],
        }}
        transition={{
          delay: 0.7,
        }}
        key={`11-${indexNumber}`}
      />
      <motion.path
        d='M37.5 118.75C40.9518 118.75 43.75 115.952 43.75 112.5C43.75 109.048 40.9518 106.25 37.5 106.25C34.0482 106.25 31.25 109.048 31.25 112.5C31.25 115.952 34.0482 118.75 37.5 118.75Z'
        fill='#43729A'
        animate={{
          fillOpacity: [0.55, 1, 0.55],
        }}
        transition={{
          delay: 0.8,
        }}
        key={`12-${indexNumber}`}
      />
      <motion.path
        d='M75 118.75C78.4518 118.75 81.25 115.952 81.25 112.5C81.25 109.048 78.4518 106.25 75 106.25C71.5482 106.25 68.75 109.048 68.75 112.5C68.75 115.952 71.5482 118.75 75 118.75Z'
        fill='#25425D'
        animate={{
          fillOpacity: [0.49, 1, 0.49],
        }}
        transition={{
          delay: 0.9,
        }}
        key={`13-${indexNumber}`}
      />
    </svg>
  );
}
