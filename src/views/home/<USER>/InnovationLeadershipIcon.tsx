import { motion } from 'framer-motion';
export default function InnovationLeadershipIcon() {
  return (
    <div className='relative'>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='167'
        height='203'
        viewBox='0 0 167 203'
        fill='none'
      >
        <motion.path
          animate={{ x: [-15, 0, -15], rotate: [0, 10, 0] }}
          transition={{
            duration: 4,
            repeat: Infinity,
          }}
          d='M49.8641 87.1229C42.5806 80.8141 38.9389 77.6596 36.7952 74.0378C33.1432 67.8679 32.2155 60.4591 34.2334 53.5793C35.4179 49.5407 38.1694 45.5857 43.6723 37.6757C49.1753 29.7657 51.9268 25.8107 55.3015 23.2959C61.0505 19.0117 68.3196 17.3055 75.3744 18.5843C79.5156 19.3349 83.7395 21.6526 92.1872 26.288L116.209 39.469C129.078 46.5303 135.512 50.0609 138.987 55.0834C142.708 60.4612 144.254 67.0485 143.315 73.5201C142.437 79.5643 138.246 85.5891 129.863 97.6387C121.48 109.688 117.288 115.713 111.927 118.637C106.185 121.769 99.4714 122.609 93.1354 120.99C87.218 119.479 81.6704 114.673 70.5752 105.063L49.8641 87.1229Z'
          fill='url(#paint0_linear_747_32681)'
        />
        <g opacity='0.5' filter='url(#filter0_f_747_32681)'>
          <path
            d='M62.0781 79.3595C53.565 71.9855 51.9862 59.3772 58.4182 50.1318C64.8502 40.8864 77.2208 37.9827 87.0948 43.4007L101.294 51.1918C113.664 57.9796 117.423 73.9923 109.364 85.5752C101.306 97.1582 84.9856 99.2018 74.3201 89.9634L62.0781 79.3595Z'
            fill='#0062FF'
          />
        </g>
        <foreignObject x='-47.5112' y='4.98002' width='262.022' height='245.771'>
          <div
            style={{
              backdropFilter: 'blur(23.88px)',
              clipPath: 'url(#bgblur_0_747_32681_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g data-figma-bg-blur-radius='47.7612'>
          <mask
            id='path-3-outside-1_747_32681'
            maskUnits='userSpaceOnUse'
            x='0.238281'
            y='52.7314'
            width='167'
            height='151'
            fill='black'
          >
            <rect fill='white' x='0.238281' y='52.7314' width='167' height='151' />
            <path d='M158.664 139.863C151.993 139.863 146.565 134.484 146.565 127.874C146.565 121.255 151.993 115.877 158.664 115.877C160.282 115.877 161.834 115.241 162.971 114.113C164.117 112.978 164.759 111.44 164.759 109.838L164.751 88.0819C164.751 69.6917 149.645 54.7314 131.085 54.7314H35.9126C17.3527 54.7314 2.24641 69.6917 2.24641 88.0819L2.23828 110.538C2.23828 112.141 2.88024 113.679 4.02601 114.814C5.16365 115.941 6.71573 116.577 8.33281 116.577C15.2318 116.577 20.4325 121.432 20.4325 127.874C20.4325 134.484 15.0043 139.863 8.33281 139.863C4.96863 139.863 2.23828 142.568 2.23828 145.902V167.642C2.23828 186.032 17.3365 201 35.9045 201H131.093C149.661 201 164.759 186.032 164.759 167.642V145.902C164.759 142.568 162.029 139.863 158.664 139.863Z' />
          </mask>
          <motion.path
            d='M158.664 139.863C151.993 139.863 146.565 134.484 146.565 127.874C146.565 121.255 151.993 115.877 158.664 115.877C160.282 115.877 161.834 115.241 162.971 114.113C164.117 112.978 164.759 111.44 164.759 109.838L164.751 88.0819C164.751 69.6917 149.645 54.7314 131.085 54.7314H35.9126C17.3527 54.7314 2.24641 69.6917 2.24641 88.0819L2.23828 110.538C2.23828 112.141 2.88024 113.679 4.02601 114.814C5.16365 115.941 6.71573 116.577 8.33281 116.577C15.2318 116.577 20.4325 121.432 20.4325 127.874C20.4325 134.484 15.0043 139.863 8.33281 139.863C4.96863 139.863 2.23828 142.568 2.23828 145.902V167.642C2.23828 186.032 17.3365 201 35.9045 201H131.093C149.661 201 164.759 186.032 164.759 167.642V145.902C164.759 142.568 162.029 139.863 158.664 139.863Z'
            fill='#3EA0FE'
            animate={{ fillOpacity: [0.25, 0.5, 0.25] }}
            transition={{
              duration: 4,
              repeat: Infinity,
            }}
          />
          <path
            d='M158.664 141.853C159.764 141.853 160.655 140.962 160.655 139.863C160.655 138.764 159.764 137.873 158.664 137.873V141.853ZM162.971 114.113L161.571 112.7L162.971 114.113ZM164.759 109.838H166.749V109.837L164.759 109.838ZM164.751 88.0819H162.761V88.0827L164.751 88.0819ZM2.24641 88.0819L4.23646 88.0826V88.0819H2.24641ZM2.23828 110.538L0.248232 110.538V110.538H2.23828ZM4.02601 114.814L5.42671 113.4H5.42671L4.02601 114.814ZM158.664 137.873C157.565 137.873 156.674 138.764 156.674 139.863C156.674 140.962 157.565 141.853 158.664 141.853V137.873ZM158.664 139.863V137.873C153.075 137.873 148.555 133.368 148.555 127.874H146.565H144.575C144.575 135.601 150.911 141.853 158.664 141.853V139.863ZM146.565 127.874H148.555C148.555 122.371 153.076 117.867 158.664 117.867V115.877V113.887C150.91 113.887 144.575 120.14 144.575 127.874H146.565ZM158.664 115.877V117.867C160.801 117.867 162.858 117.027 164.372 115.527L162.971 114.113L161.571 112.7C160.809 113.454 159.762 113.887 158.664 113.887V115.877ZM162.971 114.113L164.372 115.527C165.893 114.02 166.749 111.974 166.749 109.838H164.759H162.769C162.769 110.907 162.341 111.936 161.571 112.7L162.971 114.113ZM164.759 109.838L166.749 109.837L166.741 88.0812L164.751 88.0819L162.761 88.0827L162.769 109.839L164.759 109.838ZM164.751 88.0819H166.741C166.741 68.5748 150.726 52.7414 131.085 52.7414V54.7314V56.7215C148.563 56.7215 162.761 70.8085 162.761 88.0819H164.751ZM131.085 54.7314V52.7414H35.9126V54.7314V56.7215H131.085V54.7314ZM35.9126 54.7314V52.7414C16.2715 52.7414 0.256358 68.5748 0.256358 88.0819H2.24641H4.23646C4.23646 70.8085 18.4339 56.7215 35.9126 56.7215V54.7314ZM2.24641 88.0819L0.256358 88.0812L0.248232 110.538L2.23828 110.538L4.22833 110.539L4.23646 88.0826L2.24641 88.0819ZM2.23828 110.538H0.248232C0.248232 112.674 1.10418 114.72 2.62531 116.227L4.02601 114.814L5.42671 113.4C4.65629 112.637 4.22833 111.607 4.22833 110.538H2.23828ZM4.02601 114.814L2.62531 116.227C4.13908 117.727 6.19599 118.567 8.33281 118.567V116.577V114.587C7.23546 114.587 6.18823 114.155 5.42671 113.4L4.02601 114.814ZM8.33281 116.577V118.567C14.2637 118.567 18.4424 122.658 18.4424 127.874H20.4325H22.4225C22.4225 120.207 16.1999 114.587 8.33281 114.587V116.577ZM20.4325 127.874H18.4424C18.4424 133.368 13.9225 137.873 8.33281 137.873V139.863V141.853C16.0861 141.853 22.4225 135.601 22.4225 127.874H20.4325ZM8.33281 139.863V137.873C3.88682 137.873 0.248232 141.452 0.248232 145.902H2.23828H4.22833C4.22833 143.685 6.05044 141.853 8.33281 141.853V139.863ZM2.23828 145.902H0.248232V167.642H2.23828H4.22833V145.902H2.23828ZM2.23828 167.642H0.248232C0.248232 187.148 16.2543 202.99 35.9045 202.99V201V199.01C18.4186 199.01 4.22833 184.916 4.22833 167.642H2.23828ZM35.9045 201V202.99H131.093V201V199.01H35.9045V201ZM131.093 201V202.99C150.743 202.99 166.749 187.148 166.749 167.642H164.759H162.769C162.769 184.916 148.579 199.01 131.093 199.01V201ZM164.759 167.642H166.749V145.902H164.759H162.769V167.642H164.759ZM164.759 145.902H166.749C166.749 141.452 163.11 137.873 158.664 137.873V139.863V141.853C160.947 141.853 162.769 143.685 162.769 145.902H164.759Z'
            fill='url(#paint1_linear_747_32681)'
            mask='url(#path-3-outside-1_747_32681)'
          />
        </g>
        <foreignObject x='23.9422' y='67.141' width='119.108' height='116.795'>
          <div
            style={{
              backdropFilter: 'blur(14.93px)',
              clipPath: 'url(#bgblur_1_747_32681_clip_path)',
              height: '100%',
              width: '100%',
            }}
          ></div>
        </foreignObject>
        <g filter='url(#filter2_d_747_32681)' data-figma-bg-blur-radius='29.8507'>
          <path
            d='M111.375 124.523L101.794 133.851L104.061 147.04C104.451 149.323 103.533 151.574 101.656 152.923C99.7953 154.288 97.3656 154.459 95.3178 153.37L83.4944 147.154L71.6467 153.378C70.7691 153.841 69.8102 154.085 68.8594 154.085C67.6162 154.085 66.3891 153.695 65.3327 152.931C63.4638 151.574 62.5455 149.323 62.9356 147.04L65.1946 133.851L55.614 124.523C53.9563 122.914 53.3793 120.549 54.0944 118.347C54.8177 116.153 56.6866 114.593 58.9701 114.268L72.183 112.342L78.1069 100.34C79.1389 98.2756 81.2029 96.9917 83.4944 96.9917H83.5107C85.8104 96.9998 87.8744 98.2837 88.8901 100.348L94.814 112.342L108.051 114.276C110.31 114.593 112.179 116.153 112.894 118.347C113.618 120.549 113.041 122.914 111.375 124.523Z'
            fill='url(#paint2_linear_747_32681)'
          />
          <path
            d='M83.4941 97.1909H83.5098L83.9238 97.2065C85.9796 97.3566 87.791 98.5648 88.7119 100.436L94.6357 112.43L94.6816 112.524L94.7852 112.539L108.022 114.473H108.023C110.071 114.76 111.786 116.103 112.562 118.018L112.705 118.409C113.405 120.539 112.847 122.824 111.236 124.379V124.38L101.655 133.708L101.58 133.782L101.599 133.885L103.865 147.074C104.242 149.282 103.355 151.457 101.54 152.761L101.538 152.763C99.7394 154.083 97.3917 154.248 95.4111 153.195L95.4102 153.194L83.5869 146.978L83.4941 146.929L83.4023 146.978L71.5537 153.202V153.203C70.7034 153.651 69.7761 153.886 68.8594 153.886C67.6583 153.886 66.4719 153.509 65.4492 152.77C63.6419 151.457 62.7547 149.282 63.1318 147.074L65.3906 133.885L65.4082 133.782L65.333 133.708L55.7529 124.38C54.1502 122.825 53.5913 120.538 54.2832 118.408C54.983 116.286 56.7898 114.779 58.998 114.464H58.999L72.2119 112.539L72.3154 112.524L72.3613 112.43L78.2852 100.428C79.2838 98.4313 81.2791 97.191 83.4941 97.1909Z'
            stroke='url(#paint3_linear_747_32681)'
            strokeOpacity='0.5'
            strokeWidth='0.39801'
          />
        </g>
        <defs>
          <filter
            id='filter0_f_747_32681'
            x='14.6717'
            y='0.881622'
            width='138.75'
            height='134.682'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='BackgroundImageFix'
              result='shape'
            />
            <feGaussianBlur
              stdDeviation='19.9005'
              result='effect1_foregroundBlur_747_32681'
            />
          </filter>
          <clipPath
            id='bgblur_0_747_32681_clip_path'
            transform='translate(47.5112 -4.98002)'
          >
            <path d='M158.664 139.863C151.993 139.863 146.565 134.484 146.565 127.874C146.565 121.255 151.993 115.877 158.664 115.877C160.282 115.877 161.834 115.241 162.971 114.113C164.117 112.978 164.759 111.44 164.759 109.838L164.751 88.0819C164.751 69.6917 149.645 54.7314 131.085 54.7314H35.9126C17.3527 54.7314 2.24641 69.6917 2.24641 88.0819L2.23828 110.538C2.23828 112.141 2.88024 113.679 4.02601 114.814C5.16365 115.941 6.71573 116.577 8.33281 116.577C15.2318 116.577 20.4325 121.432 20.4325 127.874C20.4325 134.484 15.0043 139.863 8.33281 139.863C4.96863 139.863 2.23828 142.568 2.23828 145.902V167.642C2.23828 186.032 17.3365 201 35.9045 201H131.093C149.661 201 164.759 186.032 164.759 167.642V145.902C164.759 142.568 162.029 139.863 158.664 139.863Z' />
          </clipPath>
          <filter
            id='filter2_d_747_32681'
            x='23.9422'
            y='67.141'
            width='119.108'
            height='116.795'
            filterUnits='userSpaceOnUse'
            colorInterpolationFilters='sRGB'
          >
            <feFlood floodOpacity='0' result='BackgroundImageFix' />
            <feColorMatrix
              in='SourceAlpha'
              type='matrix'
              values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
              result='hardAlpha'
            />
            <feOffset dx='9.95025' dy='9.95025' />
            <feGaussianBlur stdDeviation='9.95025' />
            <feColorMatrix
              type='matrix'
              values='0 0 0 0 0.15375 0 0 0 0 0.522433 0 0 0 0 0.9 0 0 0 0.5 0'
            />
            <feBlend
              mode='normal'
              in2='BackgroundImageFix'
              result='effect1_dropShadow_747_32681'
            />
            <feBlend
              mode='normal'
              in='SourceGraphic'
              in2='effect1_dropShadow_747_32681'
              result='shape'
            />
          </filter>
          <clipPath
            id='bgblur_1_747_32681_clip_path'
            transform='translate(-23.9422 -67.141)'
          >
            <path d='M111.375 124.523L101.794 133.851L104.061 147.04C104.451 149.323 103.533 151.574 101.656 152.923C99.7953 154.288 97.3656 154.459 95.3178 153.37L83.4944 147.154L71.6467 153.378C70.7691 153.841 69.8102 154.085 68.8594 154.085C67.6162 154.085 66.3891 153.695 65.3327 152.931C63.4638 151.574 62.5455 149.323 62.9356 147.04L65.1946 133.851L55.614 124.523C53.9563 122.914 53.3793 120.549 54.0944 118.347C54.8177 116.153 56.6866 114.593 58.9701 114.268L72.183 112.342L78.1069 100.34C79.1389 98.2756 81.2029 96.9917 83.4944 96.9917H83.5107C85.8104 96.9998 87.8744 98.2837 88.8901 100.348L94.814 112.342L108.051 114.276C110.31 114.593 112.179 116.153 112.894 118.347C113.618 120.549 113.041 122.914 111.375 124.523Z' />
          </clipPath>
          <linearGradient
            id='paint0_linear_747_32681'
            x1='65.5381'
            y1='6.24586'
            x2='56.4054'
            y2='125.202'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='#39AFFD' />
            <stop offset='1' stopColor='#477FFF' />
          </linearGradient>
          <linearGradient
            id='paint1_linear_747_32681'
            x1='28.1089'
            y1='71.7719'
            x2='146.79'
            y2='206.102'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' stopOpacity='0.25' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
          <linearGradient
            id='paint2_linear_747_32681'
            x1='108.989'
            y1='107.302'
            x2='42.8199'
            y2='109.547'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' />
            <stop offset='1' stopColor='white' stopOpacity='0.2' />
          </linearGradient>
          <linearGradient
            id='paint3_linear_747_32681'
            x1='60.1925'
            y1='116.144'
            x2='110.638'
            y2='116.434'
            gradientUnits='userSpaceOnUse'
          >
            <stop stopColor='white' />
            <stop offset='1' stopColor='white' stopOpacity='0' />
          </linearGradient>
        </defs>
      </svg>
      <div className='absolute top-[87px] right-[23px] z-10'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          width='101'
          height='97'
          viewBox='0 0 101 97'
          fill='none'
        >
          <g filter='url(#filter0_d_747_32687)' data-figma-bg-blur-radius='29.8507'>
            <path
              d='M68.3749 37.5227L58.7943 46.8514L61.0614 60.04C61.4515 62.3234 60.5332 64.5743 58.6561 65.9232C56.7953 67.2884 54.3656 67.459 52.3178 66.3701L40.4944 60.1537L28.6467 66.3783C27.7691 66.8414 26.8102 67.0852 25.8594 67.0852C24.6162 67.0852 23.3891 66.6952 22.3327 65.9313C20.4638 64.5743 19.5455 62.3234 19.9356 60.04L22.1946 46.8514L12.614 37.5227C10.9563 35.9138 10.3793 33.5491 11.0944 31.3469C11.8177 29.1529 13.6866 27.5927 15.9701 27.2677L29.183 25.3418L35.1069 13.3396C36.1389 11.2756 38.2029 9.9917 40.4944 9.9917H40.5107C42.8104 9.99983 44.8744 11.2837 45.8901 13.3478L51.814 25.3418L65.0513 27.2758C67.3104 27.5927 69.1793 29.1529 69.8944 31.3469C70.6177 33.5491 70.0407 35.9138 68.3749 37.5227Z'
              fill='url(#paint0_linear_747_32687)'
            />
            <path
              d='M40.4941 10.1909H40.5098L40.9238 10.2065C42.9796 10.3566 44.791 11.5648 45.7119 13.436L51.6357 25.4302L51.6816 25.5239L51.7852 25.5386L65.0225 27.4731H65.0234C67.0705 27.7603 68.7862 29.103 69.5625 31.0181L69.7051 31.4087C70.4047 33.5389 69.8468 35.8239 68.2363 37.3794V37.3804L58.6553 46.7085L58.5801 46.7817L58.5986 46.8853L60.8652 60.0737C61.2424 62.2824 60.3548 64.457 58.54 65.7612L58.5381 65.7632C56.7394 67.0825 54.3917 67.248 52.4111 66.1948L52.4102 66.1938L40.5869 59.978L40.4941 59.9292L40.4023 59.978L28.5537 66.2017V66.2026C27.7034 66.6514 26.7761 66.8862 25.8594 66.8862C24.6583 66.8862 23.4719 66.5095 22.4492 65.77C20.6419 64.4575 19.7547 62.2823 20.1318 60.0737L22.3906 46.8853L22.4082 46.7817L22.333 46.7085L12.7529 37.3804C11.1502 35.8248 10.5913 33.5384 11.2832 31.4077C11.983 29.2864 13.7898 27.7787 15.998 27.4644H15.999L29.2119 25.5386L29.3154 25.5239L29.3613 25.4302L35.2852 13.4282C36.2838 11.4313 38.2791 10.191 40.4941 10.1909Z'
              stroke='url(#paint1_linear_747_32687)'
              strokeOpacity='0.5'
              strokeWidth='0.39801'
            />
          </g>
          <defs>
            <filter
              id='filter0_d_747_32687'
              x='-19.0578'
              y='-19.859'
              width='119.108'
              height='116.795'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feColorMatrix
                in='SourceAlpha'
                type='matrix'
                values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
                result='hardAlpha'
              />
              <feOffset dx='9.95025' dy='9.95025' />
              <feGaussianBlur stdDeviation='9.95025' />
              <feColorMatrix
                type='matrix'
                values='0 0 0 0 0.15375 0 0 0 0 0.522433 0 0 0 0 0.9 0 0 0 0.5 0'
              />
              <feBlend
                mode='normal'
                in2='BackgroundImageFix'
                result='effect1_dropShadow_747_32687'
              />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='effect1_dropShadow_747_32687'
                result='shape'
              />
            </filter>
            <clipPath
              id='bgblur_0_747_32687_clip_path'
              transform='translate(19.0578 19.859)'
            >
              <path d='M68.3749 37.5227L58.7943 46.8514L61.0614 60.04C61.4515 62.3234 60.5332 64.5743 58.6561 65.9232C56.7953 67.2884 54.3656 67.459 52.3178 66.3701L40.4944 60.1537L28.6467 66.3783C27.7691 66.8414 26.8102 67.0852 25.8594 67.0852C24.6162 67.0852 23.3891 66.6952 22.3327 65.9313C20.4638 64.5743 19.5455 62.3234 19.9356 60.04L22.1946 46.8514L12.614 37.5227C10.9563 35.9138 10.3793 33.5491 11.0944 31.3469C11.8177 29.1529 13.6866 27.5927 15.9701 27.2677L29.183 25.3418L35.1069 13.3396C36.1389 11.2756 38.2029 9.9917 40.4944 9.9917H40.5107C42.8104 9.99983 44.8744 11.2837 45.8901 13.3478L51.814 25.3418L65.0513 27.2758C67.3104 27.5927 69.1793 29.1529 69.8944 31.3469C70.6177 33.5491 70.0407 35.9138 68.3749 37.5227Z' />
            </clipPath>
            <linearGradient
              id='paint0_linear_747_32687'
              x1='65.9889'
              y1='20.3016'
              x2='-0.180059'
              y2='22.5465'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' />
              <stop offset='1' stopColor='white' stopOpacity='0.2' />
            </linearGradient>
            <linearGradient
              id='paint1_linear_747_32687'
              x1='17.1925'
              y1='29.1438'
              x2='67.6379'
              y2='29.4336'
              gradientUnits='userSpaceOnUse'
            >
              <stop stopColor='white' />
              <stop offset='1' stopColor='white' stopOpacity='0' />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
}
