'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { TechnicalExcellenceIcon } from '../home/<USER>/TechnicalExcellenceIcon';
import { useEffect, useState } from 'react';
import { getMedia } from '@/services/directus';
import { motion } from 'framer-motion';

const socials = [
  {
    Icon: (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='7'
        height='13'
        viewBox='0 0 7 13'
        fill='none'
      >
        <path
          d='M5.0932 2.02078H6.70158C6.84955 2.02078 6.96964 1.90762 6.96964 1.76819V0.252598C6.96964 0.113164 6.84955 0 6.70158 0H5.0932C3.46766 0 2.1445 1.24632 2.1445 2.77858V4.54676H0.268063C0.120092 4.54676 0 4.65993 0 4.79936V6.31495C0 6.45438 0.120092 6.56755 0.268063 6.56755H2.1445V11.8721C2.1445 12.0115 2.2646 12.1247 2.41257 12.1247H4.02095C4.16892 12.1247 4.28901 12.0115 4.28901 11.8721V6.56755H6.16545C6.28072 6.56755 6.38312 6.49783 6.42011 6.39477L6.95624 4.87918C6.98358 4.80239 6.96964 4.71752 6.91924 4.65134C6.86831 4.58566 6.78789 4.54676 6.70158 4.54676H4.28901V2.77858C4.28901 2.36078 4.64982 2.02078 5.0932 2.02078Z'
          fill='#DFDFDF'
        />
      </svg>
    ),
    label: 'Facebook',
    key: 'facebook',
  },
  {
    Icon: (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='12'
        height='12'
        viewBox='0 0 12 12'
        fill='none'
      >
        <path
          d='M1.48355 2.9671C2.30289 2.9671 2.9671 2.30289 2.9671 1.48355C2.9671 0.664208 2.30289 0 1.48355 0C0.664208 0 0 0.664208 0 1.48355C0 2.30289 0.664208 2.9671 1.48355 2.9671Z'
          fill='#DFDFDF'
        />
        <path
          d='M2.71984 3.95801H0.247258C0.110772 3.95801 0 4.06878 0 4.20527V11.623C0 11.7595 0.110772 11.8703 0.247258 11.8703H2.71984C2.85633 11.8703 2.9671 11.7595 2.9671 11.623V4.20527C2.9671 4.06878 2.85633 3.95801 2.71984 3.95801Z'
          fill='#DFDFDF'
        />
        <path
          d='M10.0876 3.5464C9.03077 3.18442 7.70893 3.50239 6.91622 4.07257C6.88902 3.96625 6.7921 3.88712 6.67687 3.88712H4.20429C4.0678 3.88712 3.95703 3.9979 3.95703 4.13438V11.5521C3.95703 11.6886 4.0678 11.7994 4.20429 11.7994H6.67687C6.81336 11.7994 6.92413 11.6886 6.92413 11.5521V6.22124C7.3237 5.87706 7.83849 5.76728 8.25982 5.94629C8.66829 6.11888 8.9022 6.5402 8.9022 7.10148V11.5521C8.9022 11.6886 9.01297 11.7994 9.14946 11.7994H11.622C11.7585 11.7994 11.8693 11.6886 11.8693 11.5521V6.6035C11.8411 4.57153 10.8852 3.81937 10.0876 3.5464Z'
          fill='#DFDFDF'
        />
      </svg>
    ),
    label: 'LinkedIn',
    key: 'linkedin',
  },
];

export default function CardInformation() {
  const t = useTranslations('contact');
  const [socialLink, setSocialLink] = useState<Record<string, any>[]>([]);
  const [media, setMedia] = useState<Record<string, any>>();

  useEffect(() => {
    const requestMedia = async () => {
      const mediaRes = await getMedia();
      const formatSocial = socials.map((social) => ({
        ...social,
        href: mediaRes[social.key],
      }));
      setSocialLink(formatSocial);
      setMedia(mediaRes);
    };
    requestMedia();
  }, []);

  return (
    <div className='relative flex h-[500px] w-full overflow-hidden rounded-4xl md:h-[635px] lg:w-[491px] lg:overflow-visible'>
      <div className='absolute inset-0 z-1000 flex flex-col justify-between rounded-4xl border border-[#FFFFFF4D] bg-[#00224399] p-8 pl-8 backdrop-blur-3xl md:h-[635px] md:pt-8 md:pr-20 md:pb-6 lg:max-w-[491px]'>
        <div className='flex flex-col gap-y-6 md:gap-y-12'>
          <div className='flex flex-col gap-2.5'>
            <h1 className='text-2xl leading-[100%] font-semibold md:text-[28px]'>
              {t('card-info')}
            </h1>
            <p className='text-md font-normal text-[#C9C9C9] md:text-lg'>
              {t('card-desc')}
            </p>
          </div>
          <div className='flex max-w-[337px] flex-col gap-7 md:gap-13'>
            <div className='flex flex-col-reverse justify-between gap-4 sm:flex-row sm:items-end'>
              <div className='flex flex-row items-center gap-6'>
                <Image
                  src='/contact/phone.svg'
                  className='inline-block'
                  alt='Phone icon'
                  width={24}
                  height={24}
                />
                <motion.span
                  initial={{ filter: 'blur(8px)' }}
                  whileInView={{ filter: 'blur(0px)' }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  viewport={{ once: true }}
                  className='text-md'
                >
                  {media?.phone || ''}
                </motion.span>
              </div>
              <div className='flex h-30 w-22 flex-row justify-center'>
                <TechnicalExcellenceIcon className='scale-50' />
              </div>
            </div>
            <div className='flex flex-row items-center gap-6'>
              <Image
                src='/contact/email.svg'
                className='inline-block'
                alt='Email icon'
                width={24}
                height={24}
              />
              <motion.span
                initial={{ filter: 'blur(8px)' }}
                whileInView={{ filter: 'blur(0px)' }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className='text-md'
              >
                {media?.mail || ''}
              </motion.span>
            </div>
            <div className='flex flex-row items-center gap-6'>
              <Image
                src='/contact/location.svg'
                className='inline-block'
                alt='Location icon'
                width={24}
                height={24}
              />
              <motion.span
                initial={{ filter: 'blur(8px)' }}
                whileInView={{ filter: 'blur(0px)' }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className='text-md'
              >
                {media?.translations?.[0]?.address || ''}
              </motion.span>
            </div>
          </div>
        </div>

        <div className='flex gap-2'>
          {socialLink?.map((link, idx) => (
            <SocialLink key={idx} social={link} />
          ))}
        </div>
      </div>
      <div className='bg-orange-gradient absolute right-0 bottom-0 size-[269px] translate-x-1/3 translate-y-1/3 rounded-full'></div>
    </div>
  );
}

export const SocialLink = ({ social }: any) => {
  return (
    <a
      className='flex size-[35px] cursor-pointer items-center justify-center rounded-full border border-[#0A142F] bg-[#ffffff26] opacity-100 transition-opacity duration-200 hover:opacity-90'
      href={social.href}
      target='_blank'
      aria-label={social.label}
    >
      {social.Icon}
    </a>
  );
};
