'use client';
import clsx from 'clsx';
import { useTranslations } from 'next-intl';
import { type Control, Controller, type FieldValues, type Path } from 'react-hook-form';

type InputProps<T extends FieldValues> = {
  placeholder: string;
  control: Control<T>;
  name: Path<T>;
  label: string;
  type?: string;
  require?: boolean;
};

export default function Input<T extends FieldValues>({
  placeholder,
  control,
  name,
  label,
  type = 'text',
  require = false,
}: InputProps<T>) {
  const t = useTranslations('contact');

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => {
        const errorMessage = fieldState?.error?.message
          ? t(fieldState?.error?.message)
          : '';

        return (
          <div className='flex flex-col gap-2'>
            <label htmlFor={name} className='text-md font-normal text-gray-50'>
              {label}
              {require && <span className='text-md p-1 text-[#FF3B30]'>*</span>}
            </label>
            <input
              {...field}
              placeholder={placeholder}
              className={clsx(
                'placeholder:text-muted-foreground appearance-none rounded-[10px] border-2 border-[#D4D4D4] bg-white p-4 text-neutral-100',
                errorMessage && 'border-red-500'
              )}
              type={type}
            />
            {errorMessage && <p className='text-sm text-red-500'>{errorMessage}</p>}
          </div>
        );
      }}
    />
  );
}
