'use client';

import { Submit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Submit<PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import Input from './ContactInput';
import TextArea from './ContactTextArea';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/common/Button';
import Image from 'next/image';
import { type Contact, ContactSchema, defaultValues } from '@/types/contact';
import { zodResolver } from '@hookform/resolvers/zod';
import DOMPurify from 'isomorphic-dompurify';
import { createContactSubmission } from '@/services/directus';
import { toast } from 'sonner';
import { useState } from 'react';
import { IconLoader } from '@tabler/icons-react';
import { useRouter } from 'next/navigation';

const CONTACT_FIELD = [
  {
    placeholder: 'Alex',
    name: 'name',
    require: true,
    label: 'first-name',
  },
  {
    placeholder: '<EMAIL>',
    name: 'email',
    require: true,
    label: 'email-address',
  },
  {
    placeholder: '0901234567',
    name: 'phone',
    require: true,
    label: 'phone',
  },
];

export default function ContactForm() {
  const t = useTranslations('contact');
  const router = useRouter();
  const form = useForm<Contact>({
    resolver: zodResolver(ContactSchema),
    defaultValues: defaultValues,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const sanitizeInput = (data: Contact) => {
    Object.keys(data).forEach((key) => {
      data[key as keyof Contact] = DOMPurify.sanitize(data[key as keyof Contact], {
        USE_PROFILES: { html: false },
      });
    });
    return data;
  };

  const onValid: SubmitHandler<Contact> = async (data) => {
    try {
      setIsSubmitting(true);
      const res = await createContactSubmission(sanitizeInput(data));

      if (!res.success) throw new Error('Failed to submit contact form');
      router.push('/contact/submitted');
    } catch (error) {
      console.log('Got error in submit contact form: ', error);
      toast.error(t('submit-failed'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      onSubmit={form.handleSubmit(onValid)}
      className='2lg:min-w-xl z-10 mt-7 flex flex-col gap-y-4 lg:min-w-md'
    >
      {CONTACT_FIELD.map((field) => (
        <Input
          key={field.name}
          control={form.control}
          {...field}
          name={field.name as keyof Contact}
          label={t(field.label)}
        />
      ))}

      <TextArea
        control={form.control}
        name='message'
        label={t('message')}
        placeholder={t('placeholder-message')}
      />
      <div className='relative flex flex-col md:items-end lg:flex-row lg:items-start lg:justify-end'>
        <Button
          className='px-12'
          disabled={isSubmitting}
          type='submit'
          aria-label='Send inquiry'
        >
          {isSubmitting ? <IconLoader /> : t('send-inquiry')}
        </Button>
        <div className='relative -rotate-12 lg:absolute lg:-translate-x-1/2 lg:translate-y-10'>
          <Image
            alt=''
            src='/contact/letter_send.png'
            width={240}
            height={112}
            className='h-auto object-cover'
          />
        </div>
      </div>
    </form>
  );
}
