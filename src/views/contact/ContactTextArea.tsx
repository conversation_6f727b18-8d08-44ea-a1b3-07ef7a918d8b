import clsx from 'clsx';
import { useLayoutEffect, useRef } from 'react';
import { type Control, Controller, type FieldValues, type Path } from 'react-hook-form';

type TextAreaProps<T extends FieldValues> = {
  placeholder: string;
  control: Control<T>;
  name: Path<T>;
  maxRows?: number;
  minRows?: number;
  label: string;
  require?: boolean;
};

export default function TextArea<T extends FieldValues>({
  placeholder,
  control,
  name,
  maxRows = 5,
  minRows = 1,
  label,
  require,
}: TextAreaProps<T>) {
  const textAreaRef = useRef<HTMLTextAreaElement | null>(null);

  const calculateHeight = () => {
    const textarea = textAreaRef.current;
    if (!textarea) return;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';

    // Calculate line height based on computed styles
    const style = window.getComputedStyle(textarea);

    const lineHeight =
      Number.parseInt(style.lineHeight) || Number.parseInt(style.fontSize) * 1.2;

    const verticalPadding =
      Number.parseInt(style.paddingBottom) + Number.parseInt(style.paddingTop);

    // Calculate min and max heights
    const minHeight = lineHeight * minRows + verticalPadding;
    const maxHeight = lineHeight * maxRows + verticalPadding;

    // Set the height based on scrollHeight, but constrained by min/max
    const newHeight = Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight);
    textarea.style.height = `${newHeight}px`;

    // Show or hide scrollbar based on content
    textarea.style.overflowY = textarea.scrollHeight > newHeight ? 'auto' : 'hidden';
  };

  useLayoutEffect(() => {
    calculateHeight();
  }, []);

  return (
    <>
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState }) => {
          const errorMessage = fieldState?.error?.message;

          return (
            <div className='flex flex-col gap-2'>
              <label htmlFor={name} className='text-md font-normal text-gray-50'>
                {label}
                {require && <span className='text-md p-1 text-[#FF3B30]'>*</span>}
              </label>
              <textarea
                {...field}
                ref={textAreaRef}
                rows={1}
                placeholder={placeholder}
                className={clsx(
                  'resize-none appearance-none rounded-[10px] border-2 border-[#D4D4D4] bg-white p-4 text-neutral-100 placeholder:text-base',
                  errorMessage && 'border-red-500'
                )}
                onChange={(e) => {
                  field.onChange(e);
                  calculateHeight();
                }}
              />
              {errorMessage && (
                <p className='pt-1 text-sm text-red-500'>{errorMessage}</p>
              )}
            </div>
          );
        }}
      />
    </>
  );
}
