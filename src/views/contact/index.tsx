'use client';
import { Section } from '@/components/common/Section';
import { SlashBackground2 } from '@/components/common/SlashBackground';
import { useTranslations } from 'next-intl';
import CardInformation from './CardInformation';
import ContactForm from './ContactForm';

export default function ContactView() {
  const t = useTranslations('contact');
  return (
    <Section container center className='my-20 md:my-36'>
      <div className='relative flex justify-center'>
        <SlashBackground2 h={1100} />
      </div>
      <div className='text-center'>
        <h1 className='text-4xl leading-16 font-bold md:text-[40px]'>
          {t('contact-us')}
        </h1>
        <p className='text-md mt-2.5 md:text-lg'>{t('description')}</p>
      </div>
      <div className='bg-card-container-40 2lg:w-auto mt-7 flex w-full flex-col gap-3 overflow-hidden rounded-4xl border border-[#FFFFFF4D] p-2 backdrop-blur-2xl md:mt-14 md:w-[80%] md:gap-11 md:p-4 lg:w-full lg:flex-row lg:pr-12'>
        <CardInformation />
        <ContactForm />
      </div>
    </Section>
  );
}
