export type Job = {
  name: string;
  job_title: string;
  designation: string;
  status: 'Open' | 'Closed' | 'Draft' | string; // Extend as needed
  posted_on: string; // Consider using Date if parsing is needed
  closes_on: string | null;
  closed_on: string | null;
  company: string;
  department: string;
  department_name?: string; // Optional, can be derived from department
  employment_type: 'Full-time' | 'Part-time' | 'Contract' | string; // Extend as needed
  location: string;
  staffing_plan: string;
  planned_vacancies: number;
  job_requisition: string | null;
  vacancies: number;
  publish: number; // Could be boolean (1 = true, 0 = false)
  route: string;
  publish_applications_received: number;
  job_application_route: string;
  description?: string;
  currency: string;
  lower_range: number;
  upper_range: number;
  salary_per: 'Month' | 'Year' | 'Hour' | string; // Extend as needed
  publish_salary_range: number; // Could be boolean (1 = true, 0 = false)
  custom_expected_offer_date: string;
};
