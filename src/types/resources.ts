export type ThumbnailImage = {
  id: string;
  image: string;
};

export type Image = {
  image: string;
};

export type Category = {
  name: string;
  color: string;
};

export type Label = {
  name: string;
};

export type Post = {
  thumbnail: string;
  title: string;
  content: Content[];
  id: number;
  date_created: string;
  slug: string;
  status: string;
  description: string;
  meta_title: string;
  meta_description: string;
  header_image?: string;
  type: { label: string };
  duration?: string;
  tag: string[];
  video?: string;
};

export type Content = {
  main_section: string;
  right_section: string;
  id: number;
  right_sections: { content: string; id: string }[];
};

export const defaultPost: Post = {
  thumbnail: '',
  title: '',
  content: [],
  id: 0,
  slug: '',
  type: { label: '' },
  status: '',
  description: '',
  meta_title: '',
  meta_description: '',
  date_created: '',
  duration: '',
  tag: [],
};

export type Asset = {
  charset: string | null;
  created_on: string; // ISO date string
  description: string | null;
  duration: number | null;
  embed: string | null;
  filename_disk: string;
  filename_download: string;
  filesize: number;
  focal_point_x: number | null;
  focal_point_y: number | null;
  folder: string | null;
  height: number;
  id: string;
  location: string | null;
  metadata: Record<string, any>;
  modified_by: string | null;
  modified_on: string;
  storage: string;
  tags: string[] | null;
  title: string;
  tus_data: any | null;
  tus_id: string | null;
  type: string;
  uploaded_by: string;
  uploaded_on: string;
  width: number;
};

export type Translation = {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  languages_code: string;
  button_text: string | null;
};
