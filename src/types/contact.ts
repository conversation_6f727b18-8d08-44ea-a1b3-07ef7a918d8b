import z from 'zod';

export const ContactSchema = z.object({
  name: z.string().min(1, { message: 'require' }),
  email: z.email({ message: 'wrong-email-format' }).min(1, 'require'),
  phone: z.string().min(1, { message: 'require' }),
  message: z.string(),
});

export const defaultValues = {
  name: '',
  email: '',
  phone: '',
  message: '',
};

export type Contact = z.infer<typeof ContactSchema>;
