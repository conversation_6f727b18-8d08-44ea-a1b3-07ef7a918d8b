export function isEmptyObject(object: object = {}) {
  return !Object.keys(object).length;
}

export function extractExperienceYears(jobDescription: string): number | null {
  const cleanText = jobDescription.replace(/<[^>]*>/g, '');

  // pattern regex
  const patterns = [
    // Pattern 1: "5+ years", "3-5 years", "2+ years"
    /(\d+)\+?\s*years?/gi,

    // Pattern 2: "5+ years of experience"
    /(\d+)\+?\s*years?\s+of\s+experience/gi,

    // Pattern 3: "minimum 3 years", "at least 5 years"
    /(?:minimum|at\s+least)\s+(\d+)\s*years?/gi,

    // Pattern 4: "3-5 years", "2 to 4 years"
    /(\d+)(?:\s*[-–]\s*(\d+))?\s*years?/gi,

    // Pattern 5: "experience of 5+ years"
    /experience\s+of\s+(\d+)\+?\s*years?/gi,
  ];

  const foundYears: number[] = [];

  patterns.forEach((pattern) => {
    let match;
    while ((match = pattern.exec(cleanText)) !== null) {
      const years = parseInt(match[1]);
      if (!isNaN(years)) {
        foundYears.push(years);
      }
    }
  });

  return foundYears.length > 0 ? Math.max(...foundYears) : null;
}

export function getRoleLevel(title: string): string | null {
  const cleanText = title.replace(/<[^>]*>/g, '');
  const roleLevels = [
    'Senior',
    'Middle',
    'Junior',
    'Leader',
    'Principal',
    'Staff',
    'Associate',
    'Entry-Level',
    'Intern',
    'Director',
    'Executive',
    'Leader',
  ];
  for (const level of roleLevels) {
    if (cleanText.includes(level)) {
      return level;
    }
  }
  return null;
}
