// 'use server';
import { createDirectus, rest, authentication } from '@directus/sdk';

// Create a function to get the Directus client
export async function getDirectusClient() {
  const url = process.env.NEXT_PUBLIC_DIRECTUS_API;

  // Create the Directus client
  const directus = createDirectus(url || '')
    .with(
      rest({
        onRequest: (options) => ({
          ...options,
          cache: 'no-store',
          headers: {
            'Content-Type': 'application/json',
            // Add Authorization header if token exists
            ...(process.env.DIRECTUS_TOKEN
              ? {
                  Authorization: `Bearer ${process.env.DIRECTUS_TOKEN}`,
                }
              : {}),
          },
        }),
      })
    )
    .with(authentication());

  // If no token is provided but email/password are available, try to authenticate
  if (
    !process.env.DIRECTUS_TOKEN &&
    process.env.NEXT_PUBLIC_DIRECTUS_EMAIL &&
    process.env.NEXT_PUBLIC_DIRECTUS_PASSWORD
  ) {
    try {
      await directus.login({
        email: process.env.NEXT_PUBLIC_DIRECTUS_EMAIL,
        password: process.env.NEXT_PUBLIC_DIRECTUS_PASSWORD,
      });
    } catch (error) {
      console.error('Failed to authenticate with Directus:', error);
    }
  }

  return directus;
}
