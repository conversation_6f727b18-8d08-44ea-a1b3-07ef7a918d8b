import { BreadcrumbList, WithContext } from 'schema-dts';

export const breadCrumbsLd = (items: { name: string; item?: string }[]) =>
  <WithContext<BreadcrumbList>>{
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Vietnam Silicon',
      },
      [
        ...items.map((item, index) => ({
          '@type': 'ListItem',
          position: index + 2,
          name: item.name,
          item: item.item,
        })),
      ],
    ],
  };
