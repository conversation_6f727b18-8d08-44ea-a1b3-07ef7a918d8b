import { type Post } from '@/types/resources';
import { flatten } from 'lodash-es';
import { TAB } from '../constants/resources';

export const getPaginationButtons = (totalPages: number, currentPage: number) => {
  const pages = [];
  const maxVisiblePages = 3;

  if (totalPages <= maxVisiblePages + 2) {
    // Show all pages if the total number of pages is less than or equal to maxVisiblePages + 2
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }

    return pages;
  }

  // Show the first page, last page, and middle pages with ellipses.
  // Add ellipsis if there are pages between the first page and (currentPage - 1).
  // Add ellipsis if there are pages between (currentPage + 1) and the last page.
  pages.push(1);

  if (currentPage - 1 - 1 > 1) {
    pages.push('...');
  }

  const startPage = Math.max(2, currentPage - 1);
  const endPage = Math.min(totalPages - 1, currentPage + 1);
  // Add the middle pages between the first page and last page.
  // This will show the current page and one page before and after it.
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }

  if (currentPage + 1 + 1 < totalPages) pages.push('...');

  pages.push(totalPages);
  return pages;
};

export const formatDate = (pDate: string) => {
  const date = new Date(pDate);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

export const getIgnoreIDs = (
  popularVideos: Post[][] | undefined,
  highlightResources: Post[] | undefined,
  isVideo: boolean
) => {
  if (!popularVideos && !highlightResources) return [];
  const videos = flatten(popularVideos).concat(
    highlightResources?.filter((resource) => resource.type.label === TAB.VIDEO) ?? []
  );
  const articles = highlightResources?.filter(
    (resource) => resource.type.label === 'Article'
  );

  return (isVideo ? videos : articles)?.map((article) => article.id);
};
