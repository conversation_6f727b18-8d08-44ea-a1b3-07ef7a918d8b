import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import { getMetadata, getMetadataWithTag } from '@/services/directus';
import ResourcesView from '@/views/resources/main';
import { Metadata } from 'next';
import React from 'react';

type ResourcesPropsType = {
  searchParams?: Promise<{ tags: string }>;
};

export async function generateMetadata({
  searchParams,
}: ResourcesPropsType): Promise<Metadata> {
  const { tags = '' } = (await searchParams) || {};
  if (tags) return getMetadataWithTag(tags);
  return getMetadata('resources');
}

const ResourceSEO = () => <BreadcrumbsLDMeta items={[{ name: 'Resources' }]} />;

async function Resources() {
  return (
    <>
      <ResourceSEO />
      <ResourcesView />
    </>
  );
}

export default Resources;
