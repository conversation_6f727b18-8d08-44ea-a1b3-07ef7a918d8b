import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import { getArticleByID } from '@/services/directus';
import ResourcesDetailView from '@/views/resources/detail';
import { Metadata } from 'next';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  const detailPost = await getArticleByID(id, [
    'meta_title',
    'meta_description',
    'title',
    'description',
  ]);

  return {
    title: detailPost?.meta_title || detailPost?.title,
    description: detailPost?.meta_description || detailPost?.description,
  };
}

function ResourceDetailSEO() {
  return <BreadcrumbsLDMeta items={[{ name: 'Resources' }]} />;
}

export default async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id = '' } = await params;
  const detailArticle = await getArticleByID(id);

  return (
    <>
      <ResourcesDetailView article={detailArticle} />
      <ResourceDetailSEO />
    </>
  );
};
