import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import { getMetadata } from '@/services/directus';
import CareersView from '@/views/careers/main';
import { Metadata } from 'next';

export async function generateMetadata(): Promise<Metadata> {
  return await getMetadata('career');
}

function CareerSEO() {
  return <BreadcrumbsLDMeta items={[{ name: 'Career' }]} />;
}

export default () => {
  return (
    <>
      <CareerSEO />
      <CareersView />
    </>
  );
};
