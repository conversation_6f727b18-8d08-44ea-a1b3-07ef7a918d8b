import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import { getMetadata } from '@/services/directus';
import OpenPositionsView from '@/views/careers/open-positions';
import { Metadata } from 'next';
export async function generateMetadata(): Promise<Metadata> {
  return await getMetadata('job-listing');
}

function OpenPositionsSEO() {
  return <BreadcrumbsLDMeta items={[{ name: 'Job Openings' }]} />;
}

export default function OpenPositionsPage() {
  return (
    <>
      <OpenPositionsSEO />
      <OpenPositionsView />
    </>
  );
}
