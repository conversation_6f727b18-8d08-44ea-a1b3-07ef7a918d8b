import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import SuccessfullySection from '@/components/sections/SuccessfullySection';
import { getJobByName } from '@/services/erp';
import { Job } from '@/types/job';

function generateJobSchema(job?: Job) {
  return {
    '@context': 'https://schema.org/',
    '@type': 'JobPosting',
    title: job?.job_title,
    description: `We are looking for a ${job?.job_title} to join our team and help us maintain high availability and optimize our systems.`,
    validThrough: job?.custom_expected_offer_date,
    employmentType: job?.employment_type,
    hiringOrganization: {
      '@type': 'Organization',
      name: 'Vietnam Silicon',
    },
    jobLocation: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        addressLocality: job?.location,
      },
    },
  };
}

function JobSEO({ job }: { job?: Job }) {
  const jobSchema = generateJobSchema(job);
  return (
    <>
      <script type='application/ld+json'>{JSON.stringify(jobSchema)}</script>
      <BreadcrumbsLDMeta
        items={[{ name: 'Career', item: '/career' }, { name: job?.job_title || '' }]}
      />
    </>
  );
}

export default async function SubmittedPage({
  params,
}: {
  params: Promise<{ name: string }>;
}) {
  const { name } = await params;
  const job = await getJobByName(name);
  return (
    <>
      <JobSEO job={job?.data?.[0]} />
      <SuccessfullySection
        title='career.submitted.title'
        description='career.submitted.description'
        buttonText='career.submitted.button'
        link='/careers/open-positions'
      />
    </>
  );
}
