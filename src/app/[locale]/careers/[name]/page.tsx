import { getAllDepartments, getAllOpeningJobs, getJobByName } from '@/services/erp';
import { Job } from '@/types/job';
import { getCdnUrl } from '@/utils/url/asset';
import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import CareerDetailView from '@/views/careers/detail';
import { Department } from '@/types/department';
type Props = {
  params: Promise<{ name: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};
export async function generateMetadata({ params }: Props) {
  const { name } = await params;
  const job = await getJobByName(name);
  return {
    title: job?.data?.[0]?.job_title,
    description: `We are looking for a ${job?.data?.[0]?.job_title} to join our team and help us maintain high availability and optimize our systems.`,
    openGraph: {
      images: [getCdnUrl('/logo/logo-icon.png')],
    },
  };
}

function generateJobSchema(job?: Job) {
  return {
    '@context': 'https://schema.org/',
    '@type': 'JobPosting',
    title: job?.job_title,
    description: `We are looking for a ${job?.job_title} to join our team and help us maintain high availability and optimize our systems.`,
    validThrough: job?.custom_expected_offer_date,
    employmentType: job?.employment_type,
    hiringOrganization: {
      '@type': 'Organization',
      name: 'Vietnam Silicon',
    },
    jobLocation: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        addressLocality: job?.location,
      },
    },
  };
}

function JobSEO({ job }: { job?: Job }) {
  const jobSchema = generateJobSchema(job);
  return (
    <>
      <script type='application/ld+json'>{JSON.stringify(jobSchema)}</script>
      <BreadcrumbsLDMeta
        items={[{ name: 'Career', item: '/career' }, { name: job?.job_title || '' }]}
      />
    </>
  );
}

export default async function JobDetail({
  params,
}: {
  params: Promise<{ name: string }>;
}) {
  const { name } = await params;
  const job = await getJobByName(name);
  const relatedJobsRes = await getAllOpeningJobs();
  const departmentsRes = await getAllDepartments();

  const relatedJobsData: Job[] = relatedJobsRes?.data || [];
  const departmentNames = departmentsRes?.data || [];

  const departments: Department[] = Array.from(
    new Set(relatedJobsData.map((job) => job.department))
  ).map((department) => ({
    name: department,
    department_name:
      departmentNames.find((d) => d.name === department)?.department_name || department,
  }));

  const relatedJobs = relatedJobsData.map<Job>((job) => {
    const department_name =
      departments.find((d) => d.name === job.department)?.department_name ||
      job.department;

    return { ...job, department_name };
  });

  const filteredRelatedJobs = relatedJobs
    .filter(
      (relatedJob: Job) =>
        relatedJob.department === job?.data?.[0]?.department &&
        relatedJob.name !== job?.data?.[0]?.name
    )
    .slice(0, 6);
  return (
    <>
      <JobSEO job={job?.data?.[0]} />
      <CareerDetailView
        job={job?.data?.[0] || ({} as Job)}
        relatedJobs={filteredRelatedJobs || []}
        url={`${process.env.NEXT_PUBLIC_WEB_URL}/careers/${job?.data?.[0]?.route?.split('/')?.pop()}`}
      />
    </>
  );
}
