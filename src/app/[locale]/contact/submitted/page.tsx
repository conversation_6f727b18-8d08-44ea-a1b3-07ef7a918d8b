import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import SuccessfullySection from '@/components/sections/SuccessfullySection';
import { getMetadata } from '@/services/directus';
import ContactView from '@/views/contact';
import { Metadata } from 'next';

export async function generateMetadata(): Promise<Metadata> {
  return await getMetadata('contact');
}

function ContactSEO() {
  return <BreadcrumbsLDMeta items={[{ name: 'Contact' }]} />;
}

export default () => {
  return (
    <>
      <ContactSEO />
      <SuccessfullySection
        title='contact.submitted.title'
        description='contact.submitted.description'
        buttonText='contact.submitted.button'
        link='/'
      />
    </>
  );
};
