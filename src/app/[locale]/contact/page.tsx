import { BreadcrumbsLDMeta } from '@/components/meta/BreadcrumbsLDMeta';
import { getMetadata } from '@/services/directus';
import ContactView from '@/views/contact';
import { Metadata } from 'next';

export async function generateMetadata(): Promise<Metadata> {
  return await getMetadata('contact');
}

function ContactSEO() {
  return <BreadcrumbsLDMeta items={[{ name: 'Contact' }]} />;
}

export default () => {
  return (
    <>
      <ContactSEO />
      <ContactView />
    </>
  );
};
