import { LayoutProvider } from '@/components/context/LayoutContext';
import Footer from '@/components/layout/Footer/index';
import Header from '@/components/layout/Header';
import { AnimateRouteChange } from '@/components/motion/AnimateRouteChange';
import { SmoothScroll } from '@/components/motion/SmoothScroll';
import { routing } from '@/i18n/routing';
import { getDynamicSections, getMedia, getMetadata } from '@/services/directus';
import { getCdnUrl } from '@/utils/url/asset';
import { GoogleAnalytics, GoogleTagManager } from '@next/third-parties/google';
import type { Metadata } from 'next';
import { hasLocale, NextIntlClientProvider } from 'next-intl';
import { getMessages, setRequestLocale } from 'next-intl/server';
import { Inter } from 'next/font/google';
import { notFound } from 'next/navigation';
import NextTopLoader from 'nextjs-toploader';
import { WebSite, WithContext } from 'schema-dts';
import { Toaster } from 'sonner';
import '../globals.css';
import { LoadingIndicator } from '@/components/layout/LoadingIndicator';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
});

const jsonLd: WithContext<WebSite> = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: 'Vietnam Silicon',
  url: process.env.NEXT_PUBLIC_WEB_URL,
  description:
    'Join us on a journey to digital excellence. Together, we can transform your business and unlock new possibilities in the digital era.',
  publisher: {
    '@type': 'Organization',
    name: 'Vietnam Silicon',
    logo: {
      '@type': 'ImageObject',
      url: `${getCdnUrl('/logo/logo-icon.png')}`,
    },
  },
};
export async function generateMetadata(): Promise<Metadata> {
  return await getMetadata('root');
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  const media = await getMedia();
  const navigationRequest = [
    getDynamicSections('header navigation', { navigation: ['id', 'label', 'href'] }),
    getDynamicSections('footer navigation', { navigation: ['id', 'label', 'href'] }),
  ];
  const [headerNavigation, footerNavigation] = await Promise.all(navigationRequest);

  // Enable static rendering
  setRequestLocale(locale);
  return (
    <html lang={locale} className=''>
      <head>
        <link rel='icon' href='/favicon.ico' sizes='any' />
        <script type='application/ld+json'>{JSON.stringify(jsonLd)}</script>
      </head>
      <body className={`${inter.className} overflow-x-hidden antialiased`}>
        <NextTopLoader color='#FF6F20' initialPosition={0.3} showSpinner={false} />
        <LayoutProvider>
          <NextIntlClientProvider>
            <LoadingIndicator>
              <div className='flex min-h-screen w-screen flex-col overflow-x-hidden'>
                <Header navigation={headerNavigation?.data || []} media={media} />
                <AnimateRouteChange>
                  <SmoothScroll>{children}</SmoothScroll>
                </AnimateRouteChange>
                <Footer media={media} navigation={footerNavigation?.data || []} />
                <Toaster richColors position='bottom-right' />
              </div>
            </LoadingIndicator>
          </NextIntlClientProvider>
        </LayoutProvider>
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID as string} />
        <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GA_ID as string} />
      </body>
    </html>
  );
}
