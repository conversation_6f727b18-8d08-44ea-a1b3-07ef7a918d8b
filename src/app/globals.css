@import 'tailwindcss';
@custom-variant dark (&:is(.dark *));

:root {
  --background: #002243;
  --foreground: #ffffff;

  /* Color Palette */
  --blue-20: #e7f0fa; /* Primary/50 */
  --blue-50: #fcfdff; /* color.primary.bg.subtle */
  --blue-100: #f5f9ff; /* color.primary.bg.default */
  --blue-200: #eaf3ff; /* color.primary.surface.subtle */
  --blue-300: #dcebff; /* color.primary.surface.hover */
  --blue-400: #cbe2ff; /* color.primary.surface.active */
  --blue-500: #b7d4ff; /* color.primary.border.subtle */
  --blue-600: #9ec2f9; /* color.primary.border.default */
  --blue-700: #7aaaf3; /* color.primary.border.strong */
  --blue-800: #327fef; /* color.primary.solid.default */
  --blue-850: #2271e1; /* color.primary.solid.hover */
  --blue-900: #1d6cdb; /* color.primary.text.default */
  --blue-950: #103263; /* color.primary.text.onSolid */

  --orange-50: #fffcfa; /* color.highlight.bg.subtle */
  --orange-100: #fff5eb; /* color.highlight.bg.default */
  --orange-200: #ffe9d2; /* color.highlight.surface.subtle */
  --orange-300: #ffd8b7; /* color.highlight.surface.hover */
  --orange-400: #ffcba0; /* color.highlight.surface.active */
  --orange-500: #ffbb86; /* color.highlight.border.subtle */
  --orange-600: #ffa155; /* color.highlight.border.default */
  --orange-700: #ff860b; /* color.highlight.border.strong */
  --orange-800: #ff8300; /* color.highlight.solid.default */
  --orange-850: #f27700; /* color.highlight.solid.hover */
  --orange-900: #c75900; /* color.highlight.text.default */
  --orange-950: #5f2900; /* color.highlight.text.onSolid */

  --gray-20: #ececec; /* vien xam */
  --gray-50: #fdfdfd; /* color.neutral.bg.subtle */
  --gray-100: #f9f9f9; /* color.neutral.bg.default */
  --gray-200: #f0f0f0; /* color.neutral.surface.subtle */
  --gray-300: #e7e7e7; /* color.neutral.surface.hover */
  --gray-400: #dfdfdf; /* color.neutral.surface.active */
  --gray-500: #d5d5d5; /* color.neutral.border.subtle */
  --gray-600: #c8c8c8; /* color.neutral.border.default */
  --gray-700: #b4b4b4; /* color.neutral.border.strong */
  --gray-800: #656565; /* color.neutral.text.weak */
  --gray-850: #3f3f3f; /* color.neutral.solid.hover */
  --gray-900: #2b2b2b; /* color.neutral.solid.default & color.neutral.text.default & color.neutral.text.strong */
  --gray-950: #18191c; /* Gray/900 */

  /* Extended Color Palette */
  --neutral-20: #d6ddeb; /* Neutrals - 20 */
  --neutral-60: #7c8493; /* Neutrals - 60 */
  --neutral-100: #25324b; /* Neutrals - 100 */
  --background-highlight: #efefef; /* Background Highlight */
  --card-container: #ffffff;
  --card-data-bg: #000000;
  --black: #000;
  --transparent: transparent;
  --secondary-400: #97989f;
  --secondary-800: #181a2a;
  --card-container-40: #00000066;
}

@theme {
  --font-display: 'Inter', 'sans-serif';
  --breakpoint-2lg: 75rem;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--orange-800);

  /* Primary Colors */
  --color-blue-20: var(--blue-20);
  --color-blue-50: var(--blue-50);
  --color-blue-100: var(--blue-100);
  --color-blue-200: var(--blue-200);
  --color-blue-300: var(--blue-300);
  --color-blue-400: var(--blue-400);
  --color-blue-500: var(--blue-500);
  --color-blue-600: var(--blue-600);
  --color-blue-700: var(--blue-700);
  --color-blue-800: var(--blue-800);
  --color-blue-850: var(--blue-850);
  --color-blue-900: var(--blue-900);
  --color-blue-950: var(--blue-950);
  /* 2nd Colors */
  --color-orange-50: var(--orange-50);
  --color-orange-100: var(--orange-100);
  --color-orange-200: var(--orange-200);
  --color-orange-300: var(--orange-300);
  --color-orange-400: var(--orange-400);
  --color-orange-500: var(--orange-500);
  --color-orange-600: var(--orange-600);
  --color-orange-700: var(--orange-700);
  --color-orange-800: var(--orange-800);
  --color-orange-850: var(--orange-850);
  --color-orange-900: var(--orange-900);
  --color-orange-950: var(--orange-950);
  /* Gray Colors */
  --color-gray-20: var(--gray-20);
  --color-gray-50: var(--gray-50);
  --color-gray-100: var(--gray-100);
  --color-gray-200: var(--gray-200);
  --color-gray-300: var(--gray-300);
  --color-gray-400: var(--gray-400);
  --color-gray-500: var(--gray-500);
  --color-gray-600: var(--gray-600);
  --color-gray-700: var(--gray-700);
  --color-gray-850: var(--gray-850);
  --color-gray-800: var(--gray-800);
  --color-gray-900: var(--gray-900);

  /* Extend Color Palette */
  --color-neutral-20: var(--neutral-20);
  --color-neutral-60: var(--neutral-60);
  --color-neutral-100: var(--neutral-100);
  --color-card-container: var(--card-container);
  --color-card-data-bg: var(--card-data-bg);
  --color-background-highlight: var(--background-highlight);

  --color-secondary-800: var(--secondary-800);
  --color-secondary-400: var(--secondary-400);
  --color-card-container-40: var(--card-container-40);

  /* Extend text md */
  --text-md: 1rem;
  --text-md--line-height: 1.5rem;

  --animate-aurora: aurora 50s linear infinite;
  --animate-aurora2: aurora 20s linear infinite;
  --text-md--line-height: 1.5rem;
  --animate-in: animate-in 0.3s ease-out;
  --animate-out: animate-out 0.1s ease-in;

  --animate-star-movement-bottom: star-movement-bottom linear infinite alternate;
  --animate-star-movement-top: star-movement-top linear infinite alternate;
  --animate-star-movement-border: star-movement-border linear infinite alternate;

  @keyframes aurora {
    from {
      background-position:
        50% 50%,
        50% 50%;
    }
    to {
      background-position:
        350% 50%,
        350% 50%;
    }
  }

  @keyframes animate-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes animate-out {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
}

@utility card-shadow {
  box-shadow:
    0px 2.71px 4.4px 0px rgba(192, 192, 192, 0.027),
    0px 6.86px 11.12px 0px rgba(192, 192, 192, 0.039),
    0px 14px 22.68px 0px rgba(192, 192, 192, 0.05),
    0px 28.84px 46.72px 0px rgba(192, 192, 192, 0.07),
    0px 79px 128px 0px rgba(192, 192, 192, 0.1);
}

@keyframes star-movement-bottom {
  0% {
    transform: translate(0%, 0%);
    opacity: 1;
  }
  100% {
    transform: translate(-100%, 0%);
    opacity: 0;
  }
}

@keyframes star-movement-top {
  0% {
    transform: translate(0%, 0%);
    opacity: 1;
  }
  100% {
    transform: translate(100%, 0%);
    opacity: 0;
  }
}

@layer base {
  * {
    @apply font-display;
  }

  body {
    background: var(--background);
    color: var(--foreground);
  }
}

@layer components {
  .process-flow {
    @apply relative inline-block h-full min-h-12 w-full text-gray-50 [filter:url(#round)];
  }
  .process-flow::before {
    @apply block h-full w-full bg-blue-800 content-[""];
  }

  .process-flow.start::before {
    clip-path: polygon(
      calc(100% - 16px) 0,
      100% 50%,
      calc(100% - 16px) 100%,
      0 100%,
      0 0
    );
  }

  .process-flow.middle::before {
    clip-path: polygon(
      calc(100% - 16px) 0,
      100% 50%,
      calc(100% - 16px) 100%,
      0 100%,
      16px 50%,
      0 0
    );
  }

  .process-flow.end::before {
    clip-path: polygon(100% 0, 100% 100%, 0 100%, 16px 50%, 0 0);
  }
}

/* Tiptap Editor */
.tiptap * {
  @apply font-display text-gray-50;
}

.tiptap p {
  @apply text-base leading-6 text-gray-50;
}

.tiptap strong {
  @apply font-semibold;
}

.tiptap p + ol {
  margin-block: 0.75rem;
}

.tiptap li {
  list-style: disc;
  margin-left: 2rem;
}

::-webkit-scrollbar {
  width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #ffffff13;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--blue-200);
  border-radius: 10px;
  transition: background 0.3s ease;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #ffffff;
}

/* Article page */
.highlight-article-section {
  display: inline-block;
  font-size: 18px;
  border-left: 4px solid #ff8300;
  margin-left: 24px;
  padding-left: 16px;
}

.highlight-article-right-section {
  display: inline-block;
  font-size: 16px;
  border-left: 3px solid #ff8300;
  margin-left: 24px;
  padding-left: 16px;
}

.video-dialog::-webkit-scrollbar {
  width: 4px;
}

/* Handle */
.video-dialog::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to bottom,
    var(--color-card-container-40) 4%,
    var(--blue-200) 20%,
    var(--blue-200) 80%,
    transparent 100%
  );
  border-radius: 32px;
  transition: background 0.3s ease;
  background-clip: padding-box;
  border: 1px solid transparent;
}

.video-dialog::-webkit-scrollbar-track {
  background-color: linear-gradient(
    to bottom,
    var(--color-card-container-40) 60%,
    var(--blue-200) 80%,
    transparent 100%
  );
}
