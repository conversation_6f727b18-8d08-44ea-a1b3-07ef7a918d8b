import { MetadataRoute } from 'next';
import { getDirectusClient } from '@/utils/directus';
import { readItems } from '@directus/sdk';
import { locales } from '@/i18n/config';
import { DATA_MODEL } from '@/utils/constants/directus';
import { getAllOpeningJobs } from '@/services/erp';
import { Job } from '@/types/job';

const BASE_URL = process.env.NEXT_PUBLIC_WEB_URL;

interface ResourceItem {
  slug: string;
  date_updated?: string;
  date_created?: string;
  status: string;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Only generate sitemap for production domain
  if (BASE_URL !== 'https://vnsilicon.net') {
    return [];
  }

  const directus = await getDirectusClient();

  // Static routes for each locale
  const staticRoutes = locales.flatMap((locale) => [
    {
      url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}`,
      lastModified: new Date('2025-08-08'),
      changeFrequency: 'monthly' as const,
      priority: 1,
    },
    {
      url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/about`,
      lastModified: new Date('2025-08-08'),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/solutions`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/resources`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.8,
    },
    {
      url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/careers`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/careers/open-positions`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    },
    {
      url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
  ]);

  // Dynamic routes - Resources/Posts
  let resourceRoutes: MetadataRoute.Sitemap = [];
  try {
    const resources = (await directus.request(
      readItems(DATA_MODEL.RESOURCES as any, {
        fields: ['slug', 'date_updated', 'date_created', 'status'],
        filter: {
          status: {
            _eq: 'published',
          },
        },
        sort: ['-date_updated'],
      })
    )) as ResourceItem[];

    resourceRoutes = resources.flatMap((resource: ResourceItem) =>
      locales.map((locale) => ({
        url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/resources/${resource.slug}`,
        lastModified: new Date(
          resource.date_updated || resource.date_created || new Date()
        ),
        changeFrequency: 'weekly' as const,
        priority: 0.6,
      }))
    );
  } catch (error) {
    console.error('Error fetching resources for sitemap:', error);
  }

  // Dynamic routes - Career positions from ERP
  let careerRoutes: MetadataRoute.Sitemap = [];
  try {
    const jobsResponse = await getAllOpeningJobs();
    const jobs = jobsResponse?.data || [];

    careerRoutes = jobs.flatMap((job: Job) =>
      locales.map((locale) => ({
        url: `${BASE_URL}${locale === 'en' ? '' : '/' + locale}/careers/${job.route}`,
        lastModified: new Date(job.posted_on || new Date()),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
      }))
    );
  } catch (error) {
    console.error('Error fetching jobs from ERP for sitemap:', error);
  }

  return [...staticRoutes, ...resourceRoutes, ...careerRoutes];
}
