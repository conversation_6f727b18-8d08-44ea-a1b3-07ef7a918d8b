name: Deploy Nexus HRMS

on: 
  push:
    branches: 
      - develop
      - staging
      - add-cicd
      - master
  workflow_dispatch:
    inputs:
      Deploy:
        description: 'Deploy to ECS'
        required: false
        default: true
        type: boolean

permissions:
  id-token: write   # This is required for requesting the JWT
  contents: read    # This is required for actions/checkout

env:
  IMAGE_TAG: latest ##placeholder, shoule be updated

jobs:
  Build:
    name: Build
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'develop' && 'dev' || github.ref_name == 'add-cicd' && 'dev' || github.ref_name == 'staging' && 'staging' || 'prod' }}
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-southeast-1
          role-to-assume:
            ${{ github.ref_name == 'develop' && secrets.AWS_ROLE_TO_ASSUME_DEV || github.ref_name == 'add-cicd' && secrets.AWS_ROLE_TO_ASSUME_DEV || github.ref_name == 'staging' && secrets.AWS_ROLE_TO_ASSUME_STAGING|| secrets.AWS_ROLE_TO_ASSUME_CENTRAL }}

      - name: Checkout
        uses: actions/checkout@v2
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: ECR Login
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Get commit hash
        id: commit_hash
        run: echo "::set-output name=hash::$(git rev-parse --short HEAD)"

      - name: Get timestamp
        id: get-timestamp
        run: echo "::set-output name=timestamp::$(date +'%Y%m%d%H%M')"

      - name: Set IMAGE tag
        id: set-image-tag
        run: |
          echo "IMAGE_TAG=${{ steps.commit_hash.outputs.hash }}-${{ steps.get-timestamp.outputs.timestamp }}-${{ github.run_number }}" >> $GITHUB_ENV

      - name: Build and push
        uses: docker/build-push-action@v4
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ vars.SVC_REPO }}
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }}, ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:latest
          cache-from: type=gha,src=/tmp/.buildx-cache
          cache-to: type=gha,dest=/tmp/.buildx-cache
          provenance: false
          build-args: |
            BRANCH_NAME=${{ github.ref_name }}
            GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}
            NEXT_PUBLIC_ERP_API=${{ vars.NEXT_PUBLIC_ERP_API }}
            NEXT_PUBLIC_WEB_URL=${{ vars.NEXT_PUBLIC_WEB_URL }}
            NEXT_PUBLIC_ASSET_URL=${{ vars.NEXT_PUBLIC_ASSET_URL }}
            NEXT_PUBLIC_GTM_ID=${{ vars.NEXT_PUBLIC_GTM_ID }}
            NEXT_PUBLIC_GA_ID=${{ vars.NEXT_PUBLIC_GA_ID }}
            NEXT_PUBLIC_DIRECTUS_API=${{ vars.NEXT_PUBLIC_DIRECTUS_API }}
            NEXT_PUBLIC_CMS_URL=${{ vars.NEXT_PUBLIC_CMS_URL }}

    outputs:
      image_url: ${{ steps.login-ecr.outputs.registry }}/${{ vars.SVC_REPO}}:${{ env.IMAGE_TAG }}
      cluster_name: ${{ vars.ECS_CLUSTER }}

  Deploy_ECS_Services:
    name: Deploy ECS Services
    if: inputs.Deploy == true || github.event_name == 'push'
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'develop' && 'dev' || github.ref_name == 'add-cicd' && 'dev' || github.ref_name == 'staging' && 'staging' || 'prod' }}
    needs: [Build]
    env:
      IMAGE: ${{ needs.Build.outputs.image_url }}
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-southeast-1
          role-to-assume:
            ${{ github.ref_name == 'develop' && secrets.AWS_ROLE_TO_ASSUME_DEV || github.ref_name == 'add-cicd' && secrets.AWS_ROLE_TO_ASSUME_DEV || github.ref_name == 'staging' && secrets.AWS_ROLE_TO_ASSUME_STAGING|| secrets.AWS_ROLE_TO_ASSUME_CENTRAL }}

      - name: Render Amazon ECS task definition
        id: render-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition-family:  ${{ vars.ECS_SERVICE }}
          container-name: ${{ vars.ECS_SERVICE }}
          image: ${{ env.IMAGE }}

      - name: Deploy to Amazon ECS service
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.render-task-def.outputs.task-definition }}
          cluster: ${{ needs.Build.outputs.cluster_name }}
          service: ${{ vars.ECS_SERVICE }}
          wait-for-service-stability: true

      - name: Verify ECS Service state
        uses: giner/ecs-service-health-action@v1.0.0
        with:
          cluster_name: ${{ needs.Build.outputs.cluster_name }}
          service_name: ${{ vars.ECS_SERVICE }}
          timeout: 60
