name: <PERSON><PERSON> Pull Request

on:
  pull_request:
    branches:
      - develop

jobs:
  test-and-build:
    name: Test and Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'
          cache: 'yarn'

      - name: Enable Corepack and use Yarn 4
        run: |
          corepack enable
          corepack prepare yarn@4.9.1 --activate

      - name: Install dependencies (Yarn 4)
        run: yarn install --immutable

      # Optional: Add back if you use tests
      # - name: Run tests
      #   run: yarn test

      - name: Build project
        run: yarn build
