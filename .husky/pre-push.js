// // eslint-disable-next-line @typescript-eslint/no-require-imports
// const { execSync } = require('child_process');

// try {
//   // console.log('🔍 Running ESLint...');
//   // execSync('yarn lint', { stdio: 'inherit' });

//   console.log('🔍 Running Test...');
//   execSync('yarn test', { stdio: 'inherit' });

//   console.log('🏗️ Running Linter...');
//   execSync('yarn build', { stdio: 'inherit' });

//   console.log('🏗️ Running Next.js build...');
//   execSync('yarn build', { stdio: 'inherit' });

//   console.log('✅ All checks passed. Proceeding with push...');
//   process.exit(0);
// } catch (error) {
//   console.error('❌ Push rejected due to errors.');
//   process.exit(1);
// }
