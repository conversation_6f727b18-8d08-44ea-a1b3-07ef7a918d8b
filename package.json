{"name": "vns-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@directus/sdk": "^20.0.0", "@hookform/resolvers": "^5.1.1", "@next/third-parties": "^15.3.5", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@studio-freight/lenis": "^1.0.42", "@tabler/icons-react": "^3.34.0", "@tiptap/core": "^3.0.7", "@tiptap/extension-blockquote": "^3.0.7", "@tiptap/extension-bold": "^3.0.7", "@tiptap/extension-bullet-list": "^3.0.7", "@tiptap/extension-document": "^3.0.7", "@tiptap/extension-heading": "^3.0.7", "@tiptap/extension-italic": "^3.0.7", "@tiptap/extension-list": "^3.0.7", "@tiptap/extension-list-item": "^3.0.7", "@tiptap/extension-ordered-list": "^3.0.7", "@tiptap/extension-paragraph": "^3.0.7", "@tiptap/extension-strike": "^3.0.7", "@tiptap/extension-text": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "embla-carousel": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.5", "isomorphic-dompurify": "^2.26.0", "lodash-es": "^4.17.21", "moment": "^2.30.1", "next": "15.3.5", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-use-measure": "^2.1.7", "sass": "^1.89.2", "schema-dts": "^1.1.5", "sonner": "^2.0.6", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "ts-pattern": "^5.7.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-plugin-prettier": "^5.5.3", "tailwindcss": "^4", "typescript": "^5"}}