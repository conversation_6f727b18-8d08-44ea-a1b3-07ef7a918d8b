# syntax=docker/dockerfile:1

FROM node:22.14.0-alpine AS builder
WORKDIR /app

ARG NEXT_PUBLIC_DIRECTUS_API
ARG NEXT_PUBLIC_ERP_API
ARG NEXT_PUBLIC_ASSET_URL
ARG NEXT_PUBLIC_WEB_URL
ARG NEXT_PUBLIC_CMS_URL
ARG NEXT_PUBLIC_GTM_ID
ARG NEXT_PUBLIC_GA_ID

ENV NEXT_PUBLIC_DIRECTUS_API=$NEXT_PUBLIC_DIRECTUS_API
ENV NEXT_PUBLIC_ERP_API=$NEXT_PUBLIC_ERP_API
ENV NEXT_PUBLIC_ASSET_URL=$NEXT_PUBLIC_ASSET_URL
ENV NEXT_PUBLIC_WEB_URL=$NEXT_PUBLIC_WEB_URL
ENV NEXT_PUBLIC_CMS_URL=$NEXT_PUBLIC_CMS_URL
ENV NEXT_PUBLIC_GTM_ID=$NEXT_PUBLIC_GTM_ID
ENV NEXT_PUBLIC_GA_ID=$NEXT_PUBLIC_GA_ID

# Copy package files
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install

# Copy source code
COPY . .

# Build application
RUN yarn build

FROM node:22.14.0-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs && \
  adduser --system --uid 1001 nextjs

# Copy build artifacts
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
# Copy messages directory for internationalization
COPY --from=builder --chown=nextjs:nodejs /app/messages ./messages

USER nextjs

ENV PORT=3000
EXPOSE $PORT
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
